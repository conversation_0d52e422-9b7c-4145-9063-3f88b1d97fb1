# 安装指南

## 快速安装

### 1. 安装基础依赖

```bash
pip install -r requirements.txt
```

### 2. 安装热重载依赖（可选）

```bash
pip install watchdog
```

**注意**: 如果不安装 `watchdog`，热重载功能将被禁用，但服务仍可正常运行。

### 3. 创建配置文件

```bash
python -m api_proxy --create-config config.json
```

或手动创建 `config.json`:

```json
{
  "providers": {
    "openai": [
      {
        "name": "primary",
        "api_key": "sk-your-openai-key-here",
        "config": {}
      }
    ],
    "openrouter": [
      {
        "name": "primary",
        "api_key": "sk-or-your-openrouter-key-here",
        "config": {
          "timeout": 30,
          "site_url": "https://yourapp.com",
          "site_name": "Your App Name"
        }
      }
    ]
  },
  "default_timeout": 30,
  "max_retries": 3,
  "health_check_interval": 300,
  "enable_monitoring": true,
  "log_level": "INFO"
}
```

### 4. 启动服务

```bash
# 启动Web管理界面（推荐）
python -m api_proxy --web

# 启动时禁用热重载
python -m api_proxy --web --no-hot-reload

# 运行交互式演示
python -m api_proxy --demo
```

## 依赖说明

### 必需依赖

- `requests`: HTTP请求库
- `fastapi`: Web框架
- `uvicorn`: ASGI服务器
- `httpx`: 异步HTTP客户端

### 可选依赖

- `watchdog`: 文件监控，用于热重载功能
- `jinja2`: 模板引擎，用于Web界面
- `python-multipart`: 文件上传支持

## 故障排除

### 1. watchdog 安装失败

如果 `watchdog` 安装失败，可以跳过此依赖：

```bash
# 启动时禁用热重载
python -m api_proxy --web --no-hot-reload
```

服务将正常运行，但不支持配置文件自动监控。

### 2. 权限问题

在某些系统上可能需要管理员权限：

```bash
# Windows
pip install --user watchdog

# Linux/macOS
sudo pip install watchdog
```

### 3. 网络问题

如果网络连接有问题，可以使用国内镜像：

```bash
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ watchdog
```

## 验证安装

运行以下命令验证安装：

```python
python -c "
try:
    from api_proxy import ProxyService, Config, ProviderConfig
    print('✅ 核心模块导入成功')
except ImportError as e:
    print(f'❌ 核心模块导入失败: {e}')

try:
    import watchdog
    print('✅ watchdog 可用 - 热重载功能启用')
except ImportError:
    print('⚠️  watchdog 不可用 - 热重载功能禁用')
"
```

## 使用说明

### 基本使用

```python
from api_proxy import ProxyService, Config, ProviderConfig

# 创建配置
config = Config({
    "openai": [
        ProviderConfig("primary", "sk-your-key", {})
    ]
})

# 创建服务
service = ProxyService(config)

# 调用API
response = service.call("openai", "chat/completions",
                       model="gpt-3.5-turbo",
                       messages=[{"role": "user", "content": "Hello!"}])
```

### 热重载使用

```python
from api_proxy.hot_reload import HotReloadManager, ServiceReloader

# 创建服务重载器
service_reloader = ServiceReloader(ProxyService)

# 创建热重载管理器
hot_reload_manager = HotReloadManager("config.json")
hot_reload_manager.add_reload_callback(service_reloader.reload_service)

# 启动监控（需要 watchdog）
hot_reload_manager.start_monitoring()

# 手动重载
success = hot_reload_manager.manual_reload()
```

## 获取API密钥

- **OpenAI**: https://platform.openai.com/api-keys
- **OpenRouter**: https://openrouter.ai/keys

## 支持

如果遇到问题，请检查：

1. Python版本是否为3.8+
2. 所有依赖是否正确安装
3. 配置文件格式是否正确
4. API密钥是否有效

更多信息请参考 [README.md](README.md)。
