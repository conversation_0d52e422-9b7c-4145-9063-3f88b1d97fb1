"""代理服务单元测试。

测试ProxyService类的核心功能实现。
"""

import pytest
from unittest.mock import MagicMock, patch
from api_proxy.proxy_service import ProxyService
from api_proxy.config import Config
from api_proxy.models import ProviderConfig
from api_proxy.providers.base import BaseProvider


class TestProxyServiceUnit:
    """代理服务单元测试类"""

    @pytest.fixture
    def mock_provider(self):
        """模拟提供者实例"""
        provider = MagicMock(spec=BaseProvider)
        provider.call.return_value = {"result": "success"}
        return provider

    def test_register_provider_success(self, mock_provider):
        """测试成功注册提供者"""
        # 需要至少一个有效的提供商配置
        config = Config({
            "test": [ProviderConfig("test1", "test-key", {})]
        })
        service = ProxyService(config)
        service.register_provider("test", mock_provider)

        assert "test" in service.providers
        assert mock_provider in service.providers["test"]

    def test_get_provider_success(self, mock_provider):
        """测试成功获取提供者"""
        config = Config({
            "test": [ProviderConfig("test1", "test-key", {})]
        })
        service = ProxyService(config)
        # 清空自动创建的提供者，添加我们的mock
        service.providers["test"] = [mock_provider]

        provider = service.get_provider("test")
        assert provider == mock_provider
