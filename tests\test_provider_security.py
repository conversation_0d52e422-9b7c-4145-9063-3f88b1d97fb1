"""提供者安全测试模块

测试API提供商的安全措施和敏感数据处理
"""

import pytest
from unittest.mock import patch
from api_proxy.providers.openai import OpenAIProvider
from api_proxy.models import ProviderConfig


class TestProviderSecurity:
    """提供者安全测试类"""

    def test_api_key_redaction_in_logs(self):
        """测试日志中API密钥被正确脱敏"""
        provider = OpenAIProvider("sk-test1234567890abcdef", timeout=10)
        log_message = str(provider)
        assert "sk-test1234567890abcdef" not in log_message
        assert "sk-test1***" in log_message

    def test_empty_api_key_handling(self):
        """测试空API密钥处理"""
        with pytest.raises(ValueError):
            OpenAIProvider("", timeout=10)

        with pytest.raises(ValueError):
            OpenAIProvider("   ", timeout=10)

    @patch('requests.post')
    def test_request_headers_contain_key(self, mock_post):
        """测试请求头中包含正确格式的API密钥"""
        mock_response = MagicMock()
        mock_response.json.return_value = {"result": "success"}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response

        provider = OpenAIProvider("sk-test123456789012345678901234567890123456789012", timeout=10)
        provider.call("completions", prompt="test")

        # 检查调用参数
        assert mock_post.called
        call_args = mock_post.call_args
        headers = call_args[1]['headers']
        assert headers['Authorization'].startswith('Bearer sk-test123')
