# AI 代理服务架构升级规划

## 📋 项目现状分析

### 当前架构特点
- **核心设计**: 基于 `BaseProvider` 抽象类的提供商模式
- **支持的功能**: 文本对话补全 (Chat Completions)
- **已有提供商**: OpenAI、OpenRouter
- **关键特性**: 负载均衡、故障转移、健康检查、监控统计

### 现有代码结构
```
api_proxy/
├── providers/
│   ├── base.py          # 基础提供商接口 (抽象类)
│   ├── openai.py        # OpenAI 实现
│   └── openrouter.py    # OpenRouter 实现
├── proxy_service.py     # 核心代理服务
├── config.py            # 配置管理
├── models.py            # 数据模型
├── web_app.py           # Web 管理界面
└── auth_manager.py      # 认证权限管理
```

---

## 🎯 升级目标

支持三大类 API 调用，保持统一的架构设计：
1. **文本对话** (Chat Completions) - 现有功能
2. **文生图** (Image Generation) - 新增功能
3. **文生视频** (Video Generation) - 新增功能

---

## 🏗️ 改造方案（不改变核心架构）

### 方案核心思想
**保持 `BaseProvider` 抽象类设计不变，通过端点路由和请求类型识别来支持多种 API 类型**

### 1. 扩展 BaseProvider 接口

**修改位置**: `api_proxy/providers/base.py`

```python
class BaseProvider(ABC):
    """扩展后的基础提供商接口"""
    
    # 现有方法保持不变
    @abstractmethod
    def call(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """统一调用接口"""
        pass
    
    # 新增：支持的功能类型声明
    @property
    def supported_features(self) -> List[str]:
        """返回支持的功能列表
        
        返回值示例:
        - ['chat', 'image', 'video']
        - ['chat', 'image']
        """
        return ['chat']  # 默认仅支持文本对话
    
    # 新增：获取功能配置
    def get_feature_config(self, feature: str) -> Dict[str, Any]:
        """获取特定功能的配置信息"""
        return {}
```

### 2. 创建功能类型枚举和数据模型

**新建文件**: `api_proxy/models.py` (扩展)

```python
from enum import Enum

class APIFeatureType(Enum):
    """API 功能类型"""
    CHAT = "chat"              # 文本对话
    IMAGE = "image"            # 文生图
    VIDEO = "video"            # 文生视频

class RequestType(Enum):
    """请求类型"""
    CHAT_COMPLETION = "chat/completions"
    IMAGE_GENERATION = "images/generations"
    VIDEO_GENERATION = "videos/generations"
```

### 3. 扩展现有提供商实现

**修改位置**: `api_proxy/providers/openai.py`

```python
class OpenAIProvider(BaseProvider):
    @property
    def supported_features(self) -> List[str]:
        return ['chat', 'image']  # OpenAI 支持文本和图像
    
    def call(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """统一调用接口，自动识别功能类型"""
        # 现有逻辑保持不变
        # 新增：根据 endpoint 自动路由
        if 'image' in endpoint:
            return self._call_image_api(endpoint, **kwargs)
        elif 'video' in endpoint:
            return self._call_video_api(endpoint, **kwargs)
        else:
            return self._call_chat_api(endpoint, **kwargs)
    
    def _call_chat_api(self, endpoint: str, **kwargs):
        """现有的聊天 API 调用逻辑"""
        # 保持原有实现
        pass
    
    def _call_image_api(self, endpoint: str, **kwargs):
        """新增：图像生成 API 调用"""
        # 调用 OpenAI DALL-E API
        pass
    
    def _call_video_api(self, endpoint: str, **kwargs):
        """新增：视频生成 API 调用"""
        # 如果 OpenAI 支持，调用相应 API
        pass
```

### 4. 新增专用提供商

**新建文件**: `api_proxy/providers/image_providers.py`

```python
# 支持文生图的专用提供商
class StabilityAIProvider(BaseProvider):
    """Stability AI 文生图提供商"""
    supported_features = ['image']

class MidjourneyProvider(BaseProvider):
    """Midjourney 文生图提供商"""
    supported_features = ['image']
```

**新建文件**: `api_proxy/providers/video_providers.py`

```python
# 支持文生视频的专用提供商
class RunwayMLProvider(BaseProvider):
    """Runway ML 文生视频提供商"""
    supported_features = ['video']

class HeyGenProvider(BaseProvider):
    """HeyGen 文生视频提供商"""
    supported_features = ['video']
```

### 5. 扩展 ProxyService

**修改位置**: `api_proxy/proxy_service.py`

```python
class ProxyService:
    def _create_provider(self, provider_type: str, config: ProviderConfig):
        """扩展提供商创建逻辑"""
        providers_map = {
            "openai": OpenAIProvider,
            "openrouter": OpenRouterProvider,
            # 新增提供商
            "stability_ai": StabilityAIProvider,
            "midjourney": MidjourneyProvider,
            "runway_ml": RunwayMLProvider,
            "heygen": HeyGenProvider,
        }
        # 保持原有逻辑
    
    def call(self, provider_type: str, endpoint: str, **kwargs):
        """现有调用逻辑保持不变"""
        # 自动识别功能类型并路由
        pass
    
    def get_available_features(self, provider_type: str) -> List[str]:
        """新增：获取提供商支持的功能"""
        providers = self.providers.get(provider_type, [])
        if providers:
            return providers[0].supported_features
        return []
```

### 6. 扩展 Web API 接口

**修改位置**: `api_proxy/web_app.py`

```python
# 新增请求模型
class ImageGenerationRequest(BaseModel):
    """文生图请求"""
    prompt: str
    size: Optional[str] = "1024x1024"
    quality: Optional[str] = "standard"
    n: Optional[int] = 1

class VideoGenerationRequest(BaseModel):
    """文生视频请求"""
    prompt: str
    duration: Optional[int] = 10
    fps: Optional[int] = 24

# 新增 API 端点
@app.post("/v1/images/generations")
async def generate_image(request: ImageGenerationRequest):
    """文生图 API"""
    pass

@app.post("/v1/videos/generations")
async def generate_video(request: VideoGenerationRequest):
    """文生视频 API"""
    pass
```

### 7. 配置文件扩展

**修改位置**: `config.example.json`

```json
{
  "providers": {
    "openai": [...],
    "openrouter": [...],
    "stability_ai": [
      {
        "name": "primary",
        "api_key": "sk-stability-xxx",
        "config": {"engine_id": "stable-diffusion-xl-1024-v1-0"}
      }
    ],
    "runway_ml": [
      {
        "name": "primary",
        "api_key": "runway-api-key",
        "config": {"model": "gen3"}
      }
    ]
  }
}
```

---

## 📊 改造优势

| 方面 | 优势 |
|------|------|
| **架构一致性** | 保持 BaseProvider 设计，所有提供商统一接口 |
| **扩展性** | 新增提供商只需继承 BaseProvider 并实现 call() |
| **兼容性** | 现有代码无需修改，完全向后兼容 |
| **功能识别** | 通过 endpoint 和 supported_features 自动识别 |
| **负载均衡** | 现有的轮询机制对所有功能类型适用 |
| **监控统计** | 现有的监控系统自动支持新功能 |

---

## 🔄 实施步骤

### Phase 1: 基础设施 (1-2 天)
- [ ] 扩展 BaseProvider 接口
- [ ] 添加功能类型枚举
- [ ] 更新 ProxyService 创建逻辑

### Phase 2: 文生图支持 (2-3 天)
- [ ] 实现 OpenAI DALL-E 支持
- [ ] 新增 Stability AI 提供商
- [ ] 添加 Web API 端点
- [ ] 编写测试用例

### Phase 3: 文生视频支持 (2-3 天)
- [ ] 实现 Runway ML 提供商
- [ ] 实现 HeyGen 提供商
- [ ] 添加 Web API 端点
- [ ] 编写测试用例

### Phase 4: 集成和优化 (1-2 天)
- [ ] 更新配置示例
- [ ] 更新文档
- [ ] 性能测试
- [ ] 安全审计

---

## 🔐 安全考虑

- 敏感信息（API 密钥）加密存储
- 请求参数验证和清理
- 速率限制和配额管理
- 审计日志记录

---

## 📝 文档更新

- [ ] API_USAGE.md - 添加文生图和文生视频示例
- [ ] README.md - 更新功能列表
- [ ] 新增 PROVIDERS.md - 提供商配置指南
- [ ] 新增 FEATURES.md - 功能详细说明

