# GitLab CI/CD 配置文件
# AI代理服务自动化部署流程

# 定义阶段
stages:
  - validate      # 代码验证
  - test         # 测试阶段
  - security     # 安全检查
  - build        # 构建阶段
  - package      # 打包阶段
  - deploy       # 部署阶段
  - notify       # 通知阶段

# 全局变量
variables:
  # Python配置
  PIP_CACHE_DIR: "$CI_PROJECT_DIR/.cache/pip"
  PYTHONUNBUFFERED: "1"
  PYTHONPATH: "$CI_PROJECT_DIR"

  # Docker配置
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  DOCKER_BUILDKIT: "1"

  # 应用配置
  APP_NAME: "ai-proxy"
  APP_VERSION: "${CI_COMMIT_TAG:-${CI_COMMIT_SHORT_SHA}}"

  # 部署配置
  STAGING_HOST: "${STAGING_HOST}"
  PRODUCTION_HOST: "${PRODUCTION_HOST}"
  REGISTRY_URL: "${CI_REGISTRY}"

# 缓存配置
cache:
  key: "${CI_COMMIT_REF_SLUG}"
  paths:
    - .cache/pip
    - venv/
    - node_modules/
    - .pytest_cache/

# 通用脚本
.install_dependencies: &install_dependencies
  - python -m venv venv
  - source venv/bin/activate
  - pip install --upgrade pip setuptools wheel
  - pip install -r requirements.txt
  - pip install -r requirements-dev.txt

.docker_login: &docker_login
  - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY

# ==================== 验证阶段 ====================

# 代码格式检查
format_check:
  stage: validate
  image: python:3.9-slim
  before_script:
    - pip install black==23.12.1 isort==5.13.2
  script:
    - black --check --config pyproject.toml .
    - isort --check-only --profile black .
  allow_failure: false
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH


# 依赖安全检查
dependency_check:
  stage: validate
  image: python:3.9-slim
  before_script:
    - pip install safety==2.3.5
  script:
    - safety check --output json > safety-report.json || true
    - safety check  # 失败时退出
  artifacts:
    paths:
      - safety-report.json
    expire_in: 1 week
  allow_failure: false

# ==================== 测试阶段 ====================

# 单元测试
unit_tests:
  stage: test
  image: python:3.9-slim
  before_script:
    - *install_dependencies
    - source venv/bin/activate
  script:
    - pytest tests/test_*_unit.py -v --tb=short --junitxml=unit-test-report.xml --cov=api_proxy --cov-report=xml --cov-report=html
  coverage: '/TOTAL.*\s+(\d+%)$/'
  artifacts:
    reports:
      junit: unit-test-report.xml
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
    paths:
      - htmlcov/
      - coverage.xml
    expire_in: 1 week

# 集成测试
integration_tests:
  stage: test
  image: python:3.9-slim
  services:
    - redis:7-alpine
  variables:
    REDIS_URL: "redis://redis:6379/0"
  before_script:
    - *install_dependencies
    - source venv/bin/activate
  script:
    - pytest tests/test_*_integration.py -v --tb=short --junitxml=integration-test-report.xml
  artifacts:
    reports:
      junit: integration-test-report.xml
    expire_in: 1 week
  allow_failure: false

# 边界测试
boundary_tests:
  stage: test
  image: python:3.9-slim
  before_script:
    - *install_dependencies
    - source venv/bin/activate
  script:
    - pytest tests/test_*_boundary.py -v --tb=short --junitxml=boundary-test-report.xml
  artifacts:
    reports:
      junit: boundary-test-report.xml
    expire_in: 1 week

# 错误处理测试
error_tests:
  stage: test
  image: python:3.9-slim
  before_script:
    - *install_dependencies
    - source venv/bin/activate
  script:
    - pytest tests/test_*_error.py -v --tb=short --junitxml=error-test-report.xml
  artifacts:
    reports:
      junit: error-test-report.xml
    expire_in: 1 week

# ==================== 安全检查阶段 ====================

# 容器安全扫描
container_scan:
  stage: security
  image: docker:20.10.16
  services:
    - docker:20.10.16-dind
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
  before_script:
    - *docker_login
  script:
    - docker build -t $CI_REGISTRY_IMAGE:$APP_VERSION .
    - docker run --rm -v /var/run/docker.sock:/var/run/docker.sock
      -v $PWD:/tmp aquasec/trivy:latest image --exit-code 0 --no-progress
      --format json -o /tmp/trivy-report.json $CI_REGISTRY_IMAGE:$APP_VERSION
  artifacts:
    paths:
      - trivy-report.json
    expire_in: 1 week
  allow_failure: true
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_TAG

# SAST 静态代码分析
sast:
  stage: security
  image: python:3.9-slim
  before_script:
    - pip install semgrep==1.45.0
  script:
    - semgrep --config=auto --json --output=semgrep-report.json api_proxy/ || true
    - semgrep --config=auto api_proxy/  # 失败时退出
  artifacts:
    paths:
      - semgrep-report.json
    expire_in: 1 week
  allow_failure: true

# ==================== 构建阶段 ====================

# Python包构建
build_package:
  stage: build
  image: python:3.9-slim
  before_script:
    - pip install build==1.0.3 twine==4.0.2
  script:
    - python -m build
    - twine check dist/*
  artifacts:
    paths:
      - dist/
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_TAG

# Docker镜像构建
build_docker:
  stage: build
  image: docker:20.10.16
  services:
    - docker:20.10.16-dind
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
  before_script:
    - *docker_login
  script:
    - docker build --build-arg VERSION=$APP_VERSION -t $CI_REGISTRY_IMAGE:$APP_VERSION .
    - docker tag $CI_REGISTRY_IMAGE:$APP_VERSION $CI_REGISTRY_IMAGE:latest
    - docker push $CI_REGISTRY_IMAGE:$APP_VERSION
    - docker push $CI_REGISTRY_IMAGE:latest
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_TAG

# ==================== 打包阶段 ====================

# 创建发布包
package_release:
  stage: package
  image: alpine:latest
  before_script:
    - apk add --no-cache tar gzip
  script:
    - mkdir -p release
    - |
      tar -czf release/ai-proxy-$APP_VERSION.tar.gz \
        --exclude='.git*' --exclude='__pycache__' --exclude='*.pyc' \
        --exclude='venv' --exclude='.cache' .
    - echo "AI代理服务 v$APP_VERSION" > release/RELEASE_NOTES.md
    - echo "构建时间 $(date)" >> release/RELEASE_NOTES.md
    - echo "提交哈希 $CI_COMMIT_SHA" >> release/RELEASE_NOTES.md
  artifacts:
    paths:
      - release/
    expire_in: 1 month
  rules:
    - if: $CI_COMMIT_TAG

# ==================== 部署阶段 ====================

# 部署到测试环境
deploy_staging:
  stage: deploy
  image: alpine:latest
  environment:
    name: staging
    url: http://$STAGING_HOST:8080
  before_script:
    - apk add --no-cache openssh-client curl
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan $STAGING_HOST >> ~/.ssh/known_hosts
  script:
    - ssh deploy@$STAGING_HOST "
        cd /opt/ai-proxy &&
        docker-compose pull &&
        docker-compose up -d &&
        sleep 10 &&
        curl -f http://localhost:8080/health || exit 1
      "
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  when: manual

# 部署到生产环境
deploy_production:
  stage: deploy
  image: alpine:latest
  environment:
    name: production
    url: http://$PRODUCTION_HOST:8080
  before_script:
    - apk add --no-cache openssh-client curl
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan $PRODUCTION_HOST >> ~/.ssh/known_hosts
  script:
    - ssh deploy@$PRODUCTION_HOST "
        cd /opt/ai-proxy &&
        docker-compose pull &&
        docker-compose up -d &&
        sleep 15 &&
        curl -f http://localhost:8080/health || exit 1
      "
  rules:
    - if: $CI_COMMIT_TAG
  when: manual
  allow_failure: false

# ==================== 通知阶段 ====================

# 部署成功通知
notify_success:
  stage: notify
  image: alpine:latest
  before_script:
    - apk add --no-cache curl
  script:
    - |
      if [ "$CI_COMMIT_TAG" ]; then
        MESSAGE="🚀 AI代理服务 $CI_COMMIT_TAG 已成功部署到生产环境"
      else
        MESSAGE="✅ AI代理服务已成功部署到测试环境 (${CI_COMMIT_SHORT_SHA})"
      fi

      # 发送到企业微信/钉钉/Slack等
      if [ "$WEBHOOK_URL" ]; then
        curl -X POST "$WEBHOOK_URL" \
          -H "Content-Type: application/json" \
          -d "{\"text\": \"$MESSAGE\"}"
      fi
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: on_success
    - if: $CI_COMMIT_TAG
      when: on_success
  allow_failure: true

# 部署失败通知
notify_failure:
  stage: notify
  image: alpine:latest
  before_script:
    - apk add --no-cache curl
  script:
    - |
      MESSAGE="❌ AI代理服务部署失败 - 流水线: $CI_PIPELINE_URL"

      if [ "$WEBHOOK_URL" ]; then
        curl -X POST "$WEBHOOK_URL" \
          -H "Content-Type: application/json" \
          -d "{\"text\": \"$MESSAGE\"}"
      fi
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: on_failure
    - if: $CI_COMMIT_TAG
      when: on_failure
  allow_failure: true
