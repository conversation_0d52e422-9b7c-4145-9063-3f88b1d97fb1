#!/usr/bin/env python3
"""
Web界面功能测试脚本

测试新添加的API信息页面和认证管理功能
"""

import requests
import json
import time
from urllib.parse import urljoin


class WebInterfaceTest:
    """Web界面测试类"""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
    
    def test_page_access(self):
        """测试页面访问"""
        pages = [
            ("/", "仪表板"),
            ("/config", "配置管理"),
            ("/api-info", "API信息"),
            ("/admin/auth", "认证管理"),
        ]
        
        print("🌐 测试页面访问...")
        for path, name in pages:
            try:
                url = urljoin(self.base_url, path)
                response = self.session.get(url)
                
                if response.status_code == 200:
                    print(f"✅ {name} ({path}): 访问成功")
                else:
                    print(f"❌ {name} ({path}): HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"❌ {name} ({path}): 连接失败 - {e}")
    
    def test_api_endpoints(self):
        """测试API端点"""
        endpoints = [
            ("/api/health", "健康检查"),
            ("/api/service/status", "服务状态"),
            ("/api/reload/status", "热重载状态"),
            ("/api/auth/keys", "API密钥列表"),
            ("/api/auth/models", "模型映射列表"),
            ("/v1/models", "模型列表"),
        ]
        
        print("\n🔌 测试API端点...")
        for path, name in endpoints:
            try:
                url = urljoin(self.base_url, path)
                response = self.session.get(url)
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"✅ {name} ({path}): 返回数据正常")
                    except json.JSONDecodeError:
                        print(f"⚠️  {name} ({path}): 响应不是JSON格式")
                elif response.status_code == 401:
                    print(f"🔒 {name} ({path}): 需要认证 (正常)")
                else:
                    print(f"❌ {name} ({path}): HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"❌ {name} ({path}): 请求失败 - {e}")
    
    def test_api_info_page(self):
        """测试API信息页面内容"""
        print("\n📋 测试API信息页面...")
        
        try:
            url = urljoin(self.base_url, "/api-info")
            response = self.session.get(url)
            
            if response.status_code == 200:
                content = response.text
                
                # 检查关键内容
                checks = [
                    ("服务地址", "base_url" in content),
                    ("可用模型", "available_models" in content),
                    ("使用示例", "curl" in content.lower()),
                    ("Python示例", "python" in content.lower()),
                    ("JavaScript示例", "javascript" in content.lower()),
                ]
                
                for check_name, condition in checks:
                    if condition:
                        print(f"✅ {check_name}: 内容存在")
                    else:
                        print(f"❌ {check_name}: 内容缺失")
            else:
                print(f"❌ API信息页面访问失败: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ API信息页面测试失败: {e}")
    
    def test_auth_management(self):
        """测试认证管理功能"""
        print("\n🔑 测试认证管理...")
        
        try:
            # 测试获取API密钥列表
            url = urljoin(self.base_url, "/api/auth/keys")
            response = self.session.get(url)
            
            if response.status_code == 200:
                data = response.json()
                if "keys" in data:
                    print(f"✅ API密钥列表: 找到 {len(data['keys'])} 个密钥")
                else:
                    print("❌ API密钥列表: 响应格式错误")
            else:
                print(f"❌ API密钥列表: HTTP {response.status_code}")
            
            # 测试获取模型映射列表
            url = urljoin(self.base_url, "/api/auth/models")
            response = self.session.get(url)
            
            if response.status_code == 200:
                data = response.json()
                if "mappings" in data:
                    print(f"✅ 模型映射列表: 找到 {len(data['mappings'])} 个映射")
                else:
                    print("❌ 模型映射列表: 响应格式错误")
            else:
                print(f"❌ 模型映射列表: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ 认证管理测试失败: {e}")
    
    def test_model_list(self):
        """测试模型列表"""
        print("\n🧠 测试模型列表...")
        
        try:
            url = urljoin(self.base_url, "/v1/models")
            response = self.session.get(url)
            
            if response.status_code == 200:
                data = response.json()
                if "data" in data and isinstance(data["data"], list):
                    models = data["data"]
                    print(f"✅ 模型列表: 找到 {len(models)} 个模型")
                    
                    # 显示前几个模型
                    for i, model in enumerate(models[:3]):
                        print(f"   {i+1}. {model.get('id', 'Unknown')} ({model.get('provider', 'Unknown')})")
                    
                    if len(models) > 3:
                        print(f"   ... 还有 {len(models) - 3} 个模型")
                else:
                    print("❌ 模型列表: 响应格式错误")
            elif response.status_code == 401:
                print("🔒 模型列表: 需要API密钥认证")
            else:
                print(f"❌ 模型列表: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ 模型列表测试失败: {e}")
    
    def test_service_info(self):
        """测试服务信息"""
        print("\n📊 测试服务信息...")
        
        try:
            # 测试健康检查
            url = urljoin(self.base_url, "/api/health")
            response = self.session.get(url)
            
            if response.status_code == 200:
                data = response.json()
                status = data.get("status", "unknown")
                print(f"✅ 健康检查: {status}")
                
                if "healthy_providers" in data:
                    providers = data["healthy_providers"]
                    print(f"✅ 健康提供商: {providers}")
            else:
                print(f"❌ 健康检查: HTTP {response.status_code}")
            
            # 测试服务状态
            url = urljoin(self.base_url, "/api/service/status")
            response = self.session.get(url)
            
            if response.status_code == 200:
                data = response.json()
                if "config" in data:
                    config = data["config"]
                    print(f"✅ 服务配置: {config.get('total_providers', 0)} 个提供商")
                    print(f"✅ 监控状态: {'启用' if config.get('monitoring_enabled') else '禁用'}")
            else:
                print(f"❌ 服务状态: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ 服务信息测试失败: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始Web界面功能测试")
        print("=" * 50)
        print(f"🎯 测试目标: {self.base_url}")
        
        self.test_page_access()
        self.test_api_endpoints()
        self.test_api_info_page()
        self.test_auth_management()
        self.test_model_list()
        self.test_service_info()
        
        print("\n" + "=" * 50)
        print("🎉 测试完成！")
        
        print("\n💡 使用提示:")
        print(f"  - 仪表板: {self.base_url}/")
        print(f"  - API信息: {self.base_url}/api-info")
        print(f"  - 认证管理: {self.base_url}/admin/auth")
        print(f"  - API文档: {self.base_url}/docs")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Web界面功能测试")
    parser.add_argument("--url", default="http://localhost:8000", help="服务地址")
    
    args = parser.parse_args()
    
    tester = WebInterfaceTest(args.url)
    tester.run_all_tests()


if __name__ == "__main__":
    main()
