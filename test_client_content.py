#!/usr/bin/env python3
"""
测试客户端是否能正确接收内容
"""

import requests
import json

def test_simple_non_stream():
    """简单测试非流式响应"""
    print("🧪 测试非流式响应内容接收...")
    
    data = {
        "model": "deepseek/deepseek-chat-v3-0324:free",
        "messages": [{"role": "user", "content": "请回复：测试成功"}],
        "max_tokens": 50,
        "stream": False
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            headers={"Content-Type": "application/json", "X-Provider": "openrouter"},
            json=data,
            timeout=20
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            # 检查响应结构
            if 'choices' in result and len(result['choices']) > 0:
                choice = result['choices'][0]
                if 'message' in choice and 'content' in choice['message']:
                    content = choice['message']['content']
                    print(f"✅ 接收到内容: '{content}'")
                    return True
                else:
                    print("❌ 响应结构错误，没有message.content")
                    print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            else:
                print("❌ 响应结构错误，没有choices")
                print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        else:
            print(f"❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 异常: {e}")
    
    return False

def test_simple_stream():
    """简单测试流式响应"""
    print("\n🧪 测试流式响应内容接收...")
    
    data = {
        "model": "deepseek/deepseek-chat-v3-0324:free",
        "messages": [{"role": "user", "content": "请回复：流式测试成功"}],
        "max_tokens": 50,
        "stream": True
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            headers={"Content-Type": "application/json", "X-Provider": "openrouter"},
            json=data,
            timeout=20,
            stream=True
        )
        
        print(f"状态码: {response.status_code}")
        print(f"Content-Type: {response.headers.get('Content-Type')}")
        
        if response.status_code == 200:
            content_parts = []
            line_count = 0
            
            for line in response.iter_lines(decode_unicode=True):
                if line:
                    line_count += 1
                    print(f"第{line_count}行: {line}")
                    
                    if line.startswith('data: '):
                        data_str = line[6:].strip()
                        if data_str != '[DONE]':
                            try:
                                data_obj = json.loads(data_str)
                                if 'choices' in data_obj and len(data_obj['choices']) > 0:
                                    choice = data_obj['choices'][0]
                                    if 'delta' in choice and 'content' in choice['delta']:
                                        content = choice['delta']['content']
                                        if content:
                                            content_parts.append(content)
                                            print(f"  📝 内容片段: '{content}'")
                            except json.JSONDecodeError:
                                pass
                    
                    if line_count >= 15:  # 限制显示
                        print("  ... (继续接收)")
                        break
            
            full_content = ''.join(content_parts)
            print(f"✅ 完整内容: '{full_content}'")
            
            if full_content.strip():
                print("✅ 流式内容接收成功")
                return True
            else:
                print("❌ 没有接收到任何内容")
        else:
            print(f"❌ 流式请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 流式异常: {e}")
    
    return False

if __name__ == "__main__":
    print("🔍 测试客户端内容接收")
    print("=" * 40)
    
    # 测试非流式
    non_stream_ok = test_simple_non_stream()
    
    # 测试流式
    stream_ok = test_simple_stream()
    
    print("\n" + "=" * 40)
    print("📊 测试结果:")
    print(f"  非流式: {'✅ 成功' if non_stream_ok else '❌ 失败'}")
    print(f"  流式: {'✅ 成功' if stream_ok else '❌ 失败'}")
    
    if non_stream_ok and stream_ok:
        print("\n🎉 客户端能正确接收内容！")
    else:
        print("\n⚠️  客户端接收内容有问题，需要进一步调试")
        print("\n💡 建议:")
        print("1. 检查服务日志中的详细信息")
        print("2. 确认OpenRouter API返回了正确的数据")
        print("3. 检查响应转发逻辑")
