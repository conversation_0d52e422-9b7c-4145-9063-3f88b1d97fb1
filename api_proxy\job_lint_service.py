"""Lint 作业分析服务

提供专业的 lint 作业失败分析和修复建议
"""

import re
from datetime import datetime
from dataclasses import dataclass
from enum import Enum, auto
from typing import List, Optional, Dict


class LintErrorType(Enum):
    """Lint 错误类型枚举"""

    BLACK_FORMATTING = auto()  # Black 格式化问题
    BLACK_FAILED = auto()  # Black 处理失败
    FLAKE8_SYNTAX = auto()  # Flake8 语法问题
    FLAKE8_STYLE = auto()  # Flake8 风格问题  
    FLAKE8_CONFIG = auto()  # Flake8 配置问题
    ISORT_ISSUE = auto()  # isort 导入顺序问题
    MYPY_ERROR = auto()  # mypy 类型检查错误


@dataclass
class LintFailure:
    """Lint 失败分析结果"""

    error_type: LintErrorType
    file_path: str
    line_no: Optional[int] = None
    message: Optional[str] = None
    solution: Optional[str] = None
    timestamp: datetime = datetime.now()


class LintAnalyzer:
    """Lint 日志分析器

    功能:
    - 解析常见 lint 工具(black/flake8/isort/mypy)的错误输出
    - 提供针对性的修复建议
    - 错误分类统计
    """

    ERROR_PATTERNS = [
        (LintErrorType.BLACK_FORMATTING, r"would reformat ([\w\/\.\-]+)"),
        (LintErrorType.BLACK_FAILED, r"oh +no!.* (\d+) files? would be reformatted"),
        (LintErrorType.FLAKE8_SYNTAX, r"([\w\/\.\-]+):(\d+):\d+: (E\d+)\s*(.+)"),
        (LintErrorType.FLAKE8_STYLE, r"([\w\/\.\-]+):(\d+):\d+: (W\d+)\s*(.+)"),
        (LintErrorType.FLAKE8_CONFIG, r"([\w\/\.\-]+):(\d+):\d+: (F\d+)\s*(.+)"),
        (LintErrorType.ISORT_ISSUE, r"ERROR: ([\w\/\.\-]+) Imports are incorrectly sorted"),
        (LintErrorType.MYPY_ERROR, r"([\w\/\.\-]+):(\d+): error: (.+)"),
    ]

    def analyze_log(self, log_text: str) -> List[LintFailure]:
        """分析 Lint 日志

        Args:
            log_text: lint 日志内容

        Returns:
            List[LintFailure]: 分析结果列表
        """
        results = []
        for line in log_text.splitlines():
            for error_type, pattern in self.ERROR_PATTERNS:
                match = re.search(pattern, line.strip())
                if match:
                    failure = self._create_failure(error_type, match)
                    results.append(failure)
                    break
        return results

    def _create_failure(
        self, error_type: LintErrorType, match: re.Match
    ) -> LintFailure:
        """根据匹配结果创建 LintFailure 对象"""
        file_path = match.group(1) if match.groups() else "未知文件"
        line_no = int(match.group(2)) if len(match.groups()) > 1 else None
        message = match.group(3) if len(match.groups()) > 2 else match.group(0)

        return LintFailure(
            error_type=error_type,
            file_path=file_path,
            line_no=line_no,
            message=message,
            solution=self._get_solution(error_type, file_path),
        )

    def _get_solution(self, error_type: LintErrorType, file_path: str) -> str:
        """获取针对错误的解决方案"""
        solutions = {
            LintErrorType.BLACK_FORMATTING: f"运行 black 修复格式: black {file_path}",
            LintErrorType.BLACK_FAILED: "需要手动修复无法自动格式化的文件",
            LintErrorType.FLAKE8_SYNTAX: f"按照 Flake8 规范修复 {file_path}",
            LintErrorType.ISORT_ISSUE: f"运行 isort 修复导入顺序: isort {file_path}",
            LintErrorType.MYPY_ERROR: f"修复 {file_path} 中的类型注解问题",
        }
        return solutions.get(error_type, "请检查日志获取详细信息")
