#!/usr/bin/env python3
"""
OpenRouter配置测试脚本
用于验证OpenRouter API密钥是否有效
"""

import json
import requests
import sys
from typing import Dict, Any


def load_config(config_path: str = "config.json") -> Dict[str, Any]:
    """加载配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 配置文件 {config_path} 不存在")
        return {}
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件格式错误: {e}")
        return {}


def test_openrouter_api(api_key: str, site_url: str = "", site_name: str = "") -> bool:
    """测试OpenRouter API密钥是否有效"""
    if not api_key or api_key in ["sk-or-your-openrouter-key-here", "123", "YOUR_ACTUAL_OPENROUTER_API_KEY_HERE"]:
        print("❌ API密钥无效或为占位符")
        return False
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
    }
    
    # 添加可选的应用归属标识头部
    if site_url:
        headers["HTTP-Referer"] = site_url
    if site_name:
        headers["X-Title"] = site_name
    
    # 测试请求 - 获取可用模型列表
    test_data = {
        "model": "openai/gpt-3.5-turbo",
        "messages": [{"role": "user", "content": "Hello"}],
        "max_tokens": 5
    }
    
    try:
        print(f"🔍 测试API密钥: {api_key[:12]}...")
        response = requests.post(
            "https://openrouter.ai/api/v1/chat/completions",
            headers=headers,
            json=test_data,
            timeout=30
        )
        
        if response.status_code == 200:
            print("✅ API密钥有效，连接成功")
            return True
        elif response.status_code == 401:
            print("❌ API密钥无效 (401 Unauthorized)")
            print(f"响应内容: {response.text}")
            return False
        else:
            print(f"⚠️  API调用返回状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except requests.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 OpenRouter配置测试工具")
    print("=" * 50)
    
    # 加载配置
    config = load_config()
    if not config:
        sys.exit(1)
    
    # 检查OpenRouter配置
    providers = config.get("providers", {})
    openrouter_configs = providers.get("openrouter", [])
    
    if not openrouter_configs:
        print("❌ 配置文件中没有找到OpenRouter配置")
        sys.exit(1)
    
    print(f"📋 找到 {len(openrouter_configs)} 个OpenRouter配置")
    
    all_valid = True
    for i, provider_config in enumerate(openrouter_configs):
        print(f"\n🔧 测试配置 #{i+1}: {provider_config.get('name', 'unnamed')}")
        
        api_key = provider_config.get("api_key", "")
        config_data = provider_config.get("config", {})
        site_url = config_data.get("site_url", "")
        site_name = config_data.get("site_name", "")
        
        is_valid = test_openrouter_api(api_key, site_url, site_name)
        if not is_valid:
            all_valid = False
    
    print("\n" + "=" * 50)
    if all_valid:
        print("✅ 所有OpenRouter配置都有效")
        print("🎉 你的AI代理服务应该可以正常工作了")
    else:
        print("❌ 部分或全部OpenRouter配置无效")
        print("📝 请检查并更新你的API密钥")
        print("\n💡 获取OpenRouter API密钥的步骤:")
        print("1. 访问 https://openrouter.ai/")
        print("2. 注册账户并登录")
        print("3. 在设置页面生成API密钥")
        print("4. 将API密钥更新到config.json文件中")


if __name__ == "__main__":
    main()
