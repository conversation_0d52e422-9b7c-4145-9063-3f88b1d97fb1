{"decisions": [{"title": "选择FastAPI作为Web框架", "description": "经过对比Django、Flask和FastAPI，最终选择FastAPI", "rationale": "FastAPI提供自动API文档、类型检查和高性能", "alternatives": ["Django REST Framework", "Flask + Flask-RESTful"], "impact": "影响整个API层的实现方式", "decision_date": "2025-01-01", "id": 1, "recorded_at": "2025-05-27T15:23:37.087411"}, {"title": "使用多提供商轮换策略", "description": "实现多个AI服务提供商的自动轮换机制", "rationale": "提高服务可用性和负载分散", "implementation": "使用策略模式 + 轮询算法", "impact": "核心业务逻辑设计", "id": 2, "recorded_at": "2025-05-27T15:23:37.097266"}]}