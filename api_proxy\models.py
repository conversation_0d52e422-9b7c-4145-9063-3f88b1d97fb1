# 数据模型
from dataclasses import dataclass
from typing import Dict, Any


@dataclass(frozen=True)
class ProviderConfig:
    """API提供商配置

    Attributes:
        name (str): 提供商名称(如'openai')
        api_key (str): API密钥(建议使用环境变量或加密存储)
        config (Dict[str, Any]): 其他配置参数

    Raises:
        ValueError: 如果输入参数无效
    """

    name: str
    api_key: str
    config: Dict[str, Any]

    def __post_init__(self):
        """后初始化验证"""
        if not isinstance(self.name, str) or not self.name.strip():
            raise ValueError("Provider name must be non-empty string")
        if not isinstance(self.api_key, str) or not self.api_key.strip():
            raise ValueError("API key must be non-empty string")
        if not isinstance(self.config, dict):
            raise ValueError("Config must be a dictionary")

        # 验证提供商名称格式 - 更宽松的验证
        if not self.name or len(self.name.strip()) == 0:
            raise ValueError("Provider name cannot be empty")

        # 检查是否包含危险字符
        dangerous_chars = ['<', '>', '"', "'", '&', '\n', '\r', '\t']
        if any(char in self.name for char in dangerous_chars):
            raise ValueError("Provider name contains invalid characters")

    def get_masked_key(self) -> str:
        """获取脱敏的API密钥用于日志显示"""
        if len(self.api_key) <= 8:
            return "***"
        return f"{self.api_key[:4]}***{self.api_key[-4:]}"

    def __str__(self) -> str:
        """安全的字符串表示"""
        return f"ProviderConfig(name={self.name}, api_key={self.get_masked_key()}, config={self.config})"
