#!/usr/bin/env python3
"""
最终测试 - 验证所有修复
"""

import requests
import json
import time

def test_health():
    """测试服务健康状态"""
    try:
        response = requests.get("http://localhost:8000/api/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def test_non_stream():
    """测试非流式响应"""
    print("🧪 测试非流式响应...")
    
    data = {
        "model": "deepseek/deepseek-chat-v3-0324:free",
        "messages": [{"role": "user", "content": "请简单回复'你好'"}],
        "max_tokens": 10,
        "temperature": 0.1,
        "stream": False
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            headers={"Content-Type": "application/json", "X-Provider": "openrouter"},
            json=data,
            timeout=15
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            # 验证响应结构
            if isinstance(result, dict) and 'choices' in result:
                choices = result['choices']
                if len(choices) > 0 and 'message' in choices[0]:
                    content = choices[0]['message'].get('content', '')
                    print(f"   ✅ 内容: '{content}'")
                    return len(content.strip()) > 0
                else:
                    print(f"   ❌ 响应结构错误: {result}")
            else:
                print(f"   ❌ 响应格式错误: {result}")
        else:
            print(f"   ❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    return False

def test_stream():
    """测试流式响应"""
    print("\n🧪 测试流式响应...")
    
    data = {
        "model": "deepseek/deepseek-chat-v3-0324:free",
        "messages": [{"role": "user", "content": "请回复'流式测试'"}],
        "max_tokens": 20,
        "temperature": 0.1,
        "stream": True
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            headers={"Content-Type": "application/json", "X-Provider": "openrouter"},
            json=data,
            timeout=15,
            stream=True
        )
        
        print(f"   状态码: {response.status_code}")
        print(f"   Content-Type: {response.headers.get('Content-Type')}")
        
        if response.status_code == 200:
            content_parts = []
            valid_chunks = 0
            total_lines = 0
            
            for line in response.iter_lines(decode_unicode=True):
                if line:
                    total_lines += 1
                    
                    if line.startswith('data: '):
                        data_str = line[6:].strip()
                        
                        if data_str == '[DONE]':
                            print(f"   🏁 流式结束")
                            break
                        
                        try:
                            data_obj = json.loads(data_str)
                            valid_chunks += 1
                            
                            if 'choices' in data_obj and len(data_obj['choices']) > 0:
                                choice = data_obj['choices'][0]
                                if 'delta' in choice and 'content' in choice['delta']:
                                    content = choice['delta']['content']
                                    if content:
                                        content_parts.append(content)
                                        
                        except json.JSONDecodeError:
                            pass
                    
                    # 限制处理行数
                    if total_lines >= 50:
                        break
            
            full_content = ''.join(content_parts)
            print(f"   📊 统计: {total_lines}行, {valid_chunks}个有效数据包")
            print(f"   ✅ 完整内容: '{full_content}'")
            
            return len(full_content.strip()) > 0
            
        else:
            print(f"   ❌ 流式请求失败: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 流式异常: {e}")
    
    return False

def main():
    """主测试函数"""
    print("🔍 最终测试 - 验证客户端内容接收")
    print("=" * 50)
    
    # 检查服务状态
    if not test_health():
        print("❌ 服务未运行或不健康")
        print("💡 请先启动服务: python start_api_service.py")
        return
    
    print("✅ 服务运行正常")
    
    # 测试非流式
    non_stream_ok = test_non_stream()
    
    # 等待一下
    time.sleep(1)
    
    # 测试流式
    stream_ok = test_stream()
    
    # 结果总结
    print("\n" + "=" * 50)
    print("📊 最终测试结果:")
    print(f"   非流式响应: {'✅ 成功' if non_stream_ok else '❌ 失败'}")
    print(f"   流式响应: {'✅ 成功' if stream_ok else '❌ 失败'}")
    
    if non_stream_ok and stream_ok:
        print("\n🎉 所有测试通过！")
        print("✅ 客户端可以正确接收内容")
        print("✅ 流式和非流式响应都正常工作")
        print("✅ 问题已解决")
    else:
        print("\n⚠️  部分测试失败")
        if not non_stream_ok:
            print("❌ 非流式响应有问题")
        if not stream_ok:
            print("❌ 流式响应有问题")
        
        print("\n🔧 建议检查:")
        print("1. 查看服务日志中的详细错误信息")
        print("2. 确认OpenRouter API密钥和配置正确")
        print("3. 检查网络连接")

if __name__ == "__main__":
    main()
