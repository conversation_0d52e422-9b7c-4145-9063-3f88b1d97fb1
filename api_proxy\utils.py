"""工具函数模块。

包含各种实用工具函数，包括:
- 轮询提供者选择
- 日志处理
- 敏感信息过滤
"""

import re
import itertools
from typing import List, Any, Dict, Optional
from datetime import datetime
from .providers.base import BaseProvider

# 敏感信息匹配模式
SENSITIVE_PATTERNS = [
    (r'(?i)api[_\-]?key["\']?\s*[:=]\s*["\']?([^\'"\s]+)', "API_KEY"),  # API Key
    (r'(?i)password["\']?\s*[:=]\s*["\']?([^\'"\s]+)', "PASSWORD"),  # 密码
    (r'(?i)secret["\']?\s*[:=]\s*["\']?([^\'"\s]+)', "SECRET"),  # 密钥
    (r'(?i)token["\']?\s*[:=]\s*["\']?([^\'"\s]+)', "TOKEN"),  # Token
    (r'(?i)bearer\s+([a-zA-Z0-9\-_\.]+)', "BEARER_TOKEN"),  # Bearer <PERSON>
    (r'sk-[a-zA-Z0-9]{48}', "OPENAI_KEY"),  # OpenAI API Key
    (r'(?i)authorization["\']?\s*[:=]\s*["\']?([^\'"\s]+)', "AUTH_HEADER"),  # Authorization header
]


# 全局轮询状态
_round_robin_state = {}

# 智能提供商选择器状态
_provider_health = {}  # 记录提供商健康状态
_provider_failure_count = {}  # 记录失败次数
_provider_last_failure = {}  # 记录最后失败时间

import time
from datetime import datetime, timedelta

def get_round_robin_provider(providers: List[BaseProvider]) -> BaseProvider:
    """获取智能轮询模式的API提供商。

    优先选择健康的提供商，避免最近失败的提供商。

    Args:
        providers: 可用的提供者列表

    Returns:
        下一个最佳的提供者

    Raises:
        ValueError: 如果提供者列表为空
    """
    if not providers:
        raise ValueError("Providers list cannot be empty")

    # 使用提供者列表的ID作为键
    providers_id = id(providers)
    current_time = time.time()

    # 初始化状态
    if providers_id not in _round_robin_state:
        _round_robin_state[providers_id] = 0

    # 计算每个提供商的健康分数
    provider_scores = []
    for i, provider in enumerate(providers):
        provider_key = id(provider)

        # 基础分数
        score = 100

        # 失败次数惩罚
        failure_count = _provider_failure_count.get(provider_key, 0)
        score -= failure_count * 20  # 每次失败扣20分

        # 最近失败时间惩罚
        last_failure = _provider_last_failure.get(provider_key, 0)
        if last_failure > 0:
            time_since_failure = current_time - last_failure
            if time_since_failure < 60:  # 1分钟内失败过
                score -= 50
            elif time_since_failure < 300:  # 5分钟内失败过
                score -= 30
            elif time_since_failure < 900:  # 15分钟内失败过
                score -= 10

        provider_scores.append((i, score, provider))

    # 按分数排序，分数高的优先
    provider_scores.sort(key=lambda x: x[1], reverse=True)

    # 选择分数最高的提供商
    best_index, best_score, best_provider = provider_scores[0]

    # 更新轮询状态（用于下次选择时的参考）
    _round_robin_state[providers_id] = (best_index + 1) % len(providers)

    return best_provider

def record_provider_success(provider: BaseProvider):
    """记录提供商成功调用

    Args:
        provider: 成功的提供商
    """
    provider_key = id(provider)

    # 重置失败计数
    _provider_failure_count[provider_key] = 0
    _provider_last_failure[provider_key] = 0
    _provider_health[provider_key] = True

def record_provider_failure(provider: BaseProvider, error_type: str = ""):
    """记录提供商失败调用

    Args:
        provider: 失败的提供商
        error_type: 错误类型
    """
    provider_key = id(provider)
    current_time = time.time()

    # 增加失败计数
    _provider_failure_count[provider_key] = _provider_failure_count.get(provider_key, 0) + 1
    _provider_last_failure[provider_key] = current_time
    _provider_health[provider_key] = False

    # 如果是速率限制错误，给更重的惩罚
    if "429" in error_type or "rate limit" in error_type.lower():
        _provider_failure_count[provider_key] += 2  # 额外惩罚

def get_provider_health_info(providers: List[BaseProvider]) -> dict:
    """获取提供商健康信息

    Args:
        providers: 提供商列表

    Returns:
        dict: 健康信息
    """
    current_time = time.time()
    health_info = {}

    for i, provider in enumerate(providers):
        provider_key = id(provider)

        failure_count = _provider_failure_count.get(provider_key, 0)
        last_failure = _provider_last_failure.get(provider_key, 0)
        is_healthy = _provider_health.get(provider_key, True)

        time_since_failure = current_time - last_failure if last_failure > 0 else 0

        health_info[f"provider_{i}"] = {
            "healthy": is_healthy,
            "failure_count": failure_count,
            "last_failure_ago": f"{time_since_failure:.1f}s" if last_failure > 0 else "never",
            "provider_id": provider_key
        }

    return health_info


def redact_sensitive_info(text: str) -> str:
    """过滤日志中的敏感信息。

    Args:
        text: 原始文本

    Returns:
        过滤后的安全文本
    """
    if not text or not isinstance(text, str):
        return text

    for pattern, replacement in SENSITIVE_PATTERNS:
        text = re.sub(pattern, f"{replacement}=[REDACTED]", text)
    return text


def validate_api_key(api_key: str, provider_type: str = "openai") -> bool:
    """验证API密钥格式

    Args:
        api_key: API密钥
        provider_type: 提供商类型

    Returns:
        bool: 密钥格式是否有效
    """
    if not api_key or not isinstance(api_key, str):
        return False

    api_key = api_key.strip()

    if provider_type == "openai":
        # OpenAI API密钥格式: sk-开头，48个字符
        return bool(re.match(r'^sk-[a-zA-Z0-9]{48}$', api_key))

    # 通用验证：至少8个字符，包含字母和数字
    return len(api_key) >= 8 and bool(re.search(r'[a-zA-Z]', api_key)) and bool(re.search(r'[0-9]', api_key))


def normalize_log_timestamp(
    log: str, timestamp_format: str = "%Y-%m-%d %H:%M:%S"
) -> str:
    """标准化日志时间戳格式。

    Args:
        log: 原始日志
        timestamp_format: 统一后的时间格式

    Returns:
        标准化后的日志
    """
    # 匹配常见的时间戳格式
    patterns = [
        r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}',  # ISO格式
        r'\d{4}/\d{2}/\d{2} \d{2}:\d{2}:\d{2}',  # 日期时间格式
    ]

    for pattern in patterns:
        matches = re.finditer(pattern, log)
        for match in matches:
            original = match.group()
            try:
                dt = datetime.strptime(
                    original,
                    "%Y-%m-%dT%H:%M:%S" if 'T' in original else "%Y/%m/%d %H:%M:%S",
                )
                standardized = dt.strftime(timestamp_format)
                log = log.replace(original, standardized)
            except ValueError:
                continue
    return log


def extract_gitlab_ci_sections(log_text: str) -> Dict[str, str]:
    """提取GitLab CI日志中的不同阶段日志。

    Args:
        log_text: 原始日志文本

    Returns:
        按阶段划分的日志字典
    """
    sections = {}
    current_section = "preparation"
    section_content = []

    for line in log_text.splitlines():
        if line.startswith("$ ") or line.startswith("> "):
            sections[current_section] = "\n".join(section_content)
            current_section = line[2:].strip()
            section_content = []
        else:
            section_content.append(line)

    sections[current_section] = "\n".join(section_content)
    return sections


def batch_process(
    items: List[Any], batch_size: int, process_func: callable
) -> List[Any]:
    """批量处理列表数据。

    Args:
        items: 待处理的项目列表
        batch_size: 每批处理大小
        process_func: 处理函数

    Returns:
        处理后的结果列表
    """
    results = []
    for i in range(0, len(items), batch_size):
        batch = items[i : i + batch_size]
        results.extend(process_func(batch))
    return results
