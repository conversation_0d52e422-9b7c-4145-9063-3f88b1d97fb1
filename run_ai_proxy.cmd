@echo off
ECHO 正在启动 ai_proxy 应用...

:: 激活 Conda 环境
CALL conda activate ai_proxy
IF %ERRORLEVEL% NEQ 0 (
    ECHO 无法激活 Conda 环境 'ai_proxy'。请确保环境存在且 Conda 已正确安装。
    pause
    exit /b %ERRORLEVEL%
)

:: 启动 Uvicorn 服务器
ECHO 正在启动 Uvicorn 服务器...
uvicorn api_proxy.web_app:app --reload --host 0.0.0.0 --port 8000
IF %ERRORLEVEL% NEQ 0 (
    ECHO 无法启动 Uvicorn 服务器。请检查 Uvicorn 是否已安装以及命令是否正确。
    pause
    exit /b %ERRORLEVEL%
)

pause