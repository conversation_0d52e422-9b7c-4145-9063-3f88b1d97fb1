#!/usr/bin/env python3
"""
测试JSON序列化修复
验证datetime对象的序列化和反序列化
"""

import os
import time
from api_proxy.monitoring import RequestMonitor


def test_json_serialization():
    """测试JSON序列化功能"""
    print("🧪 测试JSON序列化修复")
    print("=" * 40)
    
    # 创建测试文件
    test_file = "test_serialization.json"
    if os.path.exists(test_file):
        os.remove(test_file)
    
    # 创建监控器
    monitor = RequestMonitor(stats_file=test_file, auto_save_interval=0)
    
    print("📊 创建监控器...")
    
    # 记录一些请求（包含datetime对象）
    print("📝 记录测试请求...")
    
    for i in range(5):
        monitor.record_request(
            provider=f"test:instance_{i % 2}",
            duration=0.5 + i * 0.1,
            success=i % 3 != 0,  # 部分失败
            error_type="TestError" if i % 3 == 0 else None,
            model=f"test-model-{i % 3}",
            endpoint="test/endpoint"
        )
        print(f"  第 {i+1} 次请求已记录")
        time.sleep(0.1)
    
    # 手动保存
    print("\n💾 测试手动保存...")
    try:
        monitor.save_now()
        print("✅ 保存成功")
    except Exception as e:
        print(f"❌ 保存失败: {e}")
        return False
    
    # 检查文件是否存在
    if os.path.exists(test_file):
        print(f"✅ 文件已创建: {test_file}")
        
        # 检查文件大小
        file_size = os.path.getsize(test_file)
        print(f"📁 文件大小: {file_size} 字节")
        
        if file_size > 0:
            print("✅ 文件内容不为空")
        else:
            print("❌ 文件为空")
            return False
    else:
        print("❌ 文件未创建")
        return False
    
    # 测试重新加载
    print("\n🔄 测试重新加载...")
    try:
        monitor2 = RequestMonitor(stats_file=test_file, auto_save_interval=0)
        print("✅ 重新加载成功")
        
        # 检查统计数据
        stats = monitor2.get_stats()
        print(f"📊 加载的统计数据:")
        
        instance_count = 0
        for key, value in stats.items():
            if key != 'summary' and isinstance(value, dict):
                instance_count += 1
                total_requests = value.get('total_requests', 0)
                model_usage = value.get('model_usage', {})
                print(f"  {key}: {total_requests} 请求, {len(model_usage)} 个模型")
        
        if instance_count > 0:
            print(f"✅ 成功加载 {instance_count} 个实例的统计数据")
        else:
            print("❌ 未加载到实例统计数据")
            return False
            
    except Exception as e:
        print(f"❌ 重新加载失败: {e}")
        return False
    
    # 清理测试文件
    print("\n🧹 清理测试文件...")
    try:
        if os.path.exists(test_file):
            os.remove(test_file)
        if os.path.exists(f"{test_file}.backup"):
            os.remove(f"{test_file}.backup")
        print("✅ 清理完成")
    except Exception as e:
        print(f"⚠️  清理失败: {e}")
    
    return True


def test_datetime_handling():
    """专门测试datetime对象的处理"""
    print("\n🕒 测试datetime对象处理")
    print("-" * 30)
    
    from datetime import datetime
    from api_proxy.monitoring import DateTimeEncoder
    import json
    
    # 测试自定义编码器
    test_data = {
        'timestamp': datetime.now(),
        'name': 'test',
        'nested': {
            'created_at': datetime.now(),
            'value': 123
        }
    }
    
    try:
        # 使用自定义编码器
        json_str = json.dumps(test_data, cls=DateTimeEncoder, indent=2)
        print("✅ 自定义编码器工作正常")
        
        # 测试反序列化
        parsed_data = json.loads(json_str)
        print("✅ JSON解析成功")
        
        # 检查时间戳格式
        if 'timestamp' in parsed_data and isinstance(parsed_data['timestamp'], str):
            print("✅ datetime对象已正确转换为字符串")
        else:
            print("❌ datetime对象转换失败")
            return False
            
    except Exception as e:
        print(f"❌ datetime处理失败: {e}")
        return False
    
    return True


if __name__ == "__main__":
    print("🔧 JSON序列化修复测试")
    print("=" * 50)
    
    # 测试datetime处理
    datetime_ok = test_datetime_handling()
    
    # 测试完整的序列化流程
    serialization_ok = test_json_serialization()
    
    print("\n" + "=" * 50)
    if datetime_ok and serialization_ok:
        print("🎉 所有测试通过！JSON序列化问题已修复")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    
    print("\n💡 修复内容:")
    print("1. 添加了DateTimeEncoder自定义JSON编码器")
    print("2. 在保存前预处理recent_requests中的datetime对象")
    print("3. 在加载时正确恢复datetime对象")
    print("4. 使用原子写入确保数据完整性")
