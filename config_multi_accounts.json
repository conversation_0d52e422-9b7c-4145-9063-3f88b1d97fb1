{"providers": {"openrouter": [{"name": "primary", "api_key": "sk-or-v1-your-primary-key-here", "config": {"timeout": 30, "site_url": "https://yourapp.com", "site_name": "Your AI App"}}, {"name": "backup1", "api_key": "sk-or-v1-your-backup1-key-here", "config": {"timeout": 30, "site_url": "https://yourapp.com", "site_name": "Your AI App"}}, {"name": "backup2", "api_key": "sk-or-v1-your-backup2-key-here", "config": {"timeout": 30, "site_url": "https://yourapp.com", "site_name": "Your AI App"}}, {"name": "emergency", "api_key": "sk-or-v1-your-emergency-key-here", "config": {"timeout": 60, "site_url": "https://yourapp.com", "site_name": "Your AI App"}}], "openai": [{"name": "openai_primary", "api_key": "sk-your-openai-primary-key-here", "config": {}}, {"name": "openai_backup", "api_key": "sk-your-openai-backup-key-here", "config": {}}]}, "default_timeout": 30, "max_retries": 3, "health_check_interval": 300, "enable_monitoring": true, "log_level": "INFO"}