#!/usr/bin/env python3
"""
智能提供商选择演示
"""

import requests
import json
import time
from datetime import datetime

def print_separator(title):
    """打印分隔符"""
    print("\n" + "=" * 60)
    print(f"🎯 {title}")
    print("=" * 60)

def get_smart_health():
    """获取智能健康状态"""
    try:
        response = requests.get("http://localhost:8000/api/health/smart", timeout=5)
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ 获取健康状态失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 健康状态请求异常: {e}")
        return None

def display_health_status(health_data):
    """显示健康状态"""
    if not health_data:
        print("❌ 无健康状态数据")
        return
    
    print(f"📊 智能提供商健康状态 ({health_data.get('timestamp', 'unknown')})")
    
    providers = health_data.get('providers', {})
    for provider_type, info in providers.items():
        print(f"\n🔗 {provider_type.upper()} 提供商:")
        print(f"   总数: {info.get('count', 0)}")
        
        health_details = info.get('health_details', {})
        for provider_name, details in health_details.items():
            status = "🟢 健康" if details.get('healthy', True) else "🔴 不健康"
            failure_count = details.get('failure_count', 0)
            last_failure = details.get('last_failure_ago', 'never')
            
            print(f"   {provider_name}: {status}")
            print(f"     失败次数: {failure_count}")
            print(f"     最后失败: {last_failure}")

def send_test_request(request_num):
    """发送测试请求"""
    test_data = {
        "model": "deepseek/deepseek-chat-v3-0324:free",
        "messages": [{"role": "user", "content": f"智能选择测试 #{request_num}"}],
        "max_tokens": 15,
        "stream": False
    }
    
    headers = {
        "Content-Type": "application/json",
        "X-Provider": "openrouter"
    }
    
    try:
        start_time = time.time()
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            headers=headers,
            json=test_data,
            timeout=25
        )
        end_time = time.time()
        
        duration = end_time - start_time
        
        if response.status_code == 200:
            print(f"✅ 请求 #{request_num} 成功 ({duration:.2f}s)")
            return True
        else:
            print(f"❌ 请求 #{request_num} 失败: {response.status_code} ({duration:.2f}s)")
            return False
            
    except Exception as e:
        print(f"❌ 请求 #{request_num} 异常: {e}")
        return False

def demo_smart_selection():
    """演示智能选择"""
    print_separator("智能提供商选择演示")
    
    print("📋 演示流程:")
    print("1. 显示初始健康状态")
    print("2. 发送多个请求观察选择模式")
    print("3. 显示更新后的健康状态")
    print("4. 快速请求触发限制")
    print("5. 观察智能故障转移")
    
    # 步骤1: 初始健康状态
    print_separator("步骤1: 初始健康状态")
    health_data = get_smart_health()
    display_health_status(health_data)
    
    input("\n按回车键继续...")
    
    # 步骤2: 正常请求
    print_separator("步骤2: 正常请求测试")
    print("📡 发送5个正常请求，观察智能选择...")
    
    success_count = 0
    for i in range(1, 6):
        if send_test_request(i):
            success_count += 1
        time.sleep(2)  # 适当间隔
    
    print(f"\n📊 正常请求结果: {success_count}/5 成功")
    
    # 步骤3: 更新后健康状态
    print_separator("步骤3: 更新后健康状态")
    health_data = get_smart_health()
    display_health_status(health_data)
    
    input("\n按回车键继续快速请求测试...")
    
    # 步骤4: 快速请求
    print_separator("步骤4: 快速请求触发限制")
    print("🚀 快速发送请求，可能触发速率限制...")
    
    rapid_success = 0
    for i in range(6, 11):
        if send_test_request(i):
            rapid_success += 1
        time.sleep(0.5)  # 很短间隔
    
    print(f"\n📊 快速请求结果: {rapid_success}/5 成功")
    
    # 步骤5: 最终健康状态
    print_separator("步骤5: 最终健康状态")
    health_data = get_smart_health()
    display_health_status(health_data)
    
    # 总结
    print_separator("演示总结")
    print("🧠 智能提供商选择的特点:")
    print("✅ 健康评分 - 根据失败历史计算分数")
    print("✅ 智能排序 - 优先选择健康的提供商")
    print("✅ 失败记忆 - 记住失败的提供商并暂时避免")
    print("✅ 时间恢复 - 失败提供商随时间逐渐恢复")
    print("✅ 避免重复 - 同一轮不重复尝试同一提供商")
    
    print(f"\n📊 总体成功率: {success_count + rapid_success}/10")
    
    if success_count + rapid_success >= 7:
        print("🎉 智能选择工作良好！")
    else:
        print("⚠️  可能需要检查配置或网络")

def check_service():
    """检查服务状态"""
    try:
        response = requests.get("http://localhost:8000/api/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def main():
    """主函数"""
    print("🧠 智能提供商选择演示程序")
    print("=" * 60)
    
    # 检查服务
    if not check_service():
        print("❌ 服务未运行")
        print("💡 请先启动服务:")
        print("   python start_api_service.py")
        print("💡 并确保配置了多个OpenRouter账户")
        return
    
    print("✅ 服务运行正常")
    print("\n💡 建议:")
    print("1. 在另一个终端查看服务日志")
    print("2. 观察智能选择的日志输出")
    print("3. 注意健康状态的变化")
    
    # 开始演示
    demo_smart_selection()

if __name__ == "__main__":
    main()
