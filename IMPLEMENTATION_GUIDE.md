# 详细实现指南

## 1. BaseProvider 接口扩展

### 当前状态
```python
class BaseProvider(ABC):
    @abstractmethod
    def call(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        pass
```

### 扩展方案
```python
from typing import List

class BaseProvider(ABC):
    # 保持现有方法
    @abstractmethod
    def call(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """统一调用接口 - 保持不变"""
        pass
    
    # 新增属性
    @property
    def supported_features(self) -> List[str]:
        """
        返回支持的功能列表
        
        示例:
        - ['chat'] - 仅支持文本对话
        - ['chat', 'image'] - 支持文本和图像
        - ['chat', 'image', 'video'] - 支持全部
        """
        return ['chat']
    
    # 新增方法
    def supports_feature(self, feature: str) -> bool:
        """检查是否支持某个功能"""
        return feature in self.supported_features
    
    def get_feature_config(self, feature: str) -> Dict[str, Any]:
        """获取功能特定配置"""
        return {}
```

## 2. 功能类型定义

### 新增到 models.py
```python
from enum import Enum

class APIFeatureType(Enum):
    """API 功能类型"""
    CHAT = "chat"
    IMAGE = "image"
    VIDEO = "video"

class EndpointType(Enum):
    """端点类型映射"""
    CHAT_COMPLETION = ("chat/completions", APIFeatureType.CHAT)
    IMAGE_GENERATION = ("images/generations", APIFeatureType.IMAGE)
    VIDEO_GENERATION = ("videos/generations", APIFeatureType.VIDEO)
    
    @classmethod
    def get_feature_type(cls, endpoint: str) -> APIFeatureType:
        """从端点获取功能类型"""
        for member in cls:
            if member.value[0] in endpoint:
                return member.value[1]
        return APIFeatureType.CHAT  # 默认
```

## 3. OpenAI 提供商扩展

### 关键改动
```python
class OpenAIProvider(BaseProvider):
    @property
    def supported_features(self) -> List[str]:
        return ['chat', 'image']  # OpenAI 支持文本和图像
    
    def call(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """路由到具体实现"""
        if 'image' in endpoint:
            return self._call_image_api(endpoint, **kwargs)
        else:
            return self._call_chat_api(endpoint, **kwargs)
    
    def _call_chat_api(self, endpoint: str, **kwargs):
        """现有实现保持不变"""
        # 保持原有代码
        pass
    
    def _call_image_api(self, endpoint: str, **kwargs):
        """新增：DALL-E API 调用"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }
        url = f"{self.base_url}/images/generations"
        
        # 参数映射
        image_params = {
            "prompt": kwargs.get("prompt"),
            "n": kwargs.get("n", 1),
            "size": kwargs.get("size", "1024x1024"),
            "quality": kwargs.get("quality", "standard"),
            "model": kwargs.get("model", "dall-e-3"),
        }
        
        response = requests.post(url, headers=headers, json=image_params, 
                               timeout=self.timeout)
        response.raise_for_status()
        self._last_used = datetime.now()
        return response.json()
```

## 4. 新增提供商实现

### Stability AI 提供商
```python
class StabilityAIProvider(BaseProvider):
    @property
    def name(self) -> str:
        return "stability_ai"
    
    @property
    def supported_features(self) -> List[str]:
        return ['image']
    
    def __init__(self, api_key: str, engine_id: str = "stable-diffusion-xl-1024-v1-0"):
        self.api_key = api_key
        self.engine_id = engine_id
        self.base_url = "https://api.stability.ai/v1"
        self._last_used = None
    
    def call(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """调用 Stability AI API"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Accept": "application/json",
        }
        
        url = f"{self.base_url}/generation/{self.engine_id}/text-to-image"
        
        payload = {
            "text_prompts": [{"text": kwargs.get("prompt")}],
            "cfg_scale": kwargs.get("cfg_scale", 7),
            "height": kwargs.get("height", 1024),
            "width": kwargs.get("width", 1024),
            "samples": kwargs.get("n", 1),
            "steps": kwargs.get("steps", 30),
        }
        
        response = requests.post(url, headers=headers, json=payload)
        response.raise_for_status()
        self._last_used = datetime.now()
        return response.json()
```

### Runway ML 提供商
```python
class RunwayMLProvider(BaseProvider):
    @property
    def name(self) -> str:
        return "runway_ml"
    
    @property
    def supported_features(self) -> List[str]:
        return ['video']
    
    def __init__(self, api_key: str, model: str = "gen3"):
        self.api_key = api_key
        self.model = model
        self.base_url = "https://api.runwayml.com/v1"
        self._last_used = None
    
    def call(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """调用 Runway ML API"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }
        
        url = f"{self.base_url}/image_to_video"
        
        payload = {
            "model": self.model,
            "prompt": kwargs.get("prompt"),
            "duration": kwargs.get("duration", 10),
            "fps": kwargs.get("fps", 24),
        }
        
        response = requests.post(url, headers=headers, json=payload)
        response.raise_for_status()
        self._last_used = datetime.now()
        return response.json()
```

## 5. ProxyService 更新

### 提供商工厂方法扩展
```python
def _create_provider(self, provider_type: str, config: ProviderConfig):
    """扩展的提供商创建工厂"""
    providers_map = {
        "openai": lambda cfg: OpenAIProvider(cfg.api_key),
        "openrouter": lambda cfg: OpenRouterProvider(
            cfg.api_key,
            timeout=cfg.config.get("timeout", 30),
            site_url=cfg.config.get("site_url", ""),
            site_name=cfg.config.get("site_name", "")
        ),
        "stability_ai": lambda cfg: StabilityAIProvider(
            cfg.api_key,
            engine_id=cfg.config.get("engine_id", "stable-diffusion-xl-1024-v1-0")
        ),
        "runway_ml": lambda cfg: RunwayMLProvider(
            cfg.api_key,
            model=cfg.config.get("model", "gen3")
        ),
    }
    
    if provider_type not in providers_map:
        raise ValueError(f"不支持的提供商类型: {provider_type}")
    
    return providers_map[provider_type](config)

def get_available_features(self, provider_type: str) -> List[str]:
    """获取提供商支持的功能"""
    providers = self.providers.get(provider_type, [])
    if providers:
        return providers[0].supported_features
    return []
```

## 6. Web API 端点

### 新增到 web_app.py
```python
@app.post("/v1/images/generations")
async def generate_image(
    request: ImageGenerationRequest,
    authorization: str = Header(None),
    x_provider: str = Header("openai")
):
    """文生图 API"""
    api_key = verify_api_key(authorization)
    
    provider = proxy_service.get_provider(x_provider)
    response = provider.call(
        "images/generations",
        prompt=request.prompt,
        size=request.size,
        n=request.n,
        quality=request.quality
    )
    return response

@app.post("/v1/videos/generations")
async def generate_video(
    request: VideoGenerationRequest,
    authorization: str = Header(None),
    x_provider: str = Header("runway_ml")
):
    """文生视频 API"""
    api_key = verify_api_key(authorization)
    
    provider = proxy_service.get_provider(x_provider)
    response = provider.call(
        "videos/generations",
        prompt=request.prompt,
        duration=request.duration,
        fps=request.fps
    )
    return response
```

## 7. 配置示例更新

```json
{
  "providers": {
    "openai": [
      {
        "name": "primary",
        "api_key": "sk-xxx",
        "config": {"timeout": 30}
      }
    ],
    "stability_ai": [
      {
        "name": "primary",
        "api_key": "sk-stability-xxx",
        "config": {
          "engine_id": "stable-diffusion-xl-1024-v1-0"
        }
      }
    ],
    "runway_ml": [
      {
        "name": "primary",
        "api_key": "runway-api-key",
        "config": {"model": "gen3"}
      }
    ]
  }
}
```

## 8. 测试策略

### 单元测试
- 测试 supported_features 属性
- 测试端点路由逻辑
- 测试参数映射

### 集成测试
- 测试完整的请求流程
- 测试故障转移
- 测试监控统计

### 兼容性测试
- 确保现有 API 不受影响
- 测试向后兼容性

