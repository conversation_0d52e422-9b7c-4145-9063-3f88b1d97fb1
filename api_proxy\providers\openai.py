# OpenAI提供商实现
import logging
from typing import Dict, Any, Optional
import requests
import time
from datetime import datetime
from .base import BaseProvider


class OpenAIProvider(BaseProvider):
    """OpenAI API提供商实现

    特性:
    - 支持聊天补全API
    - 自动重试机制
    - 超时处理
    - 使用时间追踪
    """

    @property
    def name(self) -> str:
        return "openai"

    @property
    def last_used(self) -> Optional[datetime]:
        """返回最后API调用时间戳"""
        return self._last_used

    def __init__(self, api_key: str, timeout: int = 10):
        """初始化OpenAI提供者

        Args:
            api_key: OpenAI API密钥
            timeout: 请求超时(秒)

        Raises:
            ValueError: API密钥无效或超时参数无效
        """
        if not api_key or not isinstance(api_key, str) or not api_key.strip():
            raise ValueError("API密钥必须是非空字符串")
        if not isinstance(timeout, int) or timeout <= 0:
            raise ValueError("超时时间必须是正整数")

        self.api_key = api_key.strip()
        self.timeout = timeout
        self.base_url = "https://api.openai.com/v1"
        self.logger = logging.getLogger(__name__)
        self._last_used = None

    def call(self, endpoint: str, max_retries: int = 3, **kwargs) -> Dict[str, Any]:
        """调用OpenAI API
        Args:
            endpoint: API端点路径
            max_retries: 最大重试次数(1-5)
            **kwargs: API请求参数

        Returns:
            Dict[str, Any]: API响应数据

        Raises:
            ValueError: 参数无效
            requests.HTTPError: API请求失败
            TimeoutError: 请求超时
            RateLimitError: 达到速率限制
            ProviderError: 提供商特定错误
        """
        if not endpoint or not isinstance(endpoint, str):
            raise ValueError("端点路径必须是非空字符串")
        if not 1 <= max_retries <= 5:
            raise ValueError("重试次数必须在1-5之间")
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }
        url = f"{self.base_url}/{endpoint.lstrip('/')}"

        for attempt in range(max_retries):
            try:
                response = requests.post(url, headers=headers, json=kwargs, timeout=self.timeout)
                response.raise_for_status()
                self._last_used = datetime.now()
                return response.json()
            except requests.RequestException as e:
                self.logger.warning(
                    f"OpenAI API调用失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}"
                )
                if attempt == max_retries - 1:
                    self.logger.error("达到最大重试次数，调用失败")
                    raise
                wait_time = 2**attempt
                self.logger.info(f"等待 {wait_time}秒后重试...")
                time.sleep(wait_time)

    def __str__(self) -> str:
        """安全的字符串表示，隐藏API密钥"""
        masked_key = f"{self.api_key[:8]}***" if len(self.api_key) > 8 else "***"
        return f"OpenAIProvider(api_key={masked_key}, timeout={self.timeout})"

    def __repr__(self) -> str:
        """安全的对象表示"""
        return self.__str__()
