#!/bin/bash
# AI代理服务自动部署脚本

set -euo pipefail

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
APP_NAME="ai-proxy"
DEPLOY_USER="deploy"
BACKUP_DIR="/opt/backups"
LOG_FILE="/var/log/deploy-${APP_NAME}.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

# 显示帮助信息
show_help() {
    cat << EOF
AI代理服务部署脚本

用法: $0 [选项] <环境>

环境:
    staging     部署到测试环境
    production  部署到生产环境

选项:
    -v, --version VERSION   指定版本标签 (默认: latest)
    -b, --backup           部署前创建备份
    -r, --rollback         回滚到上一个版本
    -h, --help             显示此帮助信息
    --dry-run              模拟运行，不执行实际部署
    --skip-tests           跳过部署后测试
    --force                强制部署，跳过确认

示例:
    $0 staging
    $0 production -v v1.2.3 -b
    $0 production --rollback
EOF
}

# 解析命令行参数
parse_args() {
    ENVIRONMENT=""
    VERSION="latest"
    BACKUP=false
    ROLLBACK=false
    DRY_RUN=false
    SKIP_TESTS=false
    FORCE=false

    while [[ $# -gt 0 ]]; do
        case $1 in
            -v|--version)
                VERSION="$2"
                shift 2
                ;;
            -b|--backup)
                BACKUP=true
                shift
                ;;
            -r|--rollback)
                ROLLBACK=true
                shift
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            --skip-tests)
                SKIP_TESTS=true
                shift
                ;;
            --force)
                FORCE=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            staging|production)
                ENVIRONMENT="$1"
                shift
                ;;
            *)
                error "未知参数: $1"
                ;;
        esac
    done

    if [[ -z "$ENVIRONMENT" ]]; then
        error "请指定部署环境 (staging 或 production)"
    fi
}

# 检查先决条件
check_prerequisites() {
    log "检查部署先决条件..."

    # 检查必需的命令
    local required_commands=("docker" "docker-compose" "curl" "jq")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            error "缺少必需的命令: $cmd"
        fi
    done

    # 检查Docker服务
    if ! docker info &> /dev/null; then
        error "Docker服务未运行"
    fi

    # 检查部署用户权限
    if [[ "$USER" != "$DEPLOY_USER" ]] && [[ "$USER" != "root" ]]; then
        error "请使用 $DEPLOY_USER 用户或 root 用户运行此脚本"
    fi

    # 检查环境配置文件
    local env_file="${PROJECT_ROOT}/.env.${ENVIRONMENT}"
    if [[ ! -f "$env_file" ]]; then
        error "环境配置文件不存在: $env_file"
    fi

    success "先决条件检查通过"
}

# 加载环境配置
load_environment() {
    log "加载 $ENVIRONMENT 环境配置..."
    
    local env_file="${PROJECT_ROOT}/.env.${ENVIRONMENT}"
    if [[ -f "$env_file" ]]; then
        set -a
        source "$env_file"
        set +a
        success "环境配置加载完成"
    else
        error "环境配置文件不存在: $env_file"
    fi
}

# 创建备份
create_backup() {
    if [[ "$BACKUP" == true ]]; then
        log "创建部署前备份..."
        
        local backup_name="${APP_NAME}-backup-$(date +%Y%m%d-%H%M%S)"
        local backup_path="${BACKUP_DIR}/${backup_name}"
        
        mkdir -p "$BACKUP_DIR"
        
        # 备份数据文件
        log "备份数据文件..."
        if [[ -f "stats.json" ]]; then
            cp "stats.json" "${backup_path}-stats.json"
        fi
        if [[ -f "auth_config.json" ]]; then
            cp "auth_config.json" "${backup_path}-auth.json"
        fi
        
        # 备份配置文件
        log "备份配置文件..."
        tar -czf "${backup_path}-config.tar.gz" \
            config.json \
            .env.${ENVIRONMENT} \
            docker-compose.yml \
            2>/dev/null || true

        # 备份数据目录
        if [[ -d "data" ]]; then
            log "备份数据目录..."
            tar -czf "${backup_path}-data.tar.gz" data/
        fi

        # 备份日志目录
        if [[ -d "logs" ]]; then
            log "备份日志目录..."
            tar -czf "${backup_path}-logs.tar.gz" logs/
        fi
        
        success "备份创建完成: $backup_path"
        echo "$backup_path" > "${BACKUP_DIR}/latest-backup"
    fi
}

# 拉取最新镜像
pull_images() {
    log "拉取Docker镜像 (版本: $VERSION)..."
    
    if [[ "$DRY_RUN" == true ]]; then
        log "[DRY RUN] 将拉取镜像: ${REGISTRY_URL}/${APP_NAME}:${VERSION}"
        return
    fi
    
    # 设置镜像标签
    export VERSION="$VERSION"
    
    # 拉取镜像
    docker-compose pull
    
    success "镜像拉取完成"
}

# 部署应用
deploy_application() {
    log "部署应用到 $ENVIRONMENT 环境..."
    
    if [[ "$DRY_RUN" == true ]]; then
        log "[DRY RUN] 将执行: docker-compose up -d"
        return
    fi
    
    # 停止旧容器
    log "停止现有服务..."
    docker-compose down --remove-orphans
    
    # 启动新容器
    log "启动新服务..."
    docker-compose up -d
    
    # 等待服务启动
    log "等待服务启动..."
    sleep 30
    
    success "应用部署完成"
}

# 健康检查
health_check() {
    if [[ "$SKIP_TESTS" == true ]]; then
        warning "跳过健康检查"
        return
    fi
    
    log "执行健康检查..."
    
    local health_url="http://localhost:${APP_PORT:-8080}/health"
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f -s "$health_url" > /dev/null; then
            success "健康检查通过"
            return
        fi
        
        log "健康检查失败 (尝试 $attempt/$max_attempts)，等待5秒后重试..."
        sleep 5
        ((attempt++))
    done
    
    error "健康检查失败，部署可能有问题"
}

# 回滚部署
rollback_deployment() {
    log "执行回滚操作..."
    
    local latest_backup_file="${BACKUP_DIR}/latest-backup"
    if [[ ! -f "$latest_backup_file" ]]; then
        error "没有找到备份信息，无法回滚"
    fi
    
    local backup_path=$(cat "$latest_backup_file")
    
    if [[ "$DRY_RUN" == true ]]; then
        log "[DRY RUN] 将从备份回滚: $backup_path"
        return
    fi
    
    # 停止当前服务
    docker-compose down
    
    # 恢复配置文件
    if [[ -f "${backup_path}-config.tar.gz" ]]; then
        log "恢复配置文件..."
        tar -xzf "${backup_path}-config.tar.gz"
    fi

    # 恢复数据文件
    if [[ -f "${backup_path}-stats.json" ]]; then
        log "恢复统计数据..."
        cp "${backup_path}-stats.json" "stats.json"
    fi

    if [[ -f "${backup_path}-auth.json" ]]; then
        log "恢复认证配置..."
        cp "${backup_path}-auth.json" "auth_config.json"
    fi

    # 恢复数据目录
    if [[ -f "${backup_path}-data.tar.gz" ]]; then
        log "恢复数据目录..."
        rm -rf data/
        tar -xzf "${backup_path}-data.tar.gz"
    fi
    
    # 重启服务
    docker-compose up -d
    
    success "回滚完成"
}

# 清理旧镜像
cleanup_old_images() {
    log "清理旧的Docker镜像..."
    
    if [[ "$DRY_RUN" == true ]]; then
        log "[DRY RUN] 将清理未使用的镜像"
        return
    fi
    
    # 清理悬空镜像
    docker image prune -f
    
    # 保留最近3个版本的镜像
    docker images "${REGISTRY_URL}/${APP_NAME}" --format "table {{.Tag}}\t{{.ID}}" | \
        tail -n +4 | \
        awk '{print $2}' | \
        xargs -r docker rmi || true
    
    success "镜像清理完成"
}

# 发送通知
send_notification() {
    local status="$1"
    local message="$2"
    
    if [[ -n "${WEBHOOK_URL:-}" ]]; then
        local payload=$(jq -n \
            --arg text "$message" \
            --arg status "$status" \
            '{text: $text, status: $status}')
        
        curl -X POST "$WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "$payload" || true
    fi
}

# 主函数
main() {
    log "开始 AI代理服务部署流程"
    
    parse_args "$@"
    
    # 显示部署信息
    log "部署配置:"
    log "  环境: $ENVIRONMENT"
    log "  版本: $VERSION"
    log "  备份: $BACKUP"
    log "  回滚: $ROLLBACK"
    log "  模拟运行: $DRY_RUN"
    
    # 确认部署
    if [[ "$FORCE" != true ]] && [[ "$DRY_RUN" != true ]]; then
        read -p "确认继续部署? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log "部署已取消"
            exit 0
        fi
    fi
    
    check_prerequisites
    load_environment
    
    if [[ "$ROLLBACK" == true ]]; then
        rollback_deployment
        health_check
        send_notification "success" "AI代理服务回滚完成 - 环境: $ENVIRONMENT"
    else
        create_backup
        pull_images
        deploy_application
        health_check
        cleanup_old_images
        send_notification "success" "AI代理服务部署完成 - 环境: $ENVIRONMENT, 版本: $VERSION"
    fi
    
    success "部署流程完成!"
}

# 执行主函数
main "$@"
