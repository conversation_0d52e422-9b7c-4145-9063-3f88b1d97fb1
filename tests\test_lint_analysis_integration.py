"""Lint分析集成测试模块

测试LintAnalyzer与其他组件的集成及边缘情况处理
"""

import pytest
from unittest.mock import patch
from api_proxy.job_lint_service import <PERSON>t<PERSON>nal<PERSON><PERSON>, LintErrorType


class TestLintAnalyzerIntegration:
    """Lint分析器集成测试类"""

    @pytest.fixture
    def analyzer(self):
        return LintAnalyzer()

    def test_full_workflow(self, analyzer):
        """测试完整的日志处理流程"""
        log = """[INFO] Starting lint
would reformat src/main.py
ERROR: src/utils.py Imports are incorrectly sorted
src/utils.py:10:1: E302 expected 2 blank lines
src/models.py:15: error: Incompatible return value type
Oh no! 💥 2 files would be reformatted
"""
        results = analyzer.analyze_log(log)

        assert len(results) == 5
        error_types = {r.error_type for r in results}
        assert LintErrorType.BLACK_FORMATTING in error_types
        assert LintErrorType.BLACK_FAILED in error_types
        assert LintErrorType.FLAKE8_SYNTAX in error_types
        assert LintErrorType.ISORT_ISSUE in error_types
        assert LintErrorType.MYPY_ERROR in error_types

    def test_solution_recommendations(self, analyzer):
        """测试解决方案推荐"""
        log = "would reformat src/api/main.py"
        results = analyzer.analyze_log(log)

        assert len(results) == 1
        assert "black src/api/main.py" in results[0].solution

    def test_error_priority(self, analyzer):
        """测试错误优先级"""
        log = "would reformat file.py\nfile.py:1: error: Missing return statement"
        results = analyzer.analyze_log(log)

        # 类型错误应该优先于格式化错误
        assert results[0].error_type == LintErrorType.MYPY_ERROR

    def test_with_mock_log_source(self):
        """测试模拟日志源集成"""
        with patch('api_proxy.log_source.get_lint_log') as mock_get_log:
            mock_get_log.return_value = "would reformat src/main.py"

            analyzer = LintAnalyzer()
            result = analyzer.analyze_log(mock_get_log())

            assert len(result) == 1
