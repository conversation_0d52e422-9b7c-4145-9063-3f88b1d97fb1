"""Lint分析边界测试模块

测试LintAnalyzer类的边缘情况和错误处理
"""

import pytest
from api_proxy.job_lint_service import LintAnalyzer


class TestLintAnalyzerBoundary:
    """Lint分析器边界测试类"""

    @pytest.fixture
    def analyzer(self):
        return LintAnalyzer()

    @pytest.mark.parametrize("invalid_input", [None, 123, [], {}, b"bytes"])
    def test_invalid_input_types(self, analyzer, invalid_input):
        """测试无效输入类型处理"""
        with pytest.raises((ValueError, TypeError)):
            analyzer.analyze_log(invalid_input)

    def test_large_log_file(self, analyzer):
        """测试大日志文件性能"""
        large_log = "would reformat test.py\n" * 10000
        results = analyzer.analyze_log(large_log)
        assert len(results) == 10000

    def test_partial_matches(self, analyzer):
        """测试部分匹配情况"""
        log = "file.py would reformat partially matched line"
        results = analyzer.analyze_log(log)
        assert len(results) == 1
        assert "file.py" in results[0].file_path
