# Include the README
include README.md

# Include the license file
include LICENSE

# Include requirements files
include requirements.txt
include requirements-dev.txt

# Include configuration files
include pyproject.toml
include .flake8

# Include documentation
recursive-include docs *
recursive-exclude docs/_build *

# Include test files
recursive-include tests *

# Include data files
recursive-include api_proxy *.json
recursive-include api_proxy *.yml
recursive-include api_proxy *.yaml

# Include example files
include example.py
recursive-include examples *

# Exclude compiled Python files
global-exclude *.pyc
global-exclude *.pyo
global-exclude *.pyd
global-exclude __pycache__
global-exclude *.so

# Exclude development files
exclude .gitignore
exclude .pre-commit-config.yaml
exclude docker-compose.yml
exclude Dockerfile
exclude .dockerignore

# Exclude IDE files
global-exclude .vscode
global-exclude .idea
global-exclude *.swp
global-exclude *.swo
