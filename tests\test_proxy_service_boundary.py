"""代理服务边界测试。

测试ProxyService类的边界情况处理。
"""

import pytest
from api_proxy.proxy_service import ProxyService
from api_proxy.config import Config


class TestProxyServiceBoundary:
    """代理服务边界测试类"""

    def test_invalid_config_handling(self):
        """测试无效配置处理"""
        # 测试None配置
        with pytest.raises(ValueError):
            ProxyService(None)

        # 测试空提供商配置
        with pytest.raises(ValueError):
            Config({})

        # 测试空提供商列表
        with pytest.raises(ValueError):
            Config({"test": []})

    def test_provider_not_found_handling(self):
        """测试提供者不存在的情况"""
        from api_proxy.models import ProviderConfig
        config = Config({
            "test": [ProviderConfig("test1", "test-key", {})]
        })
        service = ProxyService(config)

        with pytest.raises(ValueError):
            service.get_provider("non_existent")

    @pytest.mark.parametrize(
        "log_text", ["", " ", "\n", "No errors here", "[INFO] Some log message"]
    )
    def test_empty_log_analysis(self, log_text):
        """测试空日志或无害日志分析"""
        config = Config({})
        service = ProxyService(config)

        results = service.analyze_job_failure(log_text)
        assert len(results) == 0

    def test_multiple_errors_in_log(self):
        """测试日志中包含多个错误的情况"""
        config = Config({})
        service = ProxyService(config)
        log_text = """FAILED test1.py
ERROR: ModuleNotFoundError: No module named 'nonexistent'
Traceback (most recent call last):
  File "test.py", line 5, in <module>
    import nonexistent
MemoryError: out of memory"""

        results = service.analyze_job_failure(log_text)
        assert len(results) == 3
        assert any(r.error_type == JobErrorType.DEPENDENCY_ERROR for r in results)
        assert any(r.error_type == JobErrorType.MEMORY_ERROR for r in results)
