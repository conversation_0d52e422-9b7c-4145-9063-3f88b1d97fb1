#!/usr/bin/env python3
"""
快速测试修复后的流式响应
"""

import requests
import json

def test_quick():
    """快速测试"""
    print("🧪 快速测试流式响应修复...")

    # 测试非流式
    print("\n1. 测试非流式响应:")
    test_data = {
        "model": "deepseek/deepseek-chat-v3-0324:free",
        "messages": [{"role": "user", "content": "请说'你好'"}],
        "max_tokens": 10,
        "stream": False
    }

    try:
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            headers={"Content-Type": "application/json", "X-Provider": "openrouter"},
            json=test_data,
            timeout=15
        )
        print(f"   状态码: {response.status_code}")
        print(f"   Content-Type: {response.headers.get('Content-Type')}")

        if response.status_code == 200:
            result = response.json()
            print(f"   响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            print("   ✅ 非流式成功")
        else:
            print(f"   ❌ 非流式失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 非流式异常: {e}")

    # 测试流式
    print("\n2. 测试流式响应:")
    test_data["stream"] = True
    test_data["messages"] = [{"role": "user", "content": "请说'你好，世界'"}]

    try:
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            headers={"Content-Type": "application/json", "X-Provider": "openrouter"},
            json=test_data,
            timeout=15,
            stream=True
        )
        print(f"   状态码: {response.status_code}")
        print(f"   Content-Type: {response.headers.get('Content-Type')}")

        if response.status_code == 200:
            print("   ✅ 流式响应成功，开始接收数据:")
            count = 0
            full_content = ""

            for line in response.iter_lines(decode_unicode=True):
                if line:
                    count += 1
                    print(f"   第{count}行: {line}")

                    # 尝试解析SSE数据
                    if line.startswith('data: '):
                        try:
                            data_str = line[6:]  # 去掉 'data: ' 前缀
                            if data_str.strip() != '[DONE]':
                                data = json.loads(data_str)
                                if 'choices' in data and len(data['choices']) > 0:
                                    delta = data['choices'][0].get('delta', {})
                                    content = delta.get('content', '')
                                    if content:
                                        full_content += content
                        except json.JSONDecodeError:
                            pass

                    # 限制显示行数
                    if count >= 10:
                        print("   ... (更多数据)")
                        break

            if full_content:
                print(f"   📝 完整内容: {full_content}")
        else:
            print(f"   ❌ 流式失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 流式异常: {e}")

if __name__ == "__main__":
    test_quick()
