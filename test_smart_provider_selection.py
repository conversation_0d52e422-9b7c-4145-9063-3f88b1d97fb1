#!/usr/bin/env python3
"""
测试智能提供商选择功能
"""

import requests
import json
import time

def test_smart_provider_selection():
    """测试智能提供商选择"""
    print("🧠 测试智能提供商选择功能...")
    
    # 测试数据
    test_data = {
        "model": "deepseek/deepseek-chat-v3-0324:free",
        "messages": [{"role": "user", "content": "测试智能提供商选择"}],
        "max_tokens": 20,
        "stream": False
    }
    
    headers = {
        "Content-Type": "application/json",
        "X-Provider": "openrouter"
    }
    
    print("📡 连续发送请求，观察提供商选择模式...")
    
    success_count = 0
    total_requests = 10
    
    for i in range(total_requests):
        print(f"\n--- 请求 {i+1}/{total_requests} ---")
        
        try:
            start_time = time.time()
            response = requests.post(
                "http://localhost:8000/v1/chat/completions",
                headers=headers,
                json=test_data,
                timeout=30
            )
            end_time = time.time()
            
            duration = end_time - start_time
            print(f"⏱️  请求耗时: {duration:.2f}秒")
            print(f"📊 状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 请求{i+1}成功")
                success_count += 1
            else:
                print(f"❌ 请求{i+1}失败: {response.status_code}")
                error_info = response.text[:200] if response.text else "无错误信息"
                print(f"   错误信息: {error_info}...")
                
        except Exception as e:
            print(f"❌ 请求{i+1}异常: {e}")
        
        # 短暂间隔，观察选择模式
        time.sleep(1)
    
    print(f"\n📊 测试结果: {success_count}/{total_requests} 个请求成功")
    return success_count

def test_rapid_requests():
    """快速连续请求，触发速率限制"""
    print("\n🚀 快速连续请求测试...")
    
    test_data = {
        "model": "deepseek/deepseek-chat-v3-0324:free",
        "messages": [{"role": "user", "content": "快速请求"}],
        "max_tokens": 10,
        "stream": False
    }
    
    headers = {
        "Content-Type": "application/json",
        "X-Provider": "openrouter"
    }
    
    print("📡 快速发送5个请求...")
    
    for i in range(5):
        print(f"\n--- 快速请求 {i+1}/5 ---")
        
        try:
            response = requests.post(
                "http://localhost:8000/v1/chat/completions",
                headers=headers,
                json=test_data,
                timeout=20
            )
            
            print(f"📊 状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"✅ 快速请求{i+1}成功")
            else:
                print(f"❌ 快速请求{i+1}失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 快速请求{i+1}异常: {e}")
        
        # 很短的间隔
        time.sleep(0.2)

def check_provider_health():
    """检查提供商健康状态"""
    print("\n🏥 检查提供商健康状态...")
    
    try:
        response = requests.get("http://localhost:8000/api/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print("📊 服务健康状态:")
            print(json.dumps(health_data, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")

def test_after_cooldown():
    """冷却后测试"""
    print("\n❄️  等待冷却后测试...")
    
    print("⏳ 等待60秒让失败的提供商恢复...")
    for i in range(60, 0, -10):
        print(f"   剩余 {i} 秒...")
        time.sleep(10)
    
    print("🔄 冷却完成，重新测试...")
    
    test_data = {
        "model": "deepseek/deepseek-chat-v3-0324:free",
        "messages": [{"role": "user", "content": "冷却后测试"}],
        "max_tokens": 20,
        "stream": False
    }
    
    headers = {
        "Content-Type": "application/json",
        "X-Provider": "openrouter"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            headers=headers,
            json=test_data,
            timeout=30
        )
        
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            print(f"✅ 冷却后请求成功")
        else:
            print(f"❌ 冷却后请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 冷却后请求异常: {e}")

def check_service_health():
    """检查服务健康状态"""
    try:
        response = requests.get("http://localhost:8000/api/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def main():
    """主测试函数"""
    print("🧠 智能提供商选择测试")
    print("=" * 50)
    
    # 检查服务状态
    if not check_service_health():
        print("❌ 服务未运行或不健康")
        print("💡 请先启动服务: python start_api_service.py")
        print("💡 并确保配置了多个OpenRouter账户")
        return
    
    print("✅ 服务运行正常")
    print("\n📋 智能提供商选择功能说明:")
    print("1. 🧠 智能选择 - 优先选择健康的提供商")
    print("2. 📊 健康评分 - 根据失败次数和时间计算分数")
    print("3. 🚫 失败惩罚 - 失败的提供商会被暂时降级")
    print("4. ⏰ 时间恢复 - 随时间推移，失败提供商会逐渐恢复")
    print("5. 🔄 避免重复 - 同一轮不会重复尝试同一提供商")
    
    print("\n💡 观察要点:")
    print("   - 🔄 智能选择提供商日志")
    print("   - 📊 提供商健康状态显示")
    print("   - 🚫 失败后的智能切换")
    print("   - ✅ 成功后的状态恢复")
    
    # 等待用户确认
    input("\n按回车键开始测试...")
    
    # 检查初始健康状态
    check_provider_health()
    
    # 测试智能选择
    success_count = test_smart_provider_selection()
    
    # 快速请求触发限制
    test_rapid_requests()
    
    # 再次检查健康状态
    check_provider_health()
    
    print("\n" + "=" * 50)
    print("📊 智能提供商选择测试总结:")
    print(f"   基本测试: {success_count}/10 成功")
    print("\n🧠 智能选择的优势:")
    print("✅ 避免重复失败 - 不会连续尝试已知失败的提供商")
    print("✅ 健康评分 - 根据历史表现智能排序")
    print("✅ 时间恢复 - 失败提供商会随时间恢复")
    print("✅ 速率限制感知 - 429错误会得到额外惩罚")
    print("✅ 避免重复尝试 - 同一轮不会重复选择")
    
    print("\n🎯 与传统轮询的区别:")
    print("❌ 传统轮询: 总是从第一个开始，重复失败")
    print("✅ 智能选择: 优先选择健康的，避免已知问题")
    
    if success_count >= 7:
        print("\n🎉 智能提供商选择工作正常！")
    else:
        print("\n⚠️  可能需要检查账户配置或网络连接")
    
    # 询问是否进行冷却测试
    if input("\n是否进行冷却恢复测试？(y/N): ").lower() == 'y':
        test_after_cooldown()

if __name__ == "__main__":
    main()
