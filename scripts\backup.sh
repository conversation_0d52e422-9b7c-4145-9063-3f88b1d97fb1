#!/bin/bash
# AI代理服务数据备份脚本
# 备份配置文件、统计数据和日志文件

set -euo pipefail

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="${BACKUP_DIR:-$PROJECT_ROOT/backups}"
APP_NAME="ai-proxy"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
AI代理服务数据备份脚本

用法: $0 [选项]

选项:
    -d, --dir DIR          指定备份目录 (默认: ./backups)
    -r, --retention DAYS   备份保留天数 (默认: 30)
    -c, --compress         压缩备份文件
    -v, --verbose          详细输出
    -h, --help             显示此帮助信息
    --dry-run              模拟运行，不执行实际备份

示例:
    $0                     # 使用默认设置备份
    $0 -d /opt/backups -r 7 -c  # 备份到指定目录，保留7天，压缩
EOF
}

# 解析命令行参数
parse_args() {
    RETENTION_DAYS=30
    COMPRESS=false
    VERBOSE=false
    DRY_RUN=false

    while [[ $# -gt 0 ]]; do
        case $1 in
            -d|--dir)
                BACKUP_DIR="$2"
                shift 2
                ;;
            -r|--retention)
                RETENTION_DAYS="$2"
                shift 2
                ;;
            -c|--compress)
                COMPRESS=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                error "未知参数: $1"
                ;;
        esac
    done
}

# 创建备份目录
create_backup_dir() {
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_name="${APP_NAME}_backup_${timestamp}"
    CURRENT_BACKUP_DIR="$BACKUP_DIR/$backup_name"
    
    if [[ "$DRY_RUN" == true ]]; then
        log "[DRY RUN] 将创建备份目录: $CURRENT_BACKUP_DIR"
        return
    fi
    
    mkdir -p "$CURRENT_BACKUP_DIR"
    log "创建备份目录: $CURRENT_BACKUP_DIR"
}

# 备份配置文件
backup_config() {
    log "备份配置文件..."
    
    local config_files=(
        "config.json"
        "auth_config.json"
        ".env"
        ".env.production"
        ".env.staging"
    )
    
    for file in "${config_files[@]}"; do
        local file_path="$PROJECT_ROOT/$file"
        if [[ -f "$file_path" ]]; then
            if [[ "$DRY_RUN" == true ]]; then
                log "[DRY RUN] 将备份: $file"
            else
                cp "$file_path" "$CURRENT_BACKUP_DIR/"
                [[ "$VERBOSE" == true ]] && log "  ✓ $file"
            fi
        fi
    done
}

# 备份数据文件
backup_data() {
    log "备份数据文件..."
    
    local data_files=(
        "stats.json"
        "stats.json.backup"
    )
    
    # 备份数据目录
    if [[ -d "$PROJECT_ROOT/data" ]]; then
        if [[ "$DRY_RUN" == true ]]; then
            log "[DRY RUN] 将备份数据目录"
        else
            cp -r "$PROJECT_ROOT/data" "$CURRENT_BACKUP_DIR/"
            [[ "$VERBOSE" == true ]] && log "  ✓ data/"
        fi
    fi
    
    # 备份统计文件
    for file in "${data_files[@]}"; do
        local file_path="$PROJECT_ROOT/$file"
        if [[ -f "$file_path" ]]; then
            if [[ "$DRY_RUN" == true ]]; then
                log "[DRY RUN] 将备份: $file"
            else
                cp "$file_path" "$CURRENT_BACKUP_DIR/"
                [[ "$VERBOSE" == true ]] && log "  ✓ $file"
            fi
        fi
    done
}

# 备份日志文件
backup_logs() {
    log "备份日志文件..."
    
    if [[ -d "$PROJECT_ROOT/logs" ]]; then
        if [[ "$DRY_RUN" == true ]]; then
            log "[DRY RUN] 将备份日志目录"
        else
            # 只备份最近7天的日志
            mkdir -p "$CURRENT_BACKUP_DIR/logs"
            find "$PROJECT_ROOT/logs" -name "*.log" -mtime -7 -exec cp {} "$CURRENT_BACKUP_DIR/logs/" \;
            [[ "$VERBOSE" == true ]] && log "  ✓ logs/ (最近7天)"
        fi
    fi
}

# 创建备份清单
create_manifest() {
    if [[ "$DRY_RUN" == true ]]; then
        log "[DRY RUN] 将创建备份清单"
        return
    fi
    
    log "创建备份清单..."
    
    local manifest_file="$CURRENT_BACKUP_DIR/MANIFEST.txt"
    cat > "$manifest_file" << EOF
AI代理服务备份清单
==================

备份时间: $(date)
备份版本: $(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
备份目录: $CURRENT_BACKUP_DIR

文件列表:
EOF
    
    find "$CURRENT_BACKUP_DIR" -type f -exec ls -lh {} \; | sed 's|'$CURRENT_BACKUP_DIR'||g' >> "$manifest_file"
    
    # 计算总大小
    local total_size=$(du -sh "$CURRENT_BACKUP_DIR" | cut -f1)
    echo -e "\n总大小: $total_size" >> "$manifest_file"
}

# 压缩备份
compress_backup() {
    if [[ "$COMPRESS" != true ]]; then
        return
    fi
    
    if [[ "$DRY_RUN" == true ]]; then
        log "[DRY RUN] 将压缩备份文件"
        return
    fi
    
    log "压缩备份文件..."
    
    local backup_name=$(basename "$CURRENT_BACKUP_DIR")
    local compressed_file="$BACKUP_DIR/${backup_name}.tar.gz"
    
    cd "$BACKUP_DIR"
    tar -czf "$compressed_file" "$backup_name"
    rm -rf "$CURRENT_BACKUP_DIR"
    
    CURRENT_BACKUP_DIR="$compressed_file"
    success "备份已压缩: $compressed_file"
}

# 清理旧备份
cleanup_old_backups() {
    log "清理超过 $RETENTION_DAYS 天的旧备份..."
    
    if [[ "$DRY_RUN" == true ]]; then
        log "[DRY RUN] 将清理旧备份文件"
        find "$BACKUP_DIR" -name "${APP_NAME}_backup_*" -mtime +$RETENTION_DAYS -type f -o -type d | head -5
        return
    fi
    
    local deleted_count=0
    
    # 清理旧的备份目录
    while IFS= read -r -d '' backup; do
        rm -rf "$backup"
        ((deleted_count++))
        [[ "$VERBOSE" == true ]] && log "  删除: $(basename "$backup")"
    done < <(find "$BACKUP_DIR" -name "${APP_NAME}_backup_*" -mtime +$RETENTION_DAYS \( -type f -o -type d \) -print0)
    
    if [[ $deleted_count -gt 0 ]]; then
        log "已删除 $deleted_count 个旧备份"
    else
        log "没有需要清理的旧备份"
    fi
}

# 验证备份
verify_backup() {
    if [[ "$DRY_RUN" == true ]]; then
        log "[DRY RUN] 将验证备份完整性"
        return
    fi
    
    log "验证备份完整性..."
    
    local backup_path="$CURRENT_BACKUP_DIR"
    
    # 如果是压缩文件，先解压验证
    if [[ "$COMPRESS" == true ]]; then
        local temp_dir=$(mktemp -d)
        tar -xzf "$backup_path" -C "$temp_dir"
        backup_path="$temp_dir/$(basename "$CURRENT_BACKUP_DIR" .tar.gz)"
    fi
    
    # 检查关键文件
    local critical_files=("config.json" "stats.json")
    local missing_files=()
    
    for file in "${critical_files[@]}"; do
        if [[ ! -f "$backup_path/$file" ]] && [[ -f "$PROJECT_ROOT/$file" ]]; then
            missing_files+=("$file")
        fi
    done
    
    if [[ ${#missing_files[@]} -gt 0 ]]; then
        error "备份验证失败，缺少文件: ${missing_files[*]}"
    fi
    
    success "备份验证通过"
    
    # 清理临时目录
    if [[ "$COMPRESS" == true ]]; then
        rm -rf "$temp_dir"
    fi
}

# 发送通知
send_notification() {
    local status="$1"
    local message="$2"
    
    if [[ -n "${WEBHOOK_URL:-}" ]]; then
        local payload=$(cat << EOF
{
    "text": "📦 AI代理服务备份通知",
    "attachments": [
        {
            "color": "$([[ "$status" == "success" ]] && echo "good" || echo "danger")",
            "fields": [
                {
                    "title": "状态",
                    "value": "$status",
                    "short": true
                },
                {
                    "title": "消息",
                    "value": "$message",
                    "short": false
                },
                {
                    "title": "备份路径",
                    "value": "$CURRENT_BACKUP_DIR",
                    "short": false
                }
            ]
        }
    ]
}
EOF
        )
        
        curl -X POST "$WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "$payload" 2>/dev/null || true
    fi
}

# 主函数
main() {
    log "开始AI代理服务数据备份"
    
    parse_args "$@"
    
    # 显示配置
    log "备份配置:"
    log "  备份目录: $BACKUP_DIR"
    log "  保留天数: $RETENTION_DAYS"
    log "  压缩备份: $COMPRESS"
    log "  模拟运行: $DRY_RUN"
    
    cd "$PROJECT_ROOT"
    
    create_backup_dir
    backup_config
    backup_data
    backup_logs
    create_manifest
    compress_backup
    verify_backup
    cleanup_old_backups
    
    local backup_size=""
    if [[ "$DRY_RUN" != true ]]; then
        backup_size=$(du -sh "$CURRENT_BACKUP_DIR" | cut -f1)
    fi
    
    success "备份完成! ${backup_size:+大小: $backup_size}"
    send_notification "success" "数据备份成功完成"
}

# 执行主函数
main "$@"
