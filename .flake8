[flake8]
# Matches <PERSON>'s default
max-line-length = 88
# Maximum cyclomatic complexity
max-complexity = 10
ignore =
    W503,
    E203,
    W504
exclude = 
    .git,
    __pycache__,
    venv,
    .venv,
    .mypy_cache,
    .tox,
    build,
    dist,
    migrations
per-file-ignores =
    __init__.py: F401
    tests/*: E501
select =
    E,
    W,
    F,
    C,
    B,
    Q
extend-ignore =
    E203,
    E501
# Show verbose output
format = pylama
statistics = True
show_source = True
explicit = True
