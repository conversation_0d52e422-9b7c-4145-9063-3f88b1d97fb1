"""Lint分析错误测试模块

测试LintAnalyzer类的错误处理
"""

import pytest
from api_proxy.job_lint_analysis import LintAnalyzer


class TestLintAnalyzerError:
    """Lint分析器错误测试类"""

    def test_invalid_input_type(self):
        """测试无效输入类型"""
        analyzer = LintAnalyzer()
        with pytest.raises(ValueError):
            analyzer.analyze_log(None)
        with pytest.raises(ValueError):
            analyzer.analyze_log(123)

    def test_malformed_log_lines(self):
        """测试格式错误的日志行"""
        analyzer = LintAnalyzer()
        log = "would reformat\nsrc/utils.py: : E302"

        # 应该忽略格式错误的行而不是报错
        result = analyzer.analyze_log(log)
        assert len(result) == 1  # 只解析第一行有效错误
