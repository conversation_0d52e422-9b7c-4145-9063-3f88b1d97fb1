# OpenRouter配置测试
import pytest
import json
import tempfile
import os
from api_proxy import Config, ProviderConfig
from api_proxy.web_app import WebApp


class TestOpenRouterConfig:
    """OpenRouter配置测试类"""

    def test_config_with_openrouter_basic(self):
        """测试基本OpenRouter配置"""
        config = Config({
            "openrouter": [
                ProviderConfig("test_account", "sk-or-test123", {})
            ]
        })
        
        assert "openrouter" in config.providers
        assert len(config.providers["openrouter"]) == 1
        
        provider_config = config.providers["openrouter"][0]
        assert provider_config.name == "test_account"
        assert provider_config.api_key == "sk-or-test123"
        assert provider_config.config == {}

    def test_config_with_openrouter_full(self):
        """测试完整OpenRouter配置"""
        config = Config({
            "openrouter": [
                ProviderConfig("test_account", "sk-or-test123", {
                    "timeout": 60,
                    "site_url": "https://myapp.com",
                    "site_name": "My App"
                })
            ]
        })
        
        provider_config = config.providers["openrouter"][0]
        assert provider_config.config["timeout"] == 60
        assert provider_config.config["site_url"] == "https://myapp.com"
        assert provider_config.config["site_name"] == "My App"

    def test_config_mixed_providers(self):
        """测试混合提供商配置"""
        config = Config({
            "openai": [
                ProviderConfig("openai_account", "sk-openai-123", {})
            ],
            "openrouter": [
                ProviderConfig("openrouter_account", "sk-or-123", {
                    "site_url": "https://test.com",
                    "site_name": "Test App"
                })
            ]
        })
        
        assert "openai" in config.providers
        assert "openrouter" in config.providers
        assert len(config.providers["openai"]) == 1
        assert len(config.providers["openrouter"]) == 1

    def test_config_multiple_openrouter_instances(self):
        """测试多个OpenRouter实例配置"""
        config = Config({
            "openrouter": [
                ProviderConfig("primary", "sk-or-123", {
                    "timeout": 30,
                    "site_url": "https://app1.com",
                    "site_name": "App 1"
                }),
                ProviderConfig("backup", "sk-or-456", {
                    "timeout": 60,
                    "site_url": "https://app2.com",
                    "site_name": "App 2"
                })
            ]
        })
        
        assert len(config.providers["openrouter"]) == 2
        
        primary = config.providers["openrouter"][0]
        backup = config.providers["openrouter"][1]
        
        assert primary.name == "primary"
        assert primary.config["site_url"] == "https://app1.com"
        assert backup.name == "backup"
        assert backup.config["site_url"] == "https://app2.com"

    def test_web_app_default_config_includes_openrouter(self):
        """测试Web应用默认配置包含OpenRouter"""
        web_app = WebApp()
        
        # 模拟没有配置文件的情况
        default_config = web_app._load_config()
        
        assert "providers" in default_config
        assert "openai" in default_config["providers"]
        assert "openrouter" in default_config["providers"]
        
        openrouter_config = default_config["providers"]["openrouter"][0]
        assert openrouter_config["name"] == "primary"
        assert "sk-or-" in openrouter_config["api_key"]
        assert "config" in openrouter_config
        assert "timeout" in openrouter_config["config"]
        assert "site_url" in openrouter_config["config"]
        assert "site_name" in openrouter_config["config"]

    def test_config_file_save_and_load(self):
        """测试配置文件保存和加载"""
        # 创建临时配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config_data = {
                "providers": {
                    "openrouter": [
                        {
                            "name": "test_instance",
                            "api_key": "sk-or-test123",
                            "config": {
                                "timeout": 45,
                                "site_url": "https://testapp.com",
                                "site_name": "Test Application"
                            }
                        }
                    ]
                },
                "default_timeout": 30,
                "max_retries": 3,
                "health_check_interval": 300,
                "enable_monitoring": True,
                "log_level": "INFO"
            }
            json.dump(config_data, f)
            config_file = f.name

        try:
            # 创建Web应用并指定配置文件
            web_app = WebApp()
            web_app.config_file = config_file
            
            # 加载配置
            loaded_config = web_app._load_config()
            
            assert "openrouter" in loaded_config["providers"]
            openrouter_provider = loaded_config["providers"]["openrouter"][0]
            assert openrouter_provider["name"] == "test_instance"
            assert openrouter_provider["api_key"] == "sk-or-test123"
            assert openrouter_provider["config"]["timeout"] == 45
            assert openrouter_provider["config"]["site_url"] == "https://testapp.com"
            assert openrouter_provider["config"]["site_name"] == "Test Application"
            
        finally:
            # 清理临时文件
            os.unlink(config_file)

    def test_provider_config_validation(self):
        """测试提供商配置验证"""
        # 测试有效配置
        valid_config = ProviderConfig("test", "sk-or-123", {
            "timeout": 30,
            "site_url": "https://valid.com",
            "site_name": "Valid App"
        })
        assert valid_config.name == "test"
        assert valid_config.api_key == "sk-or-123"

        # 测试无效名称
        with pytest.raises(ValueError, match="Provider name must be non-empty string"):
            ProviderConfig("", "sk-or-123", {})

        # 测试无效API密钥
        with pytest.raises(ValueError, match="API key must be non-empty string"):
            ProviderConfig("test", "", {})

        # 测试无效配置类型
        with pytest.raises(ValueError, match="Config must be a dictionary"):
            ProviderConfig("test", "sk-or-123", "invalid")

    def test_openrouter_config_optional_fields(self):
        """测试OpenRouter配置的可选字段"""
        # 只有必需字段
        minimal_config = ProviderConfig("minimal", "sk-or-123", {})
        assert minimal_config.config == {}

        # 部分可选字段
        partial_config = ProviderConfig("partial", "sk-or-123", {
            "site_url": "https://partial.com"
        })
        assert partial_config.config["site_url"] == "https://partial.com"
        assert "site_name" not in partial_config.config

        # 所有字段
        full_config = ProviderConfig("full", "sk-or-123", {
            "timeout": 60,
            "site_url": "https://full.com",
            "site_name": "Full App"
        })
        assert full_config.config["timeout"] == 60
        assert full_config.config["site_url"] == "https://full.com"
        assert full_config.config["site_name"] == "Full App"
