#!/bin/bash
# AI代理服务健康检查脚本
# 检查服务状态、API响应和系统资源

set -euo pipefail

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
SERVICE_URL="${SERVICE_URL:-http://localhost:8080}"
TIMEOUT="${TIMEOUT:-10}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查结果
CHECKS_PASSED=0
CHECKS_FAILED=0
WARNINGS=0

# 日志函数
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    ((CHECKS_FAILED++))
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    ((WARNINGS++))
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    ((CHECKS_PASSED++))
}

# 显示帮助信息
show_help() {
    cat << EOF
AI代理服务健康检查脚本

用法: $0 [选项]

选项:
    -u, --url URL          服务URL (默认: http://localhost:8080)
    -t, --timeout SECONDS  请求超时时间 (默认: 10)
    -v, --verbose          详细输出
    -j, --json             JSON格式输出
    -h, --help             显示此帮助信息

示例:
    $0                     # 检查本地服务
    $0 -u https://api.example.com -t 30  # 检查远程服务
EOF
}

# 解析命令行参数
parse_args() {
    VERBOSE=false
    JSON_OUTPUT=false

    while [[ $# -gt 0 ]]; do
        case $1 in
            -u|--url)
                SERVICE_URL="$2"
                shift 2
                ;;
            -t|--timeout)
                TIMEOUT="$2"
                shift 2
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -j|--json)
                JSON_OUTPUT=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                error "未知参数: $1"
                exit 1
                ;;
        esac
    done
}

# 检查服务基本连通性
check_service_connectivity() {
    log "检查服务连通性..."
    
    if curl -f -s --max-time "$TIMEOUT" "$SERVICE_URL/health" > /dev/null; then
        success "服务连通性正常"
        return 0
    else
        error "服务连通性检查失败"
        return 1
    fi
}

# 检查健康端点
check_health_endpoint() {
    log "检查健康端点..."
    
    local response
    local http_code
    
    response=$(curl -s --max-time "$TIMEOUT" -w "%{http_code}" "$SERVICE_URL/health" || echo "000")
    http_code="${response: -3}"
    response="${response%???}"
    
    if [[ "$http_code" == "200" ]]; then
        success "健康端点响应正常 (HTTP $http_code)"
        
        if [[ "$VERBOSE" == true ]] && [[ -n "$response" ]]; then
            echo "响应内容: $response"
        fi
        
        # 解析健康状态
        if command -v jq &> /dev/null && echo "$response" | jq . &> /dev/null; then
            local status=$(echo "$response" | jq -r '.status // "unknown"')
            if [[ "$status" == "healthy" ]]; then
                success "服务状态: $status"
            else
                warning "服务状态: $status"
            fi
        fi
        
        return 0
    else
        error "健康端点检查失败 (HTTP $http_code)"
        return 1
    fi
}

# 检查API端点
check_api_endpoints() {
    log "检查API端点..."
    
    local endpoints=(
        "/api/stats"
        "/api/providers"
    )
    
    local failed_endpoints=()
    
    for endpoint in "${endpoints[@]}"; do
        local url="$SERVICE_URL$endpoint"
        local http_code
        
        http_code=$(curl -s --max-time "$TIMEOUT" -w "%{http_code}" -o /dev/null "$url" || echo "000")
        
        if [[ "$http_code" =~ ^[23] ]]; then
            [[ "$VERBOSE" == true ]] && success "  $endpoint (HTTP $http_code)"
        else
            failed_endpoints+=("$endpoint")
            [[ "$VERBOSE" == true ]] && error "  $endpoint (HTTP $http_code)"
        fi
    done
    
    if [[ ${#failed_endpoints[@]} -eq 0 ]]; then
        success "所有API端点响应正常"
        return 0
    else
        error "API端点检查失败: ${failed_endpoints[*]}"
        return 1
    fi
}

# 检查Docker容器状态
check_docker_containers() {
    if ! command -v docker &> /dev/null; then
        warning "Docker未安装，跳过容器检查"
        return 0
    fi
    
    log "检查Docker容器状态..."
    
    local containers=(
        "ai-proxy-app"
        "ai-proxy-redis"
        "ai-proxy-nginx"
    )
    
    local failed_containers=()
    
    for container in "${containers[@]}"; do
        if docker ps --format "table {{.Names}}" | grep -q "^$container$"; then
            local status=$(docker inspect --format='{{.State.Status}}' "$container" 2>/dev/null || echo "not_found")
            if [[ "$status" == "running" ]]; then
                [[ "$VERBOSE" == true ]] && success "  $container: $status"
            else
                failed_containers+=("$container:$status")
                [[ "$VERBOSE" == true ]] && error "  $container: $status"
            fi
        else
            failed_containers+=("$container:not_found")
            [[ "$VERBOSE" == true ]] && warning "  $container: 未找到"
        fi
    done
    
    if [[ ${#failed_containers[@]} -eq 0 ]]; then
        success "所有容器运行正常"
        return 0
    else
        warning "部分容器状态异常: ${failed_containers[*]}"
        return 1
    fi
}

# 检查系统资源
check_system_resources() {
    log "检查系统资源..."
    
    # 检查磁盘空间
    local disk_usage
    disk_usage=$(df "$PROJECT_ROOT" | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [[ "$disk_usage" -lt 80 ]]; then
        [[ "$VERBOSE" == true ]] && success "  磁盘使用率: ${disk_usage}%"
    elif [[ "$disk_usage" -lt 90 ]]; then
        warning "磁盘使用率较高: ${disk_usage}%"
    else
        error "磁盘空间不足: ${disk_usage}%"
    fi
    
    # 检查内存使用
    if command -v free &> /dev/null; then
        local mem_usage
        mem_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
        
        if [[ "$mem_usage" -lt 80 ]]; then
            [[ "$VERBOSE" == true ]] && success "  内存使用率: ${mem_usage}%"
        elif [[ "$mem_usage" -lt 90 ]]; then
            warning "内存使用率较高: ${mem_usage}%"
        else
            error "内存使用率过高: ${mem_usage}%"
        fi
    fi
    
    # 检查负载
    if command -v uptime &> /dev/null; then
        local load_avg
        load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
        
        if (( $(echo "$load_avg < 2.0" | bc -l) )); then
            [[ "$VERBOSE" == true ]] && success "  系统负载: $load_avg"
        elif (( $(echo "$load_avg < 4.0" | bc -l) )); then
            warning "系统负载较高: $load_avg"
        else
            error "系统负载过高: $load_avg"
        fi
    fi
}

# 检查配置文件
check_config_files() {
    log "检查配置文件..."
    
    local config_files=(
        "config.json"
        "docker-compose.yml"
    )
    
    local missing_files=()
    
    for file in "${config_files[@]}"; do
        local file_path="$PROJECT_ROOT/$file"
        if [[ -f "$file_path" ]]; then
            # 检查JSON文件格式
            if [[ "$file" == *.json ]] && command -v jq &> /dev/null; then
                if jq . "$file_path" > /dev/null 2>&1; then
                    [[ "$VERBOSE" == true ]] && success "  $file: 格式正确"
                else
                    error "$file: JSON格式错误"
                fi
            else
                [[ "$VERBOSE" == true ]] && success "  $file: 存在"
            fi
        else
            missing_files+=("$file")
        fi
    done
    
    if [[ ${#missing_files[@]} -eq 0 ]]; then
        success "配置文件检查通过"
        return 0
    else
        error "缺少配置文件: ${missing_files[*]}"
        return 1
    fi
}

# 检查日志文件
check_log_files() {
    log "检查日志文件..."
    
    local log_dir="$PROJECT_ROOT/logs"
    
    if [[ ! -d "$log_dir" ]]; then
        warning "日志目录不存在: $log_dir"
        return 1
    fi
    
    # 检查日志文件大小
    local large_logs=()
    while IFS= read -r -d '' log_file; do
        local size_mb
        size_mb=$(du -m "$log_file" | cut -f1)
        if [[ "$size_mb" -gt 100 ]]; then
            large_logs+=("$(basename "$log_file"):${size_mb}MB")
        fi
    done < <(find "$log_dir" -name "*.log" -print0)
    
    if [[ ${#large_logs[@]} -gt 0 ]]; then
        warning "发现大型日志文件: ${large_logs[*]}"
    else
        success "日志文件大小正常"
    fi
    
    # 检查最近的错误日志
    local error_log="$log_dir/error.log"
    if [[ -f "$error_log" ]]; then
        local recent_errors
        recent_errors=$(tail -n 100 "$error_log" | grep -c "ERROR" || echo "0")
        if [[ "$recent_errors" -gt 10 ]]; then
            warning "最近发现 $recent_errors 个错误日志"
        else
            [[ "$VERBOSE" == true ]] && success "  错误日志数量正常: $recent_errors"
        fi
    fi
}

# 生成JSON报告
generate_json_report() {
    local status="healthy"
    if [[ "$CHECKS_FAILED" -gt 0 ]]; then
        status="unhealthy"
    elif [[ "$WARNINGS" -gt 0 ]]; then
        status="warning"
    fi
    
    cat << EOF
{
    "status": "$status",
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "service_url": "$SERVICE_URL",
    "summary": {
        "checks_passed": $CHECKS_PASSED,
        "checks_failed": $CHECKS_FAILED,
        "warnings": $WARNINGS
    },
    "details": {
        "connectivity": $(check_service_connectivity &>/dev/null && echo "true" || echo "false"),
        "health_endpoint": $(check_health_endpoint &>/dev/null && echo "true" || echo "false"),
        "api_endpoints": $(check_api_endpoints &>/dev/null && echo "true" || echo "false"),
        "docker_containers": $(check_docker_containers &>/dev/null && echo "true" || echo "false"),
        "system_resources": $(check_system_resources &>/dev/null && echo "true" || echo "false"),
        "config_files": $(check_config_files &>/dev/null && echo "true" || echo "false"),
        "log_files": $(check_log_files &>/dev/null && echo "true" || echo "false")
    }
}
EOF
}

# 主函数
main() {
    parse_args "$@"
    
    if [[ "$JSON_OUTPUT" == true ]]; then
        generate_json_report
        return
    fi
    
    log "开始AI代理服务健康检查"
    log "服务URL: $SERVICE_URL"
    log "超时时间: ${TIMEOUT}秒"
    echo
    
    cd "$PROJECT_ROOT"
    
    # 执行所有检查
    check_service_connectivity
    check_health_endpoint
    check_api_endpoints
    check_docker_containers
    check_system_resources
    check_config_files
    check_log_files
    
    echo
    log "健康检查完成"
    log "通过: $CHECKS_PASSED, 失败: $CHECKS_FAILED, 警告: $WARNINGS"
    
    # 返回适当的退出码
    if [[ "$CHECKS_FAILED" -gt 0 ]]; then
        exit 1
    elif [[ "$WARNINGS" -gt 0 ]]; then
        exit 2
    else
        exit 0
    fi
}

# 执行主函数
main "$@"
