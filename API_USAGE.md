# AI代理服务 API 使用指南

## 服务地址

假设您的服务运行在：`http://your-server:8000`

## API 端点

### 1. OpenAI 兼容接口

#### 聊天补全 (Chat Completions)

**端点**: `POST /v1/chat/completions`

**请求示例**:
```bash
curl -X POST http://your-server:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -H "X-Provider: openai" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "Hello, how are you?"}
    ],
    "temperature": 0.7,
    "max_tokens": 150
  }'
```

**Python 示例**:
```python
import requests

response = requests.post(
    "http://your-server:8000/v1/chat/completions",
    headers={
        "Content-Type": "application/json",
        "Authorization": "Bearer your-token",
        "X-Provider": "openai"  # 可选，默认为 openai
    },
    json={
        "model": "gpt-3.5-turbo",
        "messages": [
            {"role": "user", "content": "Hello, how are you?"}
        ],
        "temperature": 0.7,
        "max_tokens": 150
    }
)

print(response.json())
```

**使用 OpenAI SDK**:
```python
import openai

# 配置 OpenAI 客户端使用您的代理服务
openai.api_base = "http://your-server:8000/v1"
openai.api_key = "your-token"  # 可选

response = openai.ChatCompletion.create(
    model="gpt-3.5-turbo",
    messages=[
        {"role": "user", "content": "Hello, how are you?"}
    ]
)

print(response.choices[0].message.content)
```

#### 列出模型

**端点**: `GET /v1/models`

```bash
curl http://your-server:8000/v1/models
```

### 2. 通用代理接口

#### 代理请求

**端点**: `POST /api/proxy`

**请求示例**:
```bash
curl -X POST http://your-server:8000/api/proxy \
  -H "Content-Type: application/json" \
  -d '{
    "provider": "openrouter",
    "endpoint": "chat/completions",
    "data": {
      "model": "anthropic/claude-3-sonnet",
      "messages": [
        {"role": "user", "content": "Explain quantum computing"}
      ],
      "max_tokens": 200
    }
  }'
```

**Python 示例**:
```python
import requests

response = requests.post(
    "http://your-server:8000/api/proxy",
    json={
        "provider": "openrouter",
        "endpoint": "chat/completions", 
        "data": {
            "model": "anthropic/claude-3-sonnet",
            "messages": [
                {"role": "user", "content": "Explain quantum computing"}
            ],
            "max_tokens": 200
        }
    }
)

print(response.json())
```

### 3. 服务管理接口

#### 列出提供商

**端点**: `GET /api/providers`

```bash
curl http://your-server:8000/api/providers
```

#### 服务状态

**端点**: `GET /api/service/status`

```bash
curl http://your-server:8000/api/service/status
```

#### 健康检查

**端点**: `GET /api/health`

```bash
curl http://your-server:8000/api/health
```

## 支持的提供商和模型

### OpenAI
- `gpt-4`
- `gpt-4-turbo`
- `gpt-3.5-turbo`
- `gpt-3.5-turbo-16k`

### OpenRouter
- `openai/gpt-4o`
- `openai/gpt-4-turbo`
- `anthropic/claude-3-sonnet`
- `google/gemini-pro`
- `meta-llama/llama-2-70b-chat`

## 请求头说明

- `Authorization`: Bearer token（可选，用于认证）
- `X-Provider`: 指定使用的提供商（openai, openrouter等）
- `Content-Type`: application/json

## 错误处理

### 常见错误码

- `400`: 请求参数错误
- `401`: 认证失败
- `500`: 服务器内部错误
- `503`: 代理服务未启动

### 错误响应格式

```json
{
  "detail": "错误描述信息"
}
```

## 客户端集成示例

### JavaScript/Node.js

```javascript
const axios = require('axios');

async function callAI(message) {
  try {
    const response = await axios.post('http://your-server:8000/v1/chat/completions', {
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: message }]
    }, {
      headers: {
        'Content-Type': 'application/json',
        'X-Provider': 'openai'
      }
    });
    
    return response.data.choices[0].message.content;
  } catch (error) {
    console.error('API调用失败:', error.response?.data || error.message);
  }
}

// 使用示例
callAI('Hello, world!').then(console.log);
```

### PHP

```php
<?php
function callAI($message) {
    $url = 'http://your-server:8000/v1/chat/completions';
    $data = [
        'model' => 'gpt-3.5-turbo',
        'messages' => [
            ['role' => 'user', 'content' => $message]
        ]
    ];
    
    $options = [
        'http' => [
            'header' => [
                'Content-Type: application/json',
                'X-Provider: openai'
            ],
            'method' => 'POST',
            'content' => json_encode($data)
        ]
    ];
    
    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    
    return json_decode($result, true);
}

// 使用示例
$response = callAI('Hello, world!');
echo $response['choices'][0]['message']['content'];
?>
```

### Go

```go
package main

import (
    "bytes"
    "encoding/json"
    "fmt"
    "net/http"
)

type ChatRequest struct {
    Model    string    `json:"model"`
    Messages []Message `json:"messages"`
}

type Message struct {
    Role    string `json:"role"`
    Content string `json:"content"`
}

func callAI(message string) error {
    url := "http://your-server:8000/v1/chat/completions"
    
    reqData := ChatRequest{
        Model: "gpt-3.5-turbo",
        Messages: []Message{
            {Role: "user", Content: message},
        },
    }
    
    jsonData, _ := json.Marshal(reqData)
    
    req, _ := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("X-Provider", "openai")
    
    client := &http.Client{}
    resp, err := client.Do(req)
    if err != nil {
        return err
    }
    defer resp.Body.Close()
    
    // 处理响应...
    return nil
}
```

## 部署建议

### 1. 生产环境启动

```bash
# 使用 gunicorn 启动
gunicorn -w 4 -k uvicorn.workers.UvicornWorker \
  --bind 0.0.0.0:8000 \
  api_proxy.web_app:create_app

# 或使用 uvicorn
uvicorn api_proxy.web_app:create_app --host 0.0.0.0 --port 8000 --workers 4
```

### 2. 反向代理配置

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

### 3. SSL/HTTPS 配置

```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
    }
}
```

## 监控和日志

### 访问日志

```bash
# 查看访问日志
curl http://your-server:8000/api/logs

# 查看服务状态
curl http://your-server:8000/api/service/status
```

这样，其他人就可以通过标准的HTTP API调用您的AI中转服务了！
