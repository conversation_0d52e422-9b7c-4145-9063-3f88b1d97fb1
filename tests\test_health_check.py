"""健康检查测试模块

测试健康检查功能
"""

import time
import pytest
from unittest.mock import MagicMock
from api_proxy.health_check import HealthChecker
from api_proxy.providers.base import BaseProvider


class TestHealthChecker:
    """健康检查测试类"""

    @pytest.fixture
    def mock_provider(self):
        """模拟提供者"""
        provider = MagicMock(spec=BaseProvider)
        provider.call.return_value = {"status": "ok"}
        return provider

    def test_health_check_success(self, mock_provider):
        """测试健康检查成功"""
        checker = HealthChecker(interval=0.1, max_retries=3)
        assert checker.check_provider(mock_provider) is True

    def test_health_check_failure(self, mock_provider):
        """测试健康检查失败"""
        checker = HealthChecker(interval=0.1, max_retries=3)
        mock_provider.call.side_effect = Exception("API error")
        assert checker.check_provider(mock_provider) is False

    def test_retry_mechanism(self, mock_provider):
        """测试重试机制"""
        checker = HealthChecker(interval=0.1, max_retries=3)

        # 第一次失败，第二次成功
        mock_provider.call.side_effect = [Exception("API error"), {"status": "ok"}]
        assert checker.check_provider(mock_provider) is True
        assert mock_provider.call.call_count == 2

    def test_stale_state_handling(self, mock_provider):
        """测试状态过期处理"""
        checker = HealthChecker(interval=0.1, max_retries=3)

        # 标记为不健康
        mock_provider.call.side_effect = Exception("API error")
        assert checker.check_provider(mock_provider) is False

        # 等待状态过期
        time.sleep(0.15)

        # 现在应该重试检查
        mock_provider.call.side_effect = None
        mock_provider.call.return_value = {"status": "ok"}
        assert checker.check_provider(mock_provider) is True
