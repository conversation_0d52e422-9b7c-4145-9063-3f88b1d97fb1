"""提供者边界测试模块

测试API提供商的边界情况处理
"""

import pytest
from datetime import datetime
from unittest.mock import patch
from api_proxy.providers.openai import OpenAIProvider


class TestProviderBoundary:
    """提供者边界测试类"""

    @patch('httpx.post')
    def test_large_response_handling(self, mock_post):
        """测试大响应处理能力"""
        large_response = {"result": "a" * 10_000_000}  # 10MB响应
        mock_post.return_value.status_code = 200
        mock_post.return_value.json.return_value = large_response

        provider = OpenAIProvider("sk-test", timeout=30)
        response = provider.call("completions", prompt="test")
        assert len(response["result"]) == 10_000_000

    @patch('httpx.post')
    def test_slow_response_timeout(self, mock_post):
        """测试慢响应超时处理"""
        mock_post.side_effect = TimeoutError("Request timed out")

        provider = OpenAIProvider("sk-test", timeout=1)
        with pytest.raises(TimeoutError):
            provider.call("completions", prompt="test")

    @pytest.mark.parametrize(
        "invalid_input", [None, "", 123, {"invalid": "input"}, [1, 2, 3]]
    )
    def test_invalid_input_handling(self, invalid_input):
        """测试无效输入处理"""
        provider = OpenAIProvider("sk-test", timeout=10)
        with pytest.raises(ValueError):
            provider.call("completions", prompt=invalid_input)
