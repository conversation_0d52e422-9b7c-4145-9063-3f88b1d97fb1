#!/usr/bin/env python3
"""
快速重启服务脚本
"""

import os
import sys
import time
import signal
import subprocess
from pathlib import Path


def find_service_process():
    """查找正在运行的服务进程"""
    try:
        # 在Windows上使用tasklist，在Unix上使用ps
        if os.name == 'nt':
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                                  capture_output=True, text=True)
            lines = result.stdout.split('\n')
            for line in lines:
                if 'api_proxy' in line:
                    parts = line.split()
                    if len(parts) > 1:
                        return int(parts[1])  # PID
        else:
            result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
            lines = result.stdout.split('\n')
            for line in lines:
                if 'api_proxy' in line and '--web' in line:
                    parts = line.split()
                    if len(parts) > 1:
                        return int(parts[1])  # PID
    except Exception as e:
        print(f"查找进程失败: {e}")
    
    return None


def stop_service():
    """停止服务"""
    pid = find_service_process()
    if pid:
        try:
            print(f"🛑 停止服务进程 (PID: {pid})")
            if os.name == 'nt':
                os.kill(pid, signal.SIGTERM)
            else:
                os.kill(pid, signal.SIGTERM)
            
            # 等待进程结束
            time.sleep(2)
            
            # 检查是否还在运行
            try:
                os.kill(pid, 0)  # 检查进程是否存在
                print("⚠️  进程仍在运行，强制终止")
                os.kill(pid, signal.SIGKILL)
                time.sleep(1)
            except OSError:
                pass  # 进程已经结束
                
            print("✅ 服务已停止")
            return True
        except Exception as e:
            print(f"❌ 停止服务失败: {e}")
            return False
    else:
        print("ℹ️  未找到运行中的服务")
        return True


def start_service():
    """启动服务"""
    try:
        print("🚀 启动服务...")
        
        # 构建启动命令
        cmd = [
            sys.executable, "-m", "api_proxy", "--web",
            "--host", "127.0.0.1",
            "--port", "8000"
        ]
        
        print(f"📝 执行命令: {' '.join(cmd)}")
        
        # 启动服务
        process = subprocess.Popen(cmd)
        
        # 等待一下确保启动
        time.sleep(3)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ 服务启动成功")
            print("🌐 访问地址: http://127.0.0.1:8000")
            print("⏹️  按 Ctrl+C 停止服务")
            
            # 等待用户中断
            try:
                process.wait()
            except KeyboardInterrupt:
                print("\n🛑 用户中断，停止服务")
                process.terminate()
                process.wait()
        else:
            print("❌ 服务启动失败")
            return False
            
    except Exception as e:
        print(f"❌ 启动服务失败: {e}")
        return False
    
    return True


def restart_service():
    """重启服务"""
    print("🔄 重启AI代理服务")
    print("=" * 40)
    
    # 停止现有服务
    if stop_service():
        print()
        # 启动新服务
        start_service()
    else:
        print("❌ 重启失败")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="AI代理服务重启工具")
    parser.add_argument("--stop", action="store_true", help="只停止服务")
    parser.add_argument("--start", action="store_true", help="只启动服务")
    
    args = parser.parse_args()
    
    if args.stop:
        stop_service()
    elif args.start:
        start_service()
    else:
        restart_service()


if __name__ == "__main__":
    main()
