# AI代理服务环境变量配置示例
# 复制此文件为 .env 并填入真实值

# OpenAI API配置
OPENAI_API_KEY_PRIMARY=sk-your-primary-openai-api-key-here
OPENAI_API_KEY_BACKUP=sk-your-backup-openai-api-key-here

# 服务配置
DEFAULT_TIMEOUT=30
MAX_RETRIES=3
HEALTH_CHECK_INTERVAL=300
LOG_LEVEL=INFO

# 监控配置
ENABLE_MONITORING=true
MONITORING_PORT=8080

# 安全配置
ENABLE_API_KEY_ROTATION=false
API_KEY_ROTATION_INTERVAL=86400

# 日志配置
LOG_FILE_PATH=logs/app.log
LOG_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5

# 数据库配置（如果需要）
DATABASE_URL=sqlite:///data/ai_proxy.db

# Redis配置（如果需要缓存）
REDIS_URL=redis://localhost:6379/0

# 开发模式
DEBUG=false
DEVELOPMENT_MODE=false
