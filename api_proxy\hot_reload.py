# 热启动和配置重载模块
import os
import json
import time
import signal
import logging
import threading
from typing import Dict, Any, Optional, Callable
from pathlib import Path
from datetime import datetime

# 尝试导入 watchdog，如果不可用则使用备用方案
try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
    WATCHDOG_AVAILABLE = True
except ImportError:
    WATCHDOG_AVAILABLE = False
    # 创建占位符类
    class FileSystemEventHandler:
        pass
    class Observer:
        pass

from .config import Config
from .models import ProviderConfig


class ConfigFileHandler(FileSystemEventHandler):
    """配置文件变化监听器"""

    def __init__(self, config_path: str, reload_callback: Callable[[Config], None]):
        if WATCHDOG_AVAILABLE:
            super().__init__()
        self.config_path = Path(config_path).resolve()
        self.reload_callback = reload_callback
        self.logger = logging.getLogger(__name__)
        self.last_reload_time = 0
        self.reload_debounce = 2  # 防抖动，2秒内只重载一次
        
    def on_modified(self, event):
        """文件修改事件处理"""
        if event.is_directory:
            return
            
        file_path = Path(event.src_path).resolve()
        if file_path == self.config_path:
            current_time = time.time()
            if current_time - self.last_reload_time > self.reload_debounce:
                self.last_reload_time = current_time
                self._reload_config()
    
    def _reload_config(self):
        """重载配置"""
        try:
            self.logger.info(f"检测到配置文件变化，正在重载: {self.config_path}")
            
            # 读取新配置
            with open(self.config_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 解析配置
            providers = {}
            for provider_type, configs in data.get('providers', {}).items():
                provider_configs = []
                for config in configs:
                    provider_configs.append(ProviderConfig(
                        name=config['name'],
                        api_key=config['api_key'],
                        config=config.get('config', {})
                    ))
                providers[provider_type] = provider_configs
            
            new_config = Config(
                providers=providers,
                default_timeout=data.get('default_timeout', 30),
                max_retries=data.get('max_retries', 3),
                health_check_interval=data.get('health_check_interval', 300),
                enable_monitoring=data.get('enable_monitoring', True),
                log_level=data.get('log_level', 'INFO')
            )
            
            # 调用重载回调
            self.reload_callback(new_config)
            self.logger.info("配置重载成功")
            
        except Exception as e:
            self.logger.error(f"配置重载失败: {e}")


class HotReloadManager:
    """热重载管理器"""
    
    def __init__(self, config_path: str = "config.json"):
        self.config_path = config_path
        self.logger = logging.getLogger(__name__)
        self.observer = None
        self.reload_callbacks = []
        self.is_monitoring = False
        self._lock = threading.Lock()
        
        # 注册信号处理器
        self._setup_signal_handlers()
    
    def add_reload_callback(self, callback: Callable[[Config], None]):
        """添加重载回调函数"""
        with self._lock:
            self.reload_callbacks.append(callback)
    
    def remove_reload_callback(self, callback: Callable[[Config], None]):
        """移除重载回调函数"""
        with self._lock:
            if callback in self.reload_callbacks:
                self.reload_callbacks.remove(callback)
    
    def start_monitoring(self):
        """开始监控配置文件"""
        if self.is_monitoring:
            self.logger.warning("配置文件监控已经在运行")
            return

        if not WATCHDOG_AVAILABLE:
            self.logger.warning("watchdog 模块不可用，文件监控功能已禁用。请安装 watchdog: pip install watchdog")
            return

        try:
            config_dir = Path(self.config_path).parent
            if not config_dir.exists():
                config_dir.mkdir(parents=True, exist_ok=True)

            # 创建文件监听器
            event_handler = ConfigFileHandler(self.config_path, self._on_config_reload)

            # 创建观察者
            self.observer = Observer()
            self.observer.schedule(event_handler, str(config_dir), recursive=False)
            self.observer.start()

            self.is_monitoring = True
            self.logger.info(f"开始监控配置文件: {self.config_path}")

        except Exception as e:
            self.logger.error(f"启动配置文件监控失败: {e}")
            raise
    
    def stop_monitoring(self):
        """停止监控配置文件"""
        if not self.is_monitoring:
            return
        
        try:
            if self.observer:
                self.observer.stop()
                self.observer.join()
                self.observer = None
            
            self.is_monitoring = False
            self.logger.info("配置文件监控已停止")
            
        except Exception as e:
            self.logger.error(f"停止配置文件监控失败: {e}")
    
    def _on_config_reload(self, new_config: Config):
        """配置重载事件处理"""
        with self._lock:
            for callback in self.reload_callbacks:
                try:
                    callback(new_config)
                except Exception as e:
                    self.logger.error(f"重载回调执行失败: {e}")
    
    def manual_reload(self) -> bool:
        """手动触发重载"""
        try:
            if not os.path.exists(self.config_path):
                self.logger.error(f"配置文件不存在: {self.config_path}")
                return False
            
            # 触发重载
            event_handler = ConfigFileHandler(self.config_path, self._on_config_reload)
            event_handler._reload_config()
            return True
            
        except Exception as e:
            self.logger.error(f"手动重载失败: {e}")
            return False
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            if signum == signal.SIGUSR1:  # USR1信号触发重载
                self.logger.info("收到USR1信号，触发配置重载")
                self.manual_reload()
            elif signum == signal.SIGTERM or signum == signal.SIGINT:
                self.logger.info("收到终止信号，停止监控")
                self.stop_monitoring()
        
        # 注册信号处理器（仅在Unix系统上）
        if hasattr(signal, 'SIGUSR1'):
            signal.signal(signal.SIGUSR1, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)
    
    def get_status(self) -> Dict[str, Any]:
        """获取热重载状态"""
        return {
            "monitoring": self.is_monitoring,
            "config_path": self.config_path,
            "config_exists": os.path.exists(self.config_path),
            "config_mtime": os.path.getmtime(self.config_path) if os.path.exists(self.config_path) else None,
            "callbacks_count": len(self.reload_callbacks),
            "watchdog_available": WATCHDOG_AVAILABLE
        }
    
    def __enter__(self):
        """上下文管理器入口"""
        self.start_monitoring()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop_monitoring()


class ServiceReloader:
    """服务重载器 - 管理服务的热重载"""
    
    def __init__(self, service_factory: Callable[[Config], Any]):
        self.service_factory = service_factory
        self.current_service = None
        self.logger = logging.getLogger(__name__)
        self._lock = threading.Lock()
        self.reload_history = []
        
    def reload_service(self, new_config: Config):
        """重载服务"""
        with self._lock:
            try:
                self.logger.info("开始重载服务...")
                
                # 创建新服务实例
                new_service = self.service_factory(new_config)
                
                # 保存旧服务引用（用于清理）
                old_service = self.current_service
                
                # 切换到新服务
                self.current_service = new_service
                
                # 记录重载历史
                self.reload_history.append({
                    "timestamp": datetime.now(),
                    "success": True,
                    "config_hash": hash(str(new_config.providers))
                })
                
                # 清理旧服务资源（如果需要）
                if old_service and hasattr(old_service, 'cleanup'):
                    try:
                        old_service.cleanup()
                    except Exception as e:
                        self.logger.warning(f"清理旧服务资源失败: {e}")
                
                self.logger.info("服务重载成功")
                
            except Exception as e:
                self.logger.error(f"服务重载失败: {e}")
                
                # 记录失败历史
                self.reload_history.append({
                    "timestamp": datetime.now(),
                    "success": False,
                    "error": str(e)
                })
                
                raise
    
    def get_service(self):
        """获取当前服务实例"""
        with self._lock:
            return self.current_service
    
    def get_reload_history(self) -> list:
        """获取重载历史"""
        return self.reload_history[-10:]  # 返回最近10次记录
