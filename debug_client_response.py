#!/usr/bin/env python3
"""
调试客户端响应接收问题
"""

import requests
import json
import time

def test_non_stream_detailed():
    """详细测试非流式响应"""
    print("🧪 详细测试非流式响应...")
    
    test_data = {
        "model": "deepseek/deepseek-chat-v3-0324:free",
        "messages": [{"role": "user", "content": "请回复'测试成功'"}],
        "max_tokens": 20,
        "stream": False
    }
    
    headers = {
        "Content-Type": "application/json",
        "X-Provider": "openrouter"
    }
    
    try:
        print(f"📡 发送请求: {json.dumps(test_data, ensure_ascii=False)}")
        
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            headers=headers,
            json=test_data,
            timeout=30
        )
        
        print(f"\n📥 响应详情:")
        print(f"   状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        print(f"   响应大小: {len(response.content)} bytes")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"   ✅ JSON解析成功")
                print(f"   响应结构: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
                # 检查消息内容
                if 'choices' in result and len(result['choices']) > 0:
                    choice = result['choices'][0]
                    if 'message' in choice:
                        content = choice['message'].get('content', '')
                        print(f"   📝 消息内容: '{content}'")
                        if content.strip():
                            print("   ✅ 客户端应该能收到内容")
                        else:
                            print("   ❌ 消息内容为空")
                    else:
                        print("   ❌ 响应中没有message字段")
                else:
                    print("   ❌ 响应中没有choices字段")
                    
            except json.JSONDecodeError as e:
                print(f"   ❌ JSON解析失败: {e}")
                print(f"   原始响应: {response.text}")
        else:
            print(f"   ❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_stream_detailed():
    """详细测试流式响应"""
    print("\n🧪 详细测试流式响应...")
    
    test_data = {
        "model": "deepseek/deepseek-chat-v3-0324:free",
        "messages": [{"role": "user", "content": "请逐字回复'你好世界'"}],
        "max_tokens": 20,
        "stream": True
    }
    
    headers = {
        "Content-Type": "application/json",
        "X-Provider": "openrouter"
    }
    
    try:
        print(f"📡 发送流式请求: {json.dumps(test_data, ensure_ascii=False)}")
        
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            headers=headers,
            json=test_data,
            timeout=30,
            stream=True
        )
        
        print(f"\n📥 流式响应详情:")
        print(f"   状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print(f"   ✅ 开始接收流式数据...")
            
            collected_content = ""
            chunk_count = 0
            data_count = 0
            
            for line in response.iter_lines(decode_unicode=True):
                if line:
                    chunk_count += 1
                    print(f"   第{chunk_count}行: {repr(line)}")
                    
                    # 解析SSE数据
                    if line.startswith('data: '):
                        data_count += 1
                        data_str = line[6:].strip()
                        
                        if data_str == '[DONE]':
                            print(f"   🏁 流式传输结束")
                            break
                        
                        try:
                            data = json.loads(data_str)
                            print(f"   📦 数据包{data_count}: {json.dumps(data, ensure_ascii=False)}")
                            
                            # 提取内容
                            if 'choices' in data and len(data['choices']) > 0:
                                choice = data['choices'][0]
                                if 'delta' in choice and 'content' in choice['delta']:
                                    content = choice['delta']['content']
                                    if content:
                                        collected_content += content
                                        print(f"   📝 新内容: '{content}'")
                                        
                        except json.JSONDecodeError as e:
                            print(f"   ❌ JSON解析失败: {e}")
                            print(f"   原始数据: {data_str}")
                    
                    # 限制显示行数
                    if chunk_count >= 20:
                        print("   ... (限制显示，继续接收)")
                        # 继续接收但不显示
                        for remaining_line in response.iter_lines(decode_unicode=True):
                            if remaining_line and remaining_line.startswith('data: '):
                                data_str = remaining_line[6:].strip()
                                if data_str == '[DONE]':
                                    break
                                try:
                                    data = json.loads(data_str)
                                    if 'choices' in data and len(data['choices']) > 0:
                                        choice = data['choices'][0]
                                        if 'delta' in choice and 'content' in choice['delta']:
                                            content = choice['delta']['content']
                                            if content:
                                                collected_content += content
                                except:
                                    pass
                        break
            
            print(f"\n📊 流式接收统计:")
            print(f"   总行数: {chunk_count}")
            print(f"   数据包数: {data_count}")
            print(f"   完整内容: '{collected_content}'")
            
            if collected_content.strip():
                print("   ✅ 客户端应该能收到完整内容")
            else:
                print("   ❌ 没有收到任何内容")
                
        else:
            print(f"   ❌ 流式请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 流式请求异常: {e}")

def test_with_curl():
    """显示curl测试命令"""
    print("\n💡 你也可以用curl直接测试:")
    print("=" * 60)
    
    print("1. 非流式测试:")
    print('''curl -X POST http://localhost:8000/v1/chat/completions \\
  -H "Content-Type: application/json" \\
  -H "X-Provider: openrouter" \\
  -d '{
    "model": "deepseek/deepseek-chat-v3-0324:free",
    "messages": [{"role": "user", "content": "测试"}],
    "max_tokens": 10,
    "stream": false
  }' | jq .''')
    
    print("\n2. 流式测试:")
    print('''curl -X POST http://localhost:8000/v1/chat/completions \\
  -H "Content-Type: application/json" \\
  -H "X-Provider: openrouter" \\
  -d '{
    "model": "deepseek/deepseek-chat-v3-0324:free",
    "messages": [{"role": "user", "content": "测试"}],
    "max_tokens": 10,
    "stream": true
  }' \\
  --no-buffer''')
    
    print("=" * 60)

if __name__ == "__main__":
    print("🔍 调试客户端响应接收问题")
    print("=" * 50)
    
    # 测试非流式响应
    test_non_stream_detailed()
    
    # 等待一下
    time.sleep(2)
    
    # 测试流式响应
    test_stream_detailed()
    
    # 显示curl命令
    test_with_curl()
    
    print("\n" + "=" * 50)
    print("🎯 关键检查点:")
    print("1. 非流式响应是否包含完整的message.content")
    print("2. 流式响应是否正确传输所有delta.content")
    print("3. 响应头是否正确设置")
    print("4. 数据格式是否符合OpenAI API标准")
