#!/usr/bin/env python3
"""测试提供商实例数统计功能"""

import requests
import json
import time
from api_proxy import ProxyService, Config, ProviderConfig

def test_provider_instance_count():
    """测试提供商实例数统计"""
    print("🧪 测试提供商实例数统计功能")
    print("=" * 50)
    
    # 1. 测试配置加载
    print("\n1. 测试配置加载...")
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        # 计算实例数
        total_instances = 0
        provider_types = 0
        
        for provider_type, instances in config_data.get('providers', {}).items():
            provider_types += 1
            total_instances += len(instances)
            print(f"   {provider_type}: {len(instances)} 个实例")
        
        print(f"   总计: {provider_types} 个提供商类型, {total_instances} 个实例")
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return
    
    # 2. 测试ProxyService实例计数
    print("\n2. 测试ProxyService实例计数...")
    try:
        # 创建配置对象
        providers = {}
        for provider_type, instances in config_data.get('providers', {}).items():
            providers[provider_type] = []
            for instance in instances:
                provider_config = ProviderConfig(
                    name=instance['name'],
                    api_key=instance['api_key'],
                    config=instance.get('config', {})
                )
                providers[provider_type].append(provider_config)
        
        config = Config(providers)
        service = ProxyService(config)
        
        # 计算实例数
        service_total = sum(len(provider_list) for provider_list in service.providers.values())
        service_types = len(service.providers)
        
        print(f"   ProxyService统计: {service_types} 个提供商类型, {service_total} 个实例")
        
        # 验证数据一致性
        if service_total == total_instances and service_types == provider_types:
            print("✅ 实例计数一致")
        else:
            print(f"❌ 实例计数不一致: 配置({total_instances}) vs 服务({service_total})")
            
    except Exception as e:
        print(f"❌ ProxyService测试失败: {e}")
        return
    
    # 3. 测试Web应用的实例计数方法
    print("\n3. 测试Web应用实例计数方法...")
    try:
        from api_proxy.web_app import WebApp
        
        web_app = WebApp(proxy_service=service)
        web_instance_count = web_app._get_provider_instance_count()
        
        print(f"   WebApp统计: {web_instance_count} 个实例")
        
        if web_instance_count == total_instances:
            print("✅ WebApp实例计数正确")
        else:
            print(f"❌ WebApp实例计数错误: 期望({total_instances}) vs 实际({web_instance_count})")
            
    except Exception as e:
        print(f"❌ WebApp测试失败: {e}")
        return
    
    print("\n🎉 所有测试完成!")

if __name__ == "__main__":
    test_provider_instance_count()
