"""作业失败分析错误测试

测试JobAnalyzer类的错误处理能力
"""

import pytest
from api_proxy.job_failure_analysis import JobAnalyzer


class TestJobAnalyzerError:
    """作业分析器错误测试类"""

    @pytest.fixture
    def analyzer(self):
        return JobAnalyzer()

    @pytest.mark.parametrize("invalid_input", [None, 123, [], {}, True, b"bytes"])
    def test_invalid_input_types(self, analyzer, invalid_input):
        """测试无效输入类型处理"""
        with pytest.raises(ValueError):
            analyzer.analyze_log(invalid_input)

    def test_invalid_error_type_handling(self, analyzer):
        """测试SOLUTIONS字典中没有定义错误类型时的处理"""
        # 篡改SOLUTIONS字典以模拟未定义错误类型的情况
        original = analyzer.SOLUTIONS
        analyzer.SOLUTIONS = {}
        try:
            log = "FAILED test.py"
            results = analyzer.analyze_log(log)
            assert results[0].solution == "请检查日志获取详细信息"
        finally:
            analyzer.SOLUTIONS = original

    def test_sensitive_data_handling(self, analyzer):
        """测试敏感信息过滤"""
        log = "FAILED test.py\nApiKey=sk_test_1234567890"
        results = analyzer.analyze_log(log, redact_sensitive=True)
        assert "sk_test_1234567890" not in str(results)
