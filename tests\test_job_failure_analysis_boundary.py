"""作业失败分析边界测试

测试JobAnalyzer类的边界情况处理
"""

import pytest
from api_proxy.job_failure_analysis import JobAnalyzer


class TestJobAnalyzerBoundary:
    """作业分析器边界测试类"""

    @pytest.fixture
    def analyzer(self):
        return JobAnalyzer()

    @pytest.mark.parametrize(
        "log_text,expected",
        [
            ("", 0),
            ("   ", 0),
            ("\n\n\n", 0),
            ("[INFO] Normal log message", 0),
        ],
    )
    def test_empty_or_irrelevant_logs(self, analyzer, log_text, expected):
        """测试空日志或无错误日志"""
        assert len(analyzer.analyze_log(log_text)) == expected

    def test_large_log_performance(self, analyzer):
        """测试大日志处理性能"""
        large_log = "FAILED test.py\n" * 100_000
        results = analyzer.analyze_log(large_log)
        assert len(results) == 100_000

    def test_partial_matches(self, analyzer):
        """测试部分匹配的日志行"""
        log = "The test FAILED but the message is not complete"
        results = analyzer.analyze_log(log)
        assert len(results) == 1
        assert "检查失败的测试用例" in results[0].solution

    def test_multiple_errors_single_line(self, analyzer):
        """测试单行多错误情况"""
        log = "FAILED test.py ModuleNotFoundError"
        results = analyzer.analyze_log(log)
        assert len(results) >= 1
