#!/usr/bin/env python3
"""
实例级别统计功能测试脚本
演示每个提供商实例的调用次数统计，包括成功和失败
"""

import json
import time
import random
from api_proxy.config import Config
from api_proxy.proxy_service import ProxyService
from api_proxy.models import ProviderConfig


def create_test_config():
    """创建测试配置，包含多个实例"""
    config_data = {
        "providers": {
            "openrouter": [
                {
                    "name": "primary",
                    "api_key": "sk-or-test-key-primary",
                    "config": {
                        "timeout": 30,
                        "site_url": "https://primary.com",
                        "site_name": "Primary Instance"
                    }
                },
                {
                    "name": "backup", 
                    "api_key": "sk-or-test-key-backup",
                    "config": {
                        "timeout": 30,
                        "site_url": "https://backup.com",
                        "site_name": "Backup Instance"
                    }
                },
                {
                    "name": "tertiary",
                    "api_key": "sk-or-test-key-tertiary", 
                    "config": {
                        "timeout": 30,
                        "site_url": "https://tertiary.com",
                        "site_name": "Tertiary Instance"
                    }
                }
            ]
        },
        "default_timeout": 30,
        "max_retries": 3,
        "health_check_interval": 300,
        "enable_monitoring": True,
        "log_level": "INFO"
    }
    
    # 创建配置对象
    providers = {}
    for provider_type, provider_configs in config_data["providers"].items():
        provider_list = []
        for pc in provider_configs:
            provider_list.append(ProviderConfig(
                name=pc["name"],
                api_key=pc["api_key"],
                config=pc.get("config", {})
            ))
        providers[provider_type] = provider_list
    
    return Config(
        providers=providers,
        default_timeout=config_data["default_timeout"],
        max_retries=config_data["max_retries"],
        health_check_interval=config_data["health_check_interval"],
        enable_monitoring=config_data["enable_monitoring"],
        log_level=config_data["log_level"]
    )


def simulate_api_calls(service: ProxyService, num_calls: int = 20):
    """模拟API调用，包括成功和失败的情况"""
    print(f"\n🔄 模拟 {num_calls} 次API调用...")
    print("-" * 50)
    
    for i in range(num_calls):
        try:
            # 模拟不同的响应时间
            response_time = random.uniform(0.1, 2.0)
            
            # 模拟成功和失败（90%成功率）
            if random.random() < 0.9:
                # 模拟成功调用
                print(f"第 {i+1:2d} 次: ", end="")
                provider = service.get_provider("openrouter")
                instance_name = service._get_provider_instance_name("openrouter", provider)
                
                # 记录成功的请求
                service.monitor.record_request(instance_name, response_time, True)
                print(f"✅ {instance_name} - {response_time:.3f}s")
            else:
                # 模拟失败调用
                print(f"第 {i+1:2d} 次: ", end="")
                provider = service.get_provider("openrouter")
                instance_name = service._get_provider_instance_name("openrouter", provider)
                
                # 随机选择错误类型
                error_types = ["TimeoutError", "ConnectionError", "HTTPError", "AuthenticationError"]
                error_type = random.choice(error_types)
                
                # 记录失败的请求
                service.monitor.record_request(instance_name, response_time, False, error_type)
                print(f"❌ {instance_name} - {error_type}")
            
            # 随机延迟
            time.sleep(random.uniform(0.1, 0.3))
            
        except Exception as e:
            print(f"第 {i+1:2d} 次: 异常 - {e}")


def display_instance_stats(service: ProxyService):
    """显示实例级别的详细统计"""
    print("\n📊 实例级别统计报告")
    print("=" * 60)
    
    stats = service.monitor.get_stats()
    
    # 过滤出实例级别的统计
    instance_stats = {k: v for k, v in stats.items() if k != 'summary' and isinstance(v, dict)}
    
    if not instance_stats:
        print("❌ 暂无实例统计数据")
        return
    
    # 按提供商类型分组显示
    grouped_stats = {}
    for instance_name, instance_data in instance_stats.items():
        if ':' in instance_name:
            provider_type, instance_id = instance_name.split(':', 1)
        else:
            provider_type = instance_name
            instance_id = 'default'
        
        if provider_type not in grouped_stats:
            grouped_stats[provider_type] = []
        
        grouped_stats[provider_type].append({
            'instance_id': instance_id,
            'instance_name': instance_name,
            'data': instance_data
        })
    
    for provider_type, instances in grouped_stats.items():
        provider_name = provider_type.upper()
        print(f"\n🔧 {provider_name} 提供商 ({len(instances)} 个实例)")
        print("-" * 40)
        
        for instance in instances:
            data = instance['data']
            instance_id = instance['instance_id']
            
            total_requests = data.get('total_requests', 0)
            successful_requests = data.get('successful_requests', 0)
            failed_requests = data.get('failed_requests', 0)
            success_rate = data.get('success_rate', 0)
            avg_response_time = data.get('avg_response_time', 0)
            min_response_time = data.get('min_response_time', 0)
            max_response_time = data.get('max_response_time', 0)
            error_breakdown = data.get('error_breakdown', {})
            last_request = data.get('last_request', '')
            
            print(f"\n📋 实例: {instance_id}")
            print(f"   总请求数: {total_requests}")
            print(f"   成功请求: {successful_requests} ({success_rate:.1f}%)")
            print(f"   失败请求: {failed_requests}")
            print(f"   平均响应时间: {avg_response_time:.3f}s")
            print(f"   响应时间范围: {min_response_time:.3f}s - {max_response_time:.3f}s")
            
            if error_breakdown:
                print(f"   错误类型分布:")
                for error_type, count in error_breakdown.items():
                    print(f"     - {error_type}: {count} 次")
            
            if last_request:
                print(f"   最后请求时间: {last_request}")
    
    # 显示汇总统计
    summary = stats.get('summary', {})
    if summary:
        print(f"\n📈 汇总统计")
        print("-" * 20)
        print(f"总请求数: {summary.get('total_requests', 0)}")
        print(f"总成功数: {summary.get('total_success', 0)}")
        print(f"整体成功率: {summary.get('overall_success_rate', 0):.1f}%")
        print(f"平均响应时间: {summary.get('avg_time', 0):.3f}s")
        print(f"运行时间: {summary.get('uptime', 'N/A')}")


def test_load_balancing_with_stats():
    """测试负载均衡并显示统计"""
    print("🤖 实例级别统计功能测试")
    print("=" * 60)
    
    # 创建测试配置
    config = create_test_config()
    service = ProxyService(config)
    
    print(f"📋 配置了 {len(service.providers['openrouter'])} 个OpenRouter实例:")
    for i, provider_config in enumerate(config.providers['openrouter']):
        print(f"  {i+1}. {provider_config.name}")
    
    # 模拟API调用
    simulate_api_calls(service, 30)
    
    # 显示详细统计
    display_instance_stats(service)
    
    # 测试统计重置功能
    print(f"\n🔄 测试统计重置功能...")
    service.monitor.reset_stats()
    print("✅ 统计已重置")
    
    # 再次模拟少量调用
    simulate_api_calls(service, 5)
    display_instance_stats(service)


if __name__ == "__main__":
    test_load_balancing_with_stats()
    
    print("\n🎉 测试完成!")
    print("\n💡 提示:")
    print("1. 在Web界面的仪表板中可以查看实时统计")
    print("2. 每个实例的成功/失败次数都会被单独跟踪")
    print("3. 支持按实例重置统计数据")
    print("4. 负载均衡确保请求在实例间均匀分布")
