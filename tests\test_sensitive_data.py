"""敏感数据处理测试模块

测试敏感信息的正确过滤和处理
"""

import pytest
from api_proxy.utils import redact_sensitive_data


class TestSensitiveData:
    """敏感数据处理测试类"""

    @pytest.mark.parametrize(
        "input_text,expected",
        [
            ("ApiKey=sk-test123", "ApiKey=****"),
            ("password=mysecret", "password=****"),
            ("Token: Bearer abc.123.xyz", "Token: ****"),
            ("Authorization: Basic YWxhZGRpbjpvcGVuc2VzYW1l", "Authorization: ****"),
            ("No sensitive data", "No sensitive data"),
        ],
    )
    def test_redaction(self, input_text, expected):
        """测试敏感信息脱敏"""
        assert redact_sensitive_data(input_text) == expected

    def test_multiple_matches(self):
        """测试多条敏感信息脱敏"""
        input_text = "ApiKey=test Key=secret Password=123"
        expected = "ApiKey=**** Key=**** Password=****"
        assert redact_sensitive_data(input_text) == expected

    def test_edge_cases(self):
        """测试边界情况"""
        assert redact_sensitive_data("") == ""
        assert redact_sensitive_data(None) is None
        assert redact_sensitive_data(123) == 123
