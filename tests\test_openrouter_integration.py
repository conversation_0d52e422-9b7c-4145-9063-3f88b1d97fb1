# OpenRouter集成测试
import pytest
from unittest.mock import Mock, patch
from api_proxy import ProxyService, Config, ProviderConfig
from api_proxy.providers.openrouter import OpenRouterProvider


class TestOpenRouterIntegration:
    """OpenRouter集成测试类"""

    def test_config_with_openrouter(self):
        """测试包含OpenRouter的配置"""
        config = Config({
            "openai": [
                ProviderConfig("openai_account", "sk-openai-123", {})
            ],
            "openrouter": [
                ProviderConfig("openrouter_account", "sk-or-123", {
                    "timeout": 60,
                    "site_url": "https://myapp.com",
                    "site_name": "My App"
                })
            ]
        })
        
        assert "openai" in config.providers
        assert "openrouter" in config.providers
        assert len(config.providers["openrouter"]) == 1
        
        openrouter_config = config.providers["openrouter"][0]
        assert openrouter_config.name == "openrouter_account"
        assert openrouter_config.api_key == "sk-or-123"
        assert openrouter_config.config["timeout"] == 60
        assert openrouter_config.config["site_url"] == "https://myapp.com"
        assert openrouter_config.config["site_name"] == "My App"

    def test_proxy_service_creates_openrouter_provider(self):
        """测试代理服务创建OpenRouter提供商"""
        config = Config({
            "openrouter": [
                ProviderConfig("test_account", "sk-or-test123", {
                    "timeout": 45,
                    "site_url": "https://test.com",
                    "site_name": "Test App"
                })
            ]
        })
        
        service = ProxyService(config)
        
        # 验证提供商被正确创建
        assert "openrouter" in service.providers
        assert len(service.providers["openrouter"]) == 1
        
        provider = service.providers["openrouter"][0]
        assert isinstance(provider, OpenRouterProvider)
        assert provider.api_key == "sk-or-test123"
        assert provider.timeout == 45
        assert provider.site_url == "https://test.com"
        assert provider.site_name == "Test App"

    def test_proxy_service_creates_openrouter_provider_minimal_config(self):
        """测试代理服务创建最小配置的OpenRouter提供商"""
        config = Config({
            "openrouter": [
                ProviderConfig("test_account", "sk-or-test123", {})
            ]
        })
        
        service = ProxyService(config)
        provider = service.providers["openrouter"][0]
        
        assert isinstance(provider, OpenRouterProvider)
        assert provider.api_key == "sk-or-test123"
        assert provider.timeout == 30  # 默认值
        assert provider.site_url == ""
        assert provider.site_name == ""

    def test_get_openrouter_provider(self):
        """测试获取OpenRouter提供商"""
        config = Config({
            "openrouter": [
                ProviderConfig("account1", "sk-or-123", {}),
                ProviderConfig("account2", "sk-or-456", {})
            ]
        })
        
        service = ProxyService(config)
        
        # 获取提供商应该成功
        provider = service.get_provider("openrouter")
        assert isinstance(provider, OpenRouterProvider)
        assert provider.api_key in ["sk-or-123", "sk-or-456"]

    @patch('requests.post')
    def test_proxy_service_call_openrouter(self, mock_post):
        """测试通过代理服务调用OpenRouter"""
        # 模拟成功响应
        mock_response = Mock()
        mock_response.json.return_value = {
            "id": "chatcmpl-openrouter123",
            "model": "openai/gpt-4o",
            "choices": [{"message": {"content": "Hello from OpenRouter!"}}],
            "usage": {"total_tokens": 15}
        }
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response

        config = Config({
            "openrouter": [
                ProviderConfig("test_account", "sk-or-test123", {
                    "site_url": "https://myapp.com",
                    "site_name": "My App"
                })
            ]
        })
        
        service = ProxyService(config)
        
        result = service.call("openrouter", "chat/completions",
                             model="openai/gpt-4o",
                             messages=[{"role": "user", "content": "Hello"}])
        
        # 验证请求参数
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        assert call_args[1]['url'] == "https://openrouter.ai/api/v1/chat/completions"
        assert call_args[1]['headers']['Authorization'] == "Bearer sk-or-test123"
        assert call_args[1]['headers']['HTTP-Referer'] == "https://myapp.com"
        assert call_args[1]['headers']['X-Title'] == "My App"
        assert call_args[1]['json']['model'] == "openai/gpt-4o"
        
        # 验证响应
        assert result["id"] == "chatcmpl-openrouter123"
        assert result["model"] == "openai/gpt-4o"

    def test_multiple_providers_mixed(self):
        """测试混合多个提供商类型"""
        config = Config({
            "openai": [
                ProviderConfig("openai1", "sk-openai-123", {}),
                ProviderConfig("openai2", "sk-openai-456", {})
            ],
            "openrouter": [
                ProviderConfig("openrouter1", "sk-or-123", {}),
                ProviderConfig("openrouter2", "sk-or-456", {
                    "timeout": 60,
                    "site_url": "https://test.com",
                    "site_name": "Test"
                })
            ]
        })
        
        service = ProxyService(config)
        
        # 验证两种类型的提供商都被正确创建
        assert "openai" in service.providers
        assert "openrouter" in service.providers
        assert len(service.providers["openai"]) == 2
        assert len(service.providers["openrouter"]) == 2
        
        # 验证可以获取不同类型的提供商
        openai_provider = service.get_provider("openai")
        openrouter_provider = service.get_provider("openrouter")
        
        assert openai_provider.name == "openai"
        assert openrouter_provider.name == "openrouter"

    def test_unsupported_provider_type(self):
        """测试不支持的提供商类型"""
        config = Config({
            "openai": [ProviderConfig("test", "sk-123", {})]
        })
        
        service = ProxyService(config)
        
        with pytest.raises(ValueError, match="提供商类型 'unsupported' 不存在"):
            service.get_provider("unsupported")

    @patch('requests.post')
    def test_openrouter_different_models(self, mock_post):
        """测试OpenRouter调用不同模型"""
        mock_response = Mock()
        mock_response.json.return_value = {"id": "test", "model": "test"}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response

        config = Config({
            "openrouter": [
                ProviderConfig("test", "sk-or-test123", {})
            ]
        })
        
        service = ProxyService(config)
        
        # 测试不同的模型调用
        models_to_test = [
            "openai/gpt-4o",
            "anthropic/claude-3-sonnet",
            "google/gemini-pro",
            "meta-llama/llama-2-70b-chat"
        ]
        
        for model in models_to_test:
            service.call("openrouter", "chat/completions",
                        model=model,
                        messages=[{"role": "user", "content": f"Test {model}"}])
        
        # 验证所有调用都成功
        assert mock_post.call_count == len(models_to_test)
        
        # 验证每次调用的模型参数正确
        for i, model in enumerate(models_to_test):
            call_args = mock_post.call_args_list[i]
            assert call_args[1]['json']['model'] == model
