# AI代理服务 CI/CD 部署指南

本文档介绍AI代理服务的持续集成和持续部署(CI/CD)流程。

## 📋 目录

- [概述](#概述)
- [CI/CD 流程](#cicd-流程)
- [环境配置](#环境配置)
- [部署流程](#部署流程)
- [监控和维护](#监控和维护)
- [故障排除](#故障排除)

## 🎯 概述

AI代理服务采用现代化的CI/CD流程，支持：

- **多平台CI**: GitLab CI 和 GitHub Actions
- **自动化测试**: 单元测试、集成测试、边界测试、错误处理测试
- **代码质量**: 格式检查、静态分析、安全扫描
- **容器化部署**: Docker + Docker Compose
- **环境隔离**: 开发、测试、生产环境分离
- **自动化备份**: 配置和数据文件备份
- **健康监控**: 服务状态和性能监控

## 🔄 CI/CD 流程

### GitLab CI 流程

```mermaid
graph LR
    A[代码提交] --> B[验证阶段]
    B --> C[测试阶段]
    C --> D[安全检查]
    D --> E[构建阶段]
    E --> F[打包阶段]
    F --> G[部署阶段]
    G --> H[通知阶段]
```

#### 阶段说明

1. **验证阶段** (validate)
   - 代码格式检查 (Black, isort)
   - 代码质量检查 (flake8, mypy, bandit)
   - 依赖安全检查 (safety)

2. **测试阶段** (test)
   - 单元测试
   - 集成测试
   - 边界测试
   - 错误处理测试

3. **安全检查** (security)
   - 容器安全扫描 (Trivy)
   - 静态代码分析 (Semgrep)

4. **构建阶段** (build)
   - Python包构建
   - Docker镜像构建

5. **打包阶段** (package)
   - 创建发布包
   - 生成发布说明

6. **部署阶段** (deploy)
   - 测试环境部署
   - 生产环境部署

7. **通知阶段** (notify)
   - 成功/失败通知

### GitHub Actions 流程

支持多Python版本测试 (3.8, 3.9, 3.10, 3.11) 和多平台构建 (linux/amd64, linux/arm64)。

## ⚙️ 环境配置

### 1. 环境变量配置

创建环境配置文件：

```bash
# 测试环境
cp .env.staging.example .env.staging

# 生产环境  
cp .env.production.example .env.production
```

### 2. 必需的环境变量

```bash
# API密钥
OPENAI_API_KEY_PRIMARY=sk-your-openai-key
OPENAI_API_KEY_BACKUP=sk-your-backup-key

# 部署配置
STAGING_HOST=staging.example.com
PRODUCTION_HOST=production.example.com
SSH_PRIVATE_KEY=your-ssh-private-key

# 通知配置
WEBHOOK_URL=https://hooks.slack.com/your-webhook

# 容器注册表
REGISTRY_URL=ghcr.io/your-org
```

### 3. SSH密钥配置

生成部署用的SSH密钥：

```bash
ssh-keygen -t rsa -b 4096 -C "deploy@ai-proxy"
```

将公钥添加到目标服务器的 `~/.ssh/authorized_keys`。

## 🚀 部署流程

### 本地部署

```bash
# 使用Makefile
make deploy-staging    # 部署到测试环境
make deploy-production # 部署到生产环境

# 直接使用脚本
./scripts/deploy.sh staging -v v1.2.3 -b
./scripts/deploy.sh production --rollback
```

### 自动部署

#### GitLab CI

- **测试环境**: 推送到 `main` 分支自动部署
- **生产环境**: 创建标签 (如 `v1.2.3`) 自动部署

```bash
# 创建发布标签
git tag -a v1.2.3 -m "Release v1.2.3"
git push origin v1.2.3
```

#### GitHub Actions

- **测试环境**: 推送到 `main` 分支触发
- **生产环境**: 创建 release 触发

### Docker部署

#### 生产环境

```bash
# 启动服务
docker-compose up -d

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f ai-proxy
```

#### 开发环境

```bash
# 启动开发环境
docker-compose -f docker-compose.dev.yml up -d

# 启动缓存服务
docker-compose -f docker-compose.dev.yml --profile cache up -d
```

## 📊 监控和维护

### 健康检查

```bash
# 本地检查
make health-check

# 详细检查
./scripts/health-check.sh -v

# JSON格式输出
./scripts/health-check.sh -j
```

### 数据备份

```bash
# 手动备份
make backup

# 自定义备份
./scripts/backup.sh -d /opt/backups -r 7 -c
```

### 日志查看

```bash
# Docker日志
make docker-logs

# 应用日志
tail -f logs/app.log

# 错误日志
tail -f logs/error.log
```

### 性能监控

访问监控面板：

- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000 (admin/admin)

## 🔧 故障排除

### 常见问题

#### 1. 部署失败

```bash
# 检查服务状态
./scripts/health-check.sh

# 查看容器日志
docker-compose logs ai-proxy

# 检查配置文件
jq . config.json
```

#### 2. 服务无响应

```bash
# 重启服务
docker-compose restart ai-proxy

# 检查端口占用
netstat -tlnp | grep 8080

# 检查防火墙
sudo ufw status
```

#### 3. 内存不足

```bash
# 检查内存使用
free -h
docker stats

# 清理Docker资源
docker system prune -f
```

#### 4. 磁盘空间不足

```bash
# 检查磁盘使用
df -h

# 清理日志文件
find logs/ -name "*.log" -mtime +7 -delete

# 清理旧备份
find backups/ -name "*.tar.gz" -mtime +30 -delete
```

### 回滚操作

```bash
# 回滚到上一个版本
./scripts/deploy.sh production --rollback

# 回滚到指定版本
docker-compose down
docker pull your-registry/ai-proxy:v1.2.2
docker-compose up -d
```

### 紧急恢复

```bash
# 从备份恢复
cd /opt/backups
tar -xzf ai-proxy_backup_20240101_120000-config.tar.gz
cp config.json /opt/ai-proxy/
docker-compose restart ai-proxy
```

## 📝 最佳实践

### 1. 版本管理

- 使用语义化版本 (Semantic Versioning)
- 主版本号：不兼容的API修改
- 次版本号：向下兼容的功能性新增
- 修订号：向下兼容的问题修正

### 2. 分支策略

- `main`: 生产环境代码
- `develop`: 开发环境代码
- `feature/*`: 功能分支
- `hotfix/*`: 紧急修复分支

### 3. 测试策略

- 单元测试覆盖率 > 80%
- 集成测试覆盖关键流程
- 性能测试验证响应时间
- 安全测试检查漏洞

### 4. 监控告警

- 服务可用性监控
- 响应时间监控
- 错误率监控
- 资源使用监控

### 5. 安全措施

- 定期更新依赖
- 使用最小权限原则
- 加密敏感配置
- 定期安全扫描

## 📞 支持

如有问题，请联系：

- **技术支持**: <EMAIL>
- **运维团队**: <EMAIL>
- **项目文档**: https://docs.ai-proxy.example.com
