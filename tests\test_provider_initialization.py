"""提供商初始化测试

测试提供商的创建和初始化逻辑
"""

import pytest
from api_proxy.providers.openai import OpenAIProvider
from api_proxy.models import ProviderConfig
from api_proxy.proxy_service import ProxyService
from api_proxy.config import Config


class TestProviderInitialization:
    """提供商初始化测试类"""

    def test_openai_provider_init_success(self):
        """测试OpenAI提供商成功初始化"""
        provider = OpenAIProvider(api_key="test-key", timeout=10)
        assert provider.api_key == "test-key"
        assert provider.timeout == 10

    def test_invalid_api_key(self):
        """测试无效API密钥处理"""
        with pytest.raises(ValueError):
            OpenAIProvider(api_key="", timeout=10)

    def test_proxy_service_init(self):
        """测试代理服务初始化"""
        config = Config(
            {
                "openai": [
                    ProviderConfig("account1", "key1", {}),
                    ProviderConfig("account2", "key2", {}),
                ]
            }
        )
        service = ProxyService(config)
        assert len(service.providers["openai"]) == 2
