# AI代理服务 Docker镜像
# 多阶段构建以减小最终镜像大小和提高安全性

# ==================== 构建阶段 ====================
FROM python:3.9-slim as builder

# 设置构建参数
ARG VERSION=latest
ARG BUILD_DATE
ARG VCS_REF

# 安装构建依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 创建虚拟环境
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# 升级pip和安装构建工具
RUN pip install --no-cache-dir --upgrade pip setuptools wheel

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt requirements-dev.txt ./

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制项目文件
COPY . .

# 安装项目
RUN pip install --no-cache-dir .

# ==================== 运行时阶段 ====================
FROM python:3.9-slim as runtime

# 设置标签
LABEL maintainer="AI Proxy Team" \
      version="${VERSION}" \
      description="AI代理服务 - 智能API代理和负载均衡" \
      build-date="${BUILD_DATE}" \
      vcs-ref="${VCS_REF}"

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 创建非root用户
RUN groupadd -r aiproxy && useradd -r -g aiproxy -d /app -s /bin/bash aiproxy

# 复制虚拟环境
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# 设置工作目录
WORKDIR /app

# 复制应用文件
COPY --from=builder /app/api_proxy ./api_proxy
COPY --from=builder /app/config.example.json ./config.example.json
COPY --from=builder /app/README.md ./README.md

# 创建必要的目录
RUN mkdir -p logs data .secrets && \
    chown -R aiproxy:aiproxy /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    APP_ENV=production \
    LOG_LEVEL=INFO

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 切换到非root用户
USER aiproxy

# 暴露端口
EXPOSE 8080

# 启动命令
CMD ["python", "-m", "api_proxy", "--web", "--host", "0.0.0.0", "--port", "8080"]

# ==================== 开发阶段 ====================
FROM runtime as development

# 切换回root用户安装开发工具
USER root

# 安装开发依赖
RUN pip install --no-cache-dir -r requirements-dev.txt

# 安装调试工具
RUN apt-get update && apt-get install -y \
    vim \
    htop \
    net-tools \
    && rm -rf /var/lib/apt/lists/*

# 设置开发环境变量
ENV APP_ENV=development \
    LOG_LEVEL=DEBUG \
    PYTHONDONTWRITEBYTECODE=0

# 切换回应用用户
USER aiproxy

# 开发模式启动命令
CMD ["python", "-m", "api_proxy", "--web", "--host", "0.0.0.0", "--port", "8080", "--reload"]
