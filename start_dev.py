#!/usr/bin/env python3
"""
开发模式启动脚本 - 支持代码热重载

使用 uvicorn 启动服务，支持代码修改自动重载
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path


def check_uvicorn():
    """检查 uvicorn 是否安装"""
    try:
        import uvicorn
        return True
    except ImportError:
        return False


def install_uvicorn():
    """安装 uvicorn"""
    print("🔧 正在安装 uvicorn...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "uvicorn[standard]"])
        print("✅ uvicorn 安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ uvicorn 安装失败")
        return False


def start_dev_server(host="0.0.0.0", port=8000, auto_install=True):
    """启动开发服务器"""
    
    # 检查 uvicorn
    if not check_uvicorn():
        if auto_install:
            if not install_uvicorn():
                print("请手动安装: pip install uvicorn[standard]")
                return False
        else:
            print("❌ 未找到 uvicorn，请安装: pip install uvicorn[standard]")
            return False
    
    # 检查配置文件
    if not Path("config.json").exists():
        print("⚠️  未找到 config.json，将使用默认配置")
        print("💡 建议运行: python start_api_service.py --create-config")
    
    print("🚀 启动开发服务器 (支持代码热重载)")
    print("=" * 50)
    print(f"🌐 服务地址: http://{host}:{port}")
    print(f"📝 应用模块: api_proxy.web_app:app")
    print(f"🔥 热重载: 启用")
    print("⏹️  按 Ctrl+C 停止服务")
    print("=" * 50)
    
    # 构建 uvicorn 命令
    cmd = [
        sys.executable, "-m", "uvicorn",
        "api_proxy.web_app:app",
        "--reload",
        "--host", host,
        "--port", str(port),
        "--log-level", "info"
    ]
    
    try:
        # 启动服务
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\n⏹️  服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AI代理服务开发模式启动器")
    parser.add_argument("--host", default="0.0.0.0", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    parser.add_argument("--no-auto-install", action="store_true", help="不自动安装依赖")
    
    args = parser.parse_args()
    
    print("🔥 AI代理服务 - 开发模式")
    print("支持代码和配置文件热重载")
    
    success = start_dev_server(
        host=args.host,
        port=args.port,
        auto_install=not args.no_auto_install
    )
    
    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()
