# 使用示例
from api_proxy import ProxyService, Config, ProviderConfig

# 配置多个提供商
config = Config(
    {
        "openai": [
            ProviderConfig("openai_account1", "sk-xxx1", {}),
            ProviderConfig("openai_account2", "sk-xxx2", {}),
        ],
        "openrouter": [
            ProviderConfig("openrouter_account1", "sk-or-xxx1", {
                "timeout": 30,
                "site_url": "https://myapp.com",
                "site_name": "My AI App"
            }),
            ProviderConfig("openrouter_account2", "sk-or-xxx2", {
                "timeout": 60,
                "site_url": "https://myapp.com",
                "site_name": "My AI App"
            }),
        ]
    }
)

proxy = ProxyService(config)

# 使用OpenAI提供商
openai_response = proxy.call("openai", "chat/completions",
                            model="gpt-3.5-turbo",
                            messages=[{"role": "user", "content": "Hello from OpenAI!"}])

# 使用OpenRouter提供商 - 支持多种模型
openrouter_response = proxy.call("openrouter", "chat/completions",
                                 model="openai/gpt-4o",  # OpenRouter格式的模型名
                                 messages=[{"role": "user", "content": "Hello from OpenRouter!"}])

# 也可以使用其他OpenRouter支持的模型
claude_response = proxy.call("openrouter", "chat/completions",
                            model="anthropic/claude-3-sonnet",
                            messages=[{"role": "user", "content": "Hello Claude via OpenRouter!"}])
