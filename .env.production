# AI代理服务生产环境配置
# 请确保所有敏感信息都已正确配置

# ==================== 应用配置 ====================
APP_ENV=production
APP_PORT=8080
LOG_LEVEL=INFO
DEBUG=false

# ==================== API配置 ====================
# OpenAI API密钥 (生产环境必须使用真实密钥)
OPENAI_API_KEY_PRIMARY=sk-your-production-openai-api-key-here
OPENAI_API_KEY_BACKUP=sk-your-backup-production-openai-api-key-here

# API配置
DEFAULT_TIMEOUT=30
MAX_RETRIES=3
HEALTH_CHECK_INTERVAL=300

# ==================== 存储配置 ====================
# 数据文件存储路径
DATA_DIR=./data
CONFIG_FILE=./config.json
STATS_FILE=./stats.json
AUTH_FILE=./auth_config.json

# 备份配置
BACKUP_DIR=./backups
BACKUP_INTERVAL=3600

# Redis缓存配置 (可选，用于提升性能)
REDIS_PASSWORD=CHANGE_THIS_REDIS_PASSWORD_IN_PRODUCTION
REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0

# ==================== Docker配置 ====================
# 镜像注册表
REGISTRY_URL=ghcr.io/your-org
VERSION=latest

# 容器配置
COMPOSE_PROJECT_NAME=ai-proxy-production

# ==================== 网络配置 ====================
# Nginx端口
NGINX_HTTP_PORT=80
NGINX_HTTPS_PORT=443

# 监控端口 (生产环境建议使用内网访问)
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000

# ==================== 监控配置 ====================
ENABLE_MONITORING=true
GRAFANA_PASSWORD=CHANGE_THIS_GRAFANA_PASSWORD_IN_PRODUCTION

# ==================== 安全配置 ====================
ENABLE_API_KEY_ROTATION=true
API_KEY_ROTATION_INTERVAL=86400

# JWT密钥 (生产环境必须使用强密钥)
JWT_SECRET_KEY=CHANGE_THIS_JWT_SECRET_KEY_IN_PRODUCTION_USE_STRONG_RANDOM_STRING

# ==================== 日志配置 ====================
LOG_FILE_PATH=logs/app.log
LOG_MAX_SIZE=50MB
LOG_BACKUP_COUNT=10

# ==================== 通知配置 ====================
# 企业微信/钉钉/Slack Webhook URL
WEBHOOK_URL=https://hooks.slack.com/services/YOUR/PRODUCTION/WEBHOOK

# 邮件配置
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=CHANGE_THIS_SMTP_PASSWORD
SMTP_FROM=AI代理服务 <<EMAIL>>

# ==================== 部署配置 ====================
# 部署主机
PRODUCTION_HOST=ai-proxy.example.com

# SSH配置
SSH_USER=deploy
SSH_PORT=22

# 备份配置
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=ai-proxy-production-backups

# ==================== 性能配置 ====================
# 热重载 (生产环境禁用)
ENABLE_HOT_RELOAD=false

# 调试配置 (生产环境禁用)
ENABLE_DEBUG=false
ENABLE_PROFILING=false

# 缓存配置
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# 连接池配置
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
REDIS_POOL_SIZE=10

# ==================== 第三方服务配置 ====================
# Sentry错误追踪
SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_ENVIRONMENT=production

# 分析服务
ANALYTICS_API_KEY=your-production-analytics-api-key

# CDN配置
CDN_URL=https://cdn.example.com

# ==================== 功能开关 ====================
# 功能标志 (生产环境谨慎开启新功能)
FEATURE_NEW_UI=false
FEATURE_ADVANCED_ANALYTICS=true
FEATURE_RATE_LIMITING=true

# 实验性功能 (生产环境建议禁用)
EXPERIMENTAL_FEATURES=false

# ==================== 限流配置 ====================
# API限流
RATE_LIMIT_PER_MINUTE=1000
RATE_LIMIT_PER_HOUR=10000
RATE_LIMIT_PER_DAY=100000

# 并发限制
MAX_CONCURRENT_REQUESTS=100
MAX_QUEUE_SIZE=1000

# ==================== SSL/TLS配置 ====================
# SSL证书路径
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# SSL配置
SSL_PROTOCOLS=TLSv1.2 TLSv1.3
SSL_CIPHERS=ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512

# ==================== 合规配置 ====================
# 数据保留策略
DATA_RETENTION_DAYS=365
LOG_RETENTION_DAYS=90

# 隐私配置
ENABLE_DATA_ANONYMIZATION=true
ENABLE_GDPR_COMPLIANCE=true

# 审计配置
ENABLE_AUDIT_LOGGING=true
AUDIT_LOG_LEVEL=INFO
