"""API代理监控模块
记录和分析API请求指标，包括:
- 成功率
- 响应时间
- 流量分布
- 错误统计
- 数据持久化
"""

from typing import Dict, Optional, Any, List
import time
import json
import os
import threading
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from collections import defaultdict
from pathlib import Path


class DateTimeEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理datetime对象"""
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)


@dataclass
class RequestStats:
    """请求统计数据结构"""
    total: int = 0
    success: int = 0
    failed: int = 0
    total_time: float = 0.0
    min_time: float = float('inf')
    max_time: float = 0.0
    last_request: Optional[datetime] = None
    error_count: Dict[str, int] = None
    model_count: Dict[str, int] = None  # 模型使用统计
    endpoint_count: Dict[str, int] = None  # 端点使用统计

    def __post_init__(self):
        if self.error_count is None:
            self.error_count = defaultdict(int)
        if self.model_count is None:
            self.model_count = defaultdict(int)
        if self.endpoint_count is None:
            self.endpoint_count = defaultdict(int)

    @property
    def success_rate(self) -> float:
        """成功率"""
        return (self.success / self.total * 100) if self.total > 0 else 0.0

    @property
    def avg_time(self) -> float:
        """平均响应时间"""
        return self.total_time / self.total if self.total > 0 else 0.0

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典，用于JSON序列化"""
        return {
            'total': self.total,
            'success': self.success,
            'failed': self.failed,
            'total_time': self.total_time,
            'min_time': self.min_time if self.min_time != float('inf') else 0.0,
            'max_time': self.max_time,
            'last_request': self.last_request.isoformat() if self.last_request else None,
            'error_count': dict(self.error_count),
            'model_count': dict(self.model_count),
            'endpoint_count': dict(self.endpoint_count)
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RequestStats':
        """从字典创建实例，用于JSON反序列化"""
        stats = cls()
        stats.total = data.get('total', 0)
        stats.success = data.get('success', 0)
        stats.failed = data.get('failed', 0)
        stats.total_time = data.get('total_time', 0.0)
        stats.min_time = data.get('min_time', 0.0)
        if stats.min_time == 0.0 and stats.total == 0:
            stats.min_time = float('inf')
        stats.max_time = data.get('max_time', 0.0)

        last_request_str = data.get('last_request')
        if last_request_str:
            try:
                stats.last_request = datetime.fromisoformat(last_request_str.replace('Z', '+00:00'))
            except:
                stats.last_request = None

        error_count = data.get('error_count', {})
        stats.error_count = defaultdict(int, error_count)

        model_count = data.get('model_count', {})
        stats.model_count = defaultdict(int, model_count)

        endpoint_count = data.get('endpoint_count', {})
        stats.endpoint_count = defaultdict(int, endpoint_count)

        return stats


class RequestMonitor:
    """请求监控统计

    跟踪指标:
    - 总请求数
    - 成功数和失败数
    - 响应时间统计
    - 错误类型统计
    - 最后请求时间
    - 数据持久化
    """

    def __init__(self, stats_file: str = "stats.json", auto_save_interval: int = 60):
        """初始化监控器

        Args:
            stats_file: 统计数据文件路径
            auto_save_interval: 自动保存间隔(秒)，0表示禁用自动保存
        """
        self.stats: Dict[str, RequestStats] = {}
        self.start_time = datetime.now()
        self.recent_requests: List[Dict[str, Any]] = []
        self.max_recent_requests = 1000  # 保留最近1000个请求记录

        # 持久化相关
        self.stats_file = stats_file
        self.auto_save_interval = auto_save_interval
        self._lock = threading.RLock()  # 用于线程安全
        self._last_save_time = time.time()

        # 创建统计目录
        stats_dir = Path(stats_file).parent
        stats_dir.mkdir(exist_ok=True)

        # 加载现有统计数据
        self._load_stats()

    def _load_stats(self):
        """从文件加载统计数据"""
        try:
            if os.path.exists(self.stats_file):
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 恢复统计数据
                for provider_name, stats_data in data.get('stats', {}).items():
                    self.stats[provider_name] = RequestStats.from_dict(stats_data)

                # 恢复开始时间
                start_time_str = data.get('start_time')
                if start_time_str:
                    try:
                        self.start_time = datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
                    except:
                        pass

                # 恢复最近请求记录，处理时间戳
                recent_requests = data.get('recent_requests', [])
                processed_requests = []
                for request in recent_requests:
                    processed_request = request.copy()
                    # 将字符串时间戳转换回datetime对象
                    if 'timestamp' in processed_request and isinstance(processed_request['timestamp'], str):
                        try:
                            processed_request['timestamp'] = datetime.fromisoformat(processed_request['timestamp'].replace('Z', '+00:00'))
                        except:
                            # 如果转换失败，使用当前时间
                            processed_request['timestamp'] = datetime.now()
                    processed_requests.append(processed_request)

                self.recent_requests = processed_requests[-self.max_recent_requests:]

                print(f"✅ 已加载统计数据: {len(self.stats)} 个提供商, {sum(s.total for s in self.stats.values())} 个请求")
        except Exception as e:
            print(f"⚠️  加载统计数据失败: {e}")

    def _save_stats(self, force: bool = False):
        """保存统计数据到文件

        Args:
            force: 是否强制保存，忽略时间间隔
        """
        current_time = time.time()

        # 检查是否需要保存
        if not force and self.auto_save_interval > 0:
            if current_time - self._last_save_time < self.auto_save_interval:
                return

        try:
            with self._lock:
                # 准备保存数据
                # 处理最近请求记录，确保datetime对象被序列化
                serialized_recent_requests = []
                for request in self.recent_requests[-100:]:  # 只保存最近100个请求
                    serialized_request = request.copy()
                    if 'timestamp' in serialized_request and isinstance(serialized_request['timestamp'], datetime):
                        serialized_request['timestamp'] = serialized_request['timestamp'].isoformat()
                    serialized_recent_requests.append(serialized_request)

                save_data = {
                    'start_time': self.start_time.isoformat(),
                    'last_save_time': datetime.now().isoformat(),
                    'stats': {
                        provider_name: stats.to_dict()
                        for provider_name, stats in self.stats.items()
                    },
                    'recent_requests': serialized_recent_requests,
                    'metadata': {
                        'version': '1.0',
                        'total_providers': len(self.stats),
                        'total_requests': sum(s.total for s in self.stats.values())
                    }
                }

                # 原子写入：先写入临时文件，再重命名
                temp_file = f"{self.stats_file}.tmp"
                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(save_data, f, indent=2, ensure_ascii=False, cls=DateTimeEncoder)

                # 原子重命名
                if os.path.exists(self.stats_file):
                    backup_file = f"{self.stats_file}.backup"
                    os.replace(self.stats_file, backup_file)

                os.replace(temp_file, self.stats_file)
                self._last_save_time = current_time

        except Exception as e:
            print(f"❌ 保存统计数据失败: {e}")

    def record_request(self, provider: str, duration: float, success: bool, error_type: Optional[str] = None,
                      model: Optional[str] = None, endpoint: Optional[str] = None):
        """记录请求统计

        Args:
            provider: 提供商名称
            duration: 请求耗时(秒)
            success: 是否成功
            error_type: 错误类型(如果失败)
            model: 使用的模型名称
            endpoint: 调用的端点
        """
        if not isinstance(provider, str) or not provider.strip():
            raise ValueError("提供商名称必须是非空字符串")
        if not isinstance(duration, (int, float)) or duration < 0:
            raise ValueError("请求耗时必须是非负数")

        # 初始化提供商统计
        if provider not in self.stats:
            self.stats[provider] = RequestStats()

        stats = self.stats[provider]
        current_time = datetime.now()

        # 更新统计
        stats.total += 1
        stats.total_time += duration
        stats.last_request = current_time

        if success:
            stats.success += 1
        else:
            stats.failed += 1
            if error_type:
                stats.error_count[error_type] += 1

        # 更新时间范围
        stats.min_time = min(stats.min_time, duration)
        stats.max_time = max(stats.max_time, duration)

        # 记录模型使用统计
        if model:
            stats.model_count[model] += 1

        # 记录端点使用统计
        if endpoint:
            stats.endpoint_count[endpoint] += 1

        # 记录最近请求
        request_record = {
            'provider': provider,
            'timestamp': current_time,
            'duration': duration,
            'success': success,
            'error_type': error_type,
            'model': model,
            'endpoint': endpoint
        }

        self.recent_requests.append(request_record)

        # 保持最近请求列表大小
        if len(self.recent_requests) > self.max_recent_requests:
            self.recent_requests = self.recent_requests[-self.max_recent_requests:]

        # 自动保存（如果启用）
        if self.auto_save_interval > 0:
            self._save_stats()

    def get_stats(self, provider: Optional[str] = None) -> Dict[str, Any]:
        """获取统计信息

        Args:
            provider: 提供商名称，None表示获取所有统计

        Returns:
            Dict: 统计信息字典
        """
        if provider:
            if provider not in self.stats:
                return {}

            stats = self.stats[provider]
            return {
                'provider': provider,
                'total_requests': stats.total,
                'successful_requests': stats.success,
                'failed_requests': stats.failed,
                'success_rate': round(stats.success_rate, 2),
                'avg_response_time': round(stats.avg_time, 3),
                'min_response_time': round(stats.min_time, 3) if stats.min_time != float('inf') else 0,
                'max_response_time': round(stats.max_time, 3),
                'total_time': round(stats.total_time, 3),
                'last_request': stats.last_request.isoformat() if stats.last_request else None,
                'error_breakdown': dict(stats.error_count),
                'model_usage': dict(stats.model_count),
                'endpoint_usage': dict(stats.endpoint_count)
            }

        # 返回所有提供商的统计
        all_stats = {}
        total_requests = 0
        total_success = 0
        total_time = 0.0

        for provider_name, stats in self.stats.items():
            all_stats[provider_name] = self.get_stats(provider_name)
            total_requests += stats.total
            total_success += stats.success
            total_time += stats.total_time

        # 添加汇总统计
        all_stats['summary'] = {
            'total_requests': total_requests,
            'total_success': total_success,
            'overall_success_rate': round((total_success / total_requests * 100) if total_requests > 0 else 0, 2),
            'total_time': round(total_time, 3),
            'avg_time': round(total_time / total_requests, 3) if total_requests > 0 else 0,
            'uptime': str(datetime.now() - self.start_time),
            'provider_count': len(self.stats)
        }

        return all_stats

    def get_recent_requests(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取最近的请求记录

        Args:
            limit: 返回记录数量限制

        Returns:
            List: 最近的请求记录列表
        """
        return self.recent_requests[-limit:] if limit > 0 else self.recent_requests

    def reset_stats(self, provider: Optional[str] = None):
        """重置统计信息

        Args:
            provider: 提供商名称，None表示重置所有统计
        """
        with self._lock:
            if provider:
                if provider in self.stats:
                    self.stats[provider] = RequestStats()
            else:
                self.stats.clear()
                self.recent_requests.clear()
                self.start_time = datetime.now()

            # 立即保存重置后的状态
            self._save_stats(force=True)

    def get_error_summary(self) -> Dict[str, int]:
        """获取错误类型汇总

        Returns:
            Dict: 错误类型及其出现次数
        """
        error_summary = defaultdict(int)
        for stats in self.stats.values():
            for error_type, count in stats.error_count.items():
                error_summary[error_type] += count
        return dict(error_summary)

    def save_now(self):
        """立即保存统计数据"""
        self._save_stats(force=True)

    def cleanup(self):
        """清理资源，保存最终数据"""
        print("💾 保存监控统计数据...")
        self._save_stats(force=True)

    def get_stats_file_info(self) -> Dict[str, Any]:
        """获取统计文件信息"""
        try:
            if os.path.exists(self.stats_file):
                stat = os.stat(self.stats_file)
                return {
                    'file_path': self.stats_file,
                    'file_size': stat.st_size,
                    'last_modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                    'exists': True
                }
            else:
                return {
                    'file_path': self.stats_file,
                    'exists': False
                }
        except Exception as e:
            return {
                'file_path': self.stats_file,
                'error': str(e),
                'exists': False
            }
