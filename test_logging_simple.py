#!/usr/bin/env python3
"""
简单的日志测试脚本
"""

import requests
import json
import time

def test_api_call():
    """测试API调用并观察日志"""
    
    print("🧪 测试API调用...")
    
    # 测试数据
    test_data = {
        "model": "deepseek/deepseek-chat-v3-0324:free",
        "messages": [
            {"role": "user", "content": "Hello! Please respond with just 'Hi!'"}
        ],
        "max_tokens": 5,
        "temperature": 0.1
    }
    
    headers = {
        "Content-Type": "application/json",
        "X-Provider": "openrouter"
    }
    
    try:
        print("📡 发送请求到本地服务...")
        print(f"   URL: http://localhost:8000/v1/chat/completions")
        print(f"   Headers: {headers}")
        print(f"   Data: {json.dumps(test_data, indent=2)}")
        
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            headers=headers,
            json=test_data,
            timeout=30
        )
        
        print(f"\n📥 收到响应:")
        print(f"   状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
            print("✅ 请求成功")
        else:
            print(f"   错误响应: {response.text}")
            print("❌ 请求失败")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_failed_call():
    """测试一个会失败的API调用"""
    
    print("\n🧪 测试失败的API调用...")
    
    # 使用错误的模型名称
    test_data = {
        "model": "invalid/model-name",
        "messages": [
            {"role": "user", "content": "This should fail"}
        ],
        "max_tokens": 5
    }
    
    headers = {
        "Content-Type": "application/json",
        "X-Provider": "openrouter"
    }
    
    try:
        print("📡 发送失败测试请求...")
        
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            headers=headers,
            json=test_data,
            timeout=30
        )
        
        print(f"\n📥 收到响应:")
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.text}")
        
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    print("🔍 API日志测试")
    print("=" * 50)
    print("请确保服务正在运行: python start_api_service.py")
    print("然后观察控制台和日志文件中的详细信息")
    print("=" * 50)
    
    # 等待用户确认
    input("按回车键开始测试...")
    
    # 测试成功的调用
    test_api_call()
    
    # 等待一下
    time.sleep(2)
    
    # 测试失败的调用
    test_failed_call()
    
    print("\n" + "=" * 50)
    print("测试完成！请检查:")
    print("1. 控制台输出中的详细日志")
    print("2. logs/app.log 文件中的日志记录")
    print("3. 成功和失败请求的差异")
