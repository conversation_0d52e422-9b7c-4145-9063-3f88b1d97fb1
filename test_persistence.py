#!/usr/bin/env python3
"""
统计数据持久化功能测试脚本
演示统计数据的保存和加载功能
"""

import json
import time
import random
import os
from api_proxy.monitoring import RequestMonitor


def test_persistence():
    """测试持久化功能"""
    print("💾 统计数据持久化功能测试")
    print("=" * 50)
    
    # 清理旧的测试文件
    test_stats_file = "test_stats.json"
    if os.path.exists(test_stats_file):
        os.remove(test_stats_file)
        print(f"🗑️  已清理旧的测试文件: {test_stats_file}")
    
    # 创建监控器（启用自动保存，间隔5秒）
    monitor = RequestMonitor(stats_file=test_stats_file, auto_save_interval=5)
    
    print(f"\n📊 创建监控器，统计文件: {test_stats_file}")
    print(f"🔄 自动保存间隔: 5秒")
    
    # 模拟一些请求
    providers = ["openrouter:primary", "openrouter:backup", "openai:main"]
    
    print(f"\n🔄 模拟API请求...")
    for i in range(20):
        provider = random.choice(providers)
        duration = random.uniform(0.1, 2.0)
        success = random.random() > 0.1  # 90% 成功率
        error_type = None if success else random.choice(["TimeoutError", "ConnectionError"])
        
        monitor.record_request(provider, duration, success, error_type)
        print(f"第 {i+1:2d} 次: {provider} - {'✅' if success else '❌'} ({duration:.3f}s)")
        
        time.sleep(0.1)
    
    # 显示统计
    print(f"\n📈 当前统计:")
    stats = monitor.get_stats()
    for provider_name, provider_stats in stats.items():
        if provider_name != 'summary':
            print(f"  {provider_name}: {provider_stats['total_requests']} 请求, "
                  f"{provider_stats['success_rate']:.1f}% 成功率")
    
    # 手动保存
    print(f"\n💾 手动保存统计数据...")
    monitor.save_now()
    
    # 检查文件
    file_info = monitor.get_stats_file_info()
    print(f"📁 统计文件信息:")
    print(f"  路径: {file_info['file_path']}")
    print(f"  大小: {file_info.get('file_size', 0)} 字节")
    print(f"  存在: {file_info['exists']}")
    
    # 查看文件内容
    if os.path.exists(test_stats_file):
        with open(test_stats_file, 'r', encoding='utf-8') as f:
            saved_data = json.load(f)
        
        print(f"\n📄 保存的数据结构:")
        print(f"  提供商数量: {saved_data['metadata']['total_providers']}")
        print(f"  总请求数: {saved_data['metadata']['total_requests']}")
        print(f"  保存时间: {saved_data['last_save_time']}")
    
    # 测试重新加载
    print(f"\n🔄 测试重新加载...")
    
    # 创建新的监控器实例，应该自动加载数据
    monitor2 = RequestMonitor(stats_file=test_stats_file, auto_save_interval=0)
    
    stats2 = monitor2.get_stats()
    print(f"📊 重新加载后的统计:")
    for provider_name, provider_stats in stats2.items():
        if provider_name != 'summary':
            print(f"  {provider_name}: {provider_stats['total_requests']} 请求, "
                  f"{provider_stats['success_rate']:.1f}% 成功率")
    
    # 验证数据一致性
    original_total = stats['summary']['total_requests']
    reloaded_total = stats2['summary']['total_requests']
    
    if original_total == reloaded_total:
        print(f"✅ 数据一致性验证通过: {original_total} == {reloaded_total}")
    else:
        print(f"❌ 数据一致性验证失败: {original_total} != {reloaded_total}")
    
    # 测试增量更新
    print(f"\n➕ 测试增量更新...")
    monitor2.record_request("openrouter:primary", 0.5, True)
    monitor2.record_request("openrouter:primary", 1.2, False, "TimeoutError")
    
    stats3 = monitor2.get_stats()
    new_total = stats3['summary']['total_requests']
    print(f"📊 增量更新后总请求数: {new_total} (增加了 {new_total - reloaded_total})")
    
    # 手动保存增量数据
    monitor2.save_now()
    
    # 清理
    monitor.cleanup()
    monitor2.cleanup()
    
    print(f"\n🧹 清理测试文件...")
    if os.path.exists(test_stats_file):
        os.remove(test_stats_file)
    if os.path.exists(f"{test_stats_file}.backup"):
        os.remove(f"{test_stats_file}.backup")


def test_auto_save():
    """测试自动保存功能"""
    print(f"\n⏰ 测试自动保存功能")
    print("-" * 30)
    
    test_file = "test_auto_save.json"
    if os.path.exists(test_file):
        os.remove(test_file)
    
    # 创建监控器，自动保存间隔2秒
    monitor = RequestMonitor(stats_file=test_file, auto_save_interval=2)
    
    print(f"📊 创建监控器，自动保存间隔: 2秒")
    
    # 记录一些请求
    for i in range(5):
        monitor.record_request("test:provider", 0.5, True)
        print(f"第 {i+1} 次请求已记录")
        time.sleep(0.5)
    
    # 等待自动保存
    print(f"⏳ 等待自动保存...")
    time.sleep(3)
    
    # 检查文件是否存在
    if os.path.exists(test_file):
        print(f"✅ 自动保存成功，文件已创建")
        file_info = monitor.get_stats_file_info()
        print(f"📁 文件大小: {file_info.get('file_size', 0)} 字节")
    else:
        print(f"❌ 自动保存失败，文件不存在")
    
    # 清理
    monitor.cleanup()
    if os.path.exists(test_file):
        os.remove(test_file)


def show_persistence_info():
    """显示持久化功能说明"""
    print("\n📚 统计数据持久化功能说明")
    print("=" * 50)
    
    info = """
💾 持久化方案: JSON文件

📋 保存内容:
- 每个提供商实例的详细统计
- 成功/失败次数和响应时间
- 错误类型分布
- 最近请求记录
- 服务运行时间

🔄 保存时机:
- 自动保存: 每60秒自动保存一次
- 手动保存: 调用save_now()方法
- 服务关闭: 自动保存最终数据
- 统计重置: 立即保存重置状态

📁 文件结构:
{
  "start_time": "2024-01-01T00:00:00",
  "last_save_time": "2024-01-01T01:00:00", 
  "stats": {
    "openrouter:primary": {
      "total": 100,
      "success": 95,
      "failed": 5,
      "success_rate": 95.0,
      "avg_response_time": 0.85,
      "error_count": {"TimeoutError": 3, "ConnectionError": 2}
    }
  },
  "recent_requests": [...],
  "metadata": {
    "version": "1.0",
    "total_providers": 3,
    "total_requests": 500
  }
}

🔒 数据安全:
- 原子写入: 先写临时文件再重命名
- 备份机制: 保留上一版本作为备份
- 线程安全: 使用锁保护并发访问
- 错误恢复: 加载失败时使用默认值

✅ 优势:
- 重启后数据不丢失
- 长期趋势分析
- 性能监控历史
- 故障排查依据
    """
    
    print(info)


if __name__ == "__main__":
    print("🤖 AI代理服务 - 统计数据持久化测试")
    print("=" * 60)
    
    # 显示功能说明
    show_persistence_info()
    
    # 测试持久化功能
    test_persistence()
    
    # 测试自动保存
    test_auto_save()
    
    print("\n🎉 测试完成!")
    print("\n💡 提示:")
    print("1. 统计数据会自动保存到 stats.json 文件")
    print("2. 重启服务后会自动加载历史统计")
    print("3. 支持手动保存和重置功能")
    print("4. 在Web界面中可以查看文件信息")
