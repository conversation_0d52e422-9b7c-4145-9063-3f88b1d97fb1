# AI代理服务 Prometheus 监控配置

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'ai-proxy-monitor'
    environment: '${ENVIRONMENT:-production}'

# 规则文件
rule_files:
  - "alert_rules.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 抓取配置
scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # AI代理服务监控
  - job_name: 'ai-proxy'
    static_configs:
      - targets: ['ai-proxy:8080']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # Nginx监控
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:8081']
    scrape_interval: 30s
    metrics_path: /nginx_status
    scrape_timeout: 10s

  # PostgreSQL监控
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Redis监控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s
    scrape_timeout: 10s

  # 系统监控 (Node Exporter)
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Docker监控
  - job_name: 'docker'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 10s

  # 黑盒监控 (外部服务检查)
  - job_name: 'blackbox'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - https://api.openai.com/v1/models
        - https://httpbin.org/status/200
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  # 应用健康检查
  - job_name: 'health-check'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - http://ai-proxy:8080/health
        - http://nginx/health
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115
    scrape_interval: 10s

# 远程写入配置 (可选，用于长期存储)
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint/api/v1/write"
#     basic_auth:
#       username: "username"
#       password: "password"

# 远程读取配置 (可选)
# remote_read:
#   - url: "https://prometheus-remote-read-endpoint/api/v1/read"
#     basic_auth:
#       username: "username"
#       password: "password"
