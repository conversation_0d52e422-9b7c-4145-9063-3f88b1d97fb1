#!/usr/bin/env python3
"""
多账户配置设置工具
"""

import json
import os
from typing import List, Dict, Any

def load_current_config() -> Dict[str, Any]:
    """加载当前配置"""
    config_file = "config.json"
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"⚠️  加载配置文件失败: {e}")
    
    # 返回默认配置
    return {
        "providers": {
            "openrouter": [],
            "openai": []
        },
        "default_timeout": 30,
        "max_retries": 3,
        "health_check_interval": 300,
        "enable_monitoring": True,
        "log_level": "INFO"
    }

def add_openrouter_account(config: Dict[str, Any], name: str, api_key: str, 
                          site_url: str = "", site_name: str = "", timeout: int = 30) -> bool:
    """添加OpenRouter账户"""
    if "providers" not in config:
        config["providers"] = {}
    if "openrouter" not in config["providers"]:
        config["providers"]["openrouter"] = []
    
    # 检查是否已存在同名账户
    for account in config["providers"]["openrouter"]:
        if account.get("name") == name:
            print(f"⚠️  账户名 '{name}' 已存在")
            return False
    
    # 添加新账户
    new_account = {
        "name": name,
        "api_key": api_key,
        "config": {
            "timeout": timeout,
            "site_url": site_url,
            "site_name": site_name
        }
    }
    
    config["providers"]["openrouter"].append(new_account)
    print(f"✅ 已添加OpenRouter账户: {name}")
    return True

def add_openai_account(config: Dict[str, Any], name: str, api_key: str) -> bool:
    """添加OpenAI账户"""
    if "providers" not in config:
        config["providers"] = {}
    if "openai" not in config["providers"]:
        config["providers"]["openai"] = []
    
    # 检查是否已存在同名账户
    for account in config["providers"]["openai"]:
        if account.get("name") == name:
            print(f"⚠️  账户名 '{name}' 已存在")
            return False
    
    # 添加新账户
    new_account = {
        "name": name,
        "api_key": api_key,
        "config": {}
    }
    
    config["providers"]["openai"].append(new_account)
    print(f"✅ 已添加OpenAI账户: {name}")
    return True

def save_config(config: Dict[str, Any], filename: str = "config.json") -> bool:
    """保存配置到文件"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        print(f"✅ 配置已保存到: {filename}")
        return True
    except Exception as e:
        print(f"❌ 保存配置失败: {e}")
        return False

def show_current_config(config: Dict[str, Any]):
    """显示当前配置"""
    print("\n📋 当前配置:")
    print("=" * 40)
    
    providers = config.get("providers", {})
    
    # OpenRouter账户
    openrouter_accounts = providers.get("openrouter", [])
    print(f"🔗 OpenRouter账户 ({len(openrouter_accounts)}个):")
    for i, account in enumerate(openrouter_accounts, 1):
        name = account.get("name", f"account_{i}")
        api_key = account.get("api_key", "")
        masked_key = f"{api_key[:10]}***" if len(api_key) > 10 else "***"
        print(f"   {i}. {name}: {masked_key}")
    
    # OpenAI账户
    openai_accounts = providers.get("openai", [])
    print(f"🤖 OpenAI账户 ({len(openai_accounts)}个):")
    for i, account in enumerate(openai_accounts, 1):
        name = account.get("name", f"account_{i}")
        api_key = account.get("api_key", "")
        masked_key = f"{api_key[:10]}***" if len(api_key) > 10 else "***"
        print(f"   {i}. {name}: {masked_key}")

def interactive_setup():
    """交互式设置"""
    print("🔧 多账户配置设置工具")
    print("=" * 40)
    
    # 加载当前配置
    config = load_current_config()
    show_current_config(config)
    
    while True:
        print("\n📋 选择操作:")
        print("1. 添加OpenRouter账户")
        print("2. 添加OpenAI账户")
        print("3. 显示当前配置")
        print("4. 保存配置")
        print("5. 退出")
        
        choice = input("\n请选择 (1-5): ").strip()
        
        if choice == "1":
            print("\n🔗 添加OpenRouter账户:")
            name = input("账户名称: ").strip()
            if not name:
                print("❌ 账户名称不能为空")
                continue
            
            api_key = input("API密钥: ").strip()
            if not api_key:
                print("❌ API密钥不能为空")
                continue
            
            site_url = input("网站URL (可选): ").strip()
            site_name = input("网站名称 (可选): ").strip()
            
            timeout_str = input("超时时间 (默认30秒): ").strip()
            timeout = 30
            if timeout_str:
                try:
                    timeout = int(timeout_str)
                except ValueError:
                    print("⚠️  无效的超时时间，使用默认值30秒")
            
            add_openrouter_account(config, name, api_key, site_url, site_name, timeout)
        
        elif choice == "2":
            print("\n🤖 添加OpenAI账户:")
            name = input("账户名称: ").strip()
            if not name:
                print("❌ 账户名称不能为空")
                continue
            
            api_key = input("API密钥: ").strip()
            if not api_key:
                print("❌ API密钥不能为空")
                continue
            
            add_openai_account(config, name, api_key)
        
        elif choice == "3":
            show_current_config(config)
        
        elif choice == "4":
            if save_config(config):
                print("\n🎉 配置保存成功！")
                print("💡 重启服务以应用新配置:")
                print("   python start_api_service.py")
        
        elif choice == "5":
            print("👋 退出配置工具")
            break
        
        else:
            print("❌ 无效选择，请重试")

def quick_setup_demo():
    """快速设置演示配置"""
    print("🚀 快速设置演示配置...")
    
    config = load_current_config()
    
    # 添加示例OpenRouter账户
    demo_accounts = [
        ("primary", "sk-or-v1-your-primary-key-here"),
        ("backup1", "sk-or-v1-your-backup1-key-here"),
        ("backup2", "sk-or-v1-your-backup2-key-here"),
        ("emergency", "sk-or-v1-your-emergency-key-here")
    ]
    
    for name, api_key in demo_accounts:
        add_openrouter_account(
            config, name, api_key,
            site_url="https://yourapp.com",
            site_name="Your AI App"
        )
    
    # 保存配置
    if save_config(config, "config_demo.json"):
        print("\n🎉 演示配置已创建: config_demo.json")
        print("💡 请编辑文件中的API密钥，然后重命名为config.json")

def main():
    """主函数"""
    print("🔧 AI代理服务 - 多账户配置工具")
    print("=" * 50)
    
    print("📋 选择模式:")
    print("1. 交互式设置")
    print("2. 快速创建演示配置")
    print("3. 退出")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == "1":
        interactive_setup()
    elif choice == "2":
        quick_setup_demo()
    elif choice == "3":
        print("👋 退出")
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
