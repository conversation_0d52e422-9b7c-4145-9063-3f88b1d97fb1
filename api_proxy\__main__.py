#!/usr/bin/env python3
"""API代理服务主入口模块

支持通过 python -m api_proxy 启动服务
"""

import sys
import logging
import argparse
from typing import Optional
from pathlib import Path

from .proxy_service import ProxyService
from .config import Config
from .models import ProviderConfig


def setup_logging(level: str = "INFO"):
    """设置日志配置"""
    # logging.basicConfig aiohttp aiohttp.access
    # uvicorn uvicorn.access uvicorn.error
    # websockets
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        encoding='utf-8',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('logs/app.log', mode='a', encoding='utf-8')
        ]
    )


def create_sample_config() -> Config:
    """创建示例配置"""
    return Config({
        "openai": [
            ProviderConfig("demo", "sk-demo-key-replace-with-real", {}),
        ]
    })


def load_config_from_file(config_path: str) -> Optional[Config]:
    """从文件加载配置"""
    try:
        import json
        with open(config_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        providers = {}
        for provider_type, configs in data.get('providers', {}).items():
            provider_configs = []
            for config in configs:
                provider_configs.append(ProviderConfig(
                    name=config['name'],
                    api_key=config['api_key'],
                    config=config.get('config', {})
                ))
            providers[provider_type] = provider_configs
        
        return Config(
            providers=providers,
            default_timeout=data.get('default_timeout', 30),
            max_retries=data.get('max_retries', 3),
            health_check_interval=data.get('health_check_interval', 300),
            enable_monitoring=data.get('enable_monitoring', True),
            log_level=data.get('log_level', 'INFO')
        )
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return None


def run_interactive_demo(service: ProxyService):
    """运行交互式演示"""
    print("\n=== AI代理服务交互式演示 ===")
    print("输入 'quit' 退出")
    print("输入 'stats' 查看统计信息")
    print("输入 'health' 查看健康状态")
    print("输入消息内容进行AI对话测试")
    
    while True:
        try:
            user_input = input("\n> ").strip()
            
            if user_input.lower() == 'quit':
                break
            elif user_input.lower() == 'stats':
                stats = service.monitor.get_stats()
                print(f"统计信息: {stats}")
            elif user_input.lower() == 'health':
                # 执行健康检查
                service.health_checker.check_all(service.providers)
                healthy = service.health_checker.get_healthy_providers(service.providers)
                print(f"健康的提供商: {list(healthy.keys())}")
            elif user_input:
                # 模拟API调用
                try:
                    print("模拟调用OpenAI API...")
                    # 这里只是演示，实际需要真实的API密钥
                    print("注意: 需要配置真实的API密钥才能进行实际调用")
                except Exception as e:
                    print(f"调用失败: {e}")
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"错误: {e}")
    
    print("\n演示结束")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='AI代理服务')
    parser.add_argument('--config', '-c', help='配置文件路径')
    parser.add_argument('--log-level', '-l', default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')
    parser.add_argument('--demo', action='store_true', help='运行交互式演示')
    parser.add_argument('--web', action='store_true', help='启动Web管理界面')
    parser.add_argument('--host', default='127.0.0.1', help='Web服务器主机地址')
    parser.add_argument('--port', type=int, default=8000, help='Web服务器端口')
    parser.add_argument('--create-config', help='创建示例配置文件')
    parser.add_argument('--no-hot-reload', action='store_true', help='禁用热重载功能')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    # 创建日志目录
    Path('logs').mkdir(exist_ok=True)
    
    # 创建示例配置文件
    if args.create_config:
        try:
            import json
            config = create_sample_config()
            config_data = {
                'providers': {
                    'openai': [
                        {
                            'name': 'demo',
                            'api_key': 'sk-your-openai-api-key-here',
                            'config': {}
                        }
                    ]
                },
                'default_timeout': 30,
                'max_retries': 3,
                'health_check_interval': 300,
                'enable_monitoring': True,
                'log_level': 'INFO'
            }
            
            with open(args.create_config, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            print(f"示例配置文件已创建: {args.create_config}")
            print("请编辑配置文件，添加真实的API密钥")
            return
        except Exception as e:
            print(f"创建配置文件失败: {e}")
            return
    
    # 加载配置
    if args.config:
        config = load_config_from_file(args.config)
        if not config:
            print("配置加载失败，使用默认配置")
            config = create_sample_config()
    else:
        config = create_sample_config()
        print("使用默认配置，建议使用 --create-config 创建配置文件")
    
    try:
        # 创建服务实例
        service = ProxyService(config)
        logger.info("AI代理服务启动成功")
        
        # 显示服务信息
        print(f"\n=== AI代理服务 ===")
        print(f"提供商类型: {config.get_provider_types()}")
        print(f"总提供商数量: {config.get_provider_count()}")
        print(f"监控状态: {'启用' if config.enable_monitoring else '禁用'}")
        print(f"日志级别: {config.log_level}")
        
        if args.web:
            # 启动Web界面
            import uvicorn
            from .web_app import create_app

            enable_hot_reload = not args.no_hot_reload
            app = create_app(service, enable_hot_reload=enable_hot_reload)

            print(f"\n🚀 Web管理界面启动中...")
            print(f"📱 访问地址: http://{args.host}:{args.port}")
            print(f"🎯 功能包括: 配置管理、监控面板、日志查看、API测试")
            if enable_hot_reload:
                print(f"🔥 热重载: 启用 (配置文件变化时自动重载)")
            else:
                print(f"🔥 热重载: 禁用")
            print(f"⏹️  按 Ctrl+C 停止服务")

            uvicorn.run(app, host=args.host, port=args.port, log_level=args.log_level.lower())

        elif args.demo:
            run_interactive_demo(service)
        else:
            print("\n服务已启动，选择启动方式:")
            print("  --web    启动Web管理界面 (推荐)")
            print("  --demo   运行交互式演示")
            print("  --help   查看更多选项")
            
    except Exception as e:
        logger.error(f"服务启动失败: {e}")
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
