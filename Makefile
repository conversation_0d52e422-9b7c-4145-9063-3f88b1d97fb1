# AI代理服务 Makefile

.PHONY: help install install-dev test test-cov lint format clean build docs serve-docs

# 默认目标
help:
	@echo "🤖 AI代理服务开发工具"
	@echo ""
	@echo "🚀 启动服务:"
	@echo "  web          启动Web管理界面 (推荐)"
	@echo "  web-dev      启动开发模式Web界面"
	@echo "  demo         运行交互式演示"
	@echo ""
	@echo "📦 环境管理:"
	@echo "  install      安装生产依赖"
	@echo "  install-dev  安装开发依赖"
	@echo "  dev-setup    设置完整开发环境"
	@echo ""
	@echo "🧪 测试和质量:"
	@echo "  test         运行测试"
	@echo "  test-cov     运行测试并生成覆盖率报告"
	@echo "  lint         代码检查"
	@echo "  format       代码格式化"
	@echo "  security     安全检查"
	@echo ""
	@echo "🔧 工具:"
	@echo "  config       创建配置文件"
	@echo "  clean        清理临时文件"
	@echo "  build        构建包"
	@echo "  docs         生成文档"

# 安装依赖
install:
	pip install -r requirements.txt

install-dev:
	pip install -r requirements-dev.txt
	pip install -e .

# 测试
test:
	pytest tests/ -v

test-cov:
	pytest tests/ --cov=api_proxy --cov-report=html --cov-report=term-missing

# 代码质量
lint:
	flake8 api_proxy tests
	mypy api_proxy
	bandit -r api_proxy

format:
	black api_proxy tests
	isort api_proxy tests

# 清理
clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf .pytest_cache/
	rm -rf .coverage
	rm -rf htmlcov/
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

# 构建
build: clean
	python -m build

# 文档
docs:
	cd docs && make html

serve-docs:
	cd docs/_build/html && python -m http.server 8000

# 启动服务
web:
	python -m api_proxy --web

demo:
	python -m api_proxy --demo

# 启动Web界面（自定义端口）
web-dev:
	python -m api_proxy --web --host 0.0.0.0 --port 8080

# 安全检查
security:
	bandit -r api_proxy
	safety check

# 完整检查
check: lint test security
	@echo "所有检查通过!"

# 发布准备
release-check: clean format lint test security build
	@echo "发布准备完成!"

# ==================== CI/CD 相关 ====================

# 部署到测试环境
deploy-staging:
	@echo "🚀 部署到测试环境..."
	chmod +x scripts/deploy.sh
	./scripts/deploy.sh staging

# 部署到生产环境
deploy-production:
	@echo "🚀 部署到生产环境..."
	chmod +x scripts/deploy.sh
	./scripts/deploy.sh production

# 版本发布
release:
	@echo "📦 创建新版本发布..."
	chmod +x scripts/release.sh
	./scripts/release.sh

# 数据备份
backup:
	@echo "💾 备份数据..."
	chmod +x scripts/backup.sh
	./scripts/backup.sh

# 健康检查
health-check:
	@echo "🏥 执行健康检查..."
	chmod +x scripts/health-check.sh
	./scripts/health-check.sh

# Docker相关
docker-build:
	@echo "🐳 构建Docker镜像..."
	docker build -t ai-proxy:latest .

docker-run:
	@echo "🐳 运行Docker容器..."
	docker-compose up -d

docker-stop:
	@echo "🐳 停止Docker容器..."
	docker-compose down

docker-logs:
	@echo "🐳 查看Docker日志..."
	docker-compose logs -f

# 开发环境
dev-up:
	@echo "🔧 启动开发环境..."
	docker-compose -f docker-compose.dev.yml up -d

dev-down:
	@echo "🔧 停止开发环境..."
	docker-compose -f docker-compose.dev.yml down

dev-logs:
	@echo "🔧 查看开发环境日志..."
	docker-compose -f docker-compose.dev.yml logs -f

# 开发环境设置
dev-setup: install-dev
	pre-commit install
	@echo "开发环境设置完成!"

# 创建配置文件
config:
	python -m api_proxy --create-config config.json
	@echo "配置文件已创建: config.json"

# 运行所有测试类型
test-all:
	pytest tests/test_*_unit.py -v --tb=short
	pytest tests/test_*_boundary.py -v --tb=short
	pytest tests/test_*_error.py -v --tb=short
	pytest tests/test_*_integration.py -v --tb=short
