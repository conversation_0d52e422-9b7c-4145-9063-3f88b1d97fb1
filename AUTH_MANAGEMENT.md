# 认证和权限管理指南

## 概述

AI代理服务提供了完整的认证和权限管理系统，允许您：

1. **管理API密钥** - 为不同用户创建和管理API密钥
2. **控制访问权限** - 限制用户可以访问的模型和提供商
3. **模型映射** - 将公开的模型名映射到真实的提供商模型
4. **速率限制** - 控制每个用户的请求频率

## 🔑 API密钥管理

### 创建API密钥

1. 访问 `http://your-server:8000/admin/auth`
2. 点击"创建密钥"按钮
3. 填写以下信息：
   - **密钥名称**: 便于识别的名称
   - **角色**: admin/user/guest
   - **允许的模型**: 逗号分隔，`*` 表示所有模型
   - **允许的提供商**: 逗号分隔，`*` 表示所有提供商
   - **速率限制**: 每分钟最大请求数
   - **过期天数**: 可选，密钥有效期

### 密钥格式

生成的API密钥格式为：`sk-proxy-xxxxxxxxxx`

### 使用API密钥

```bash
curl -X POST http://your-server:8000/v1/chat/completions \
  -H "Authorization: Bearer sk-proxy-your-api-key" \
  -H "Content-Type: application/json" \
  -d '{"model": "gpt-3.5-turbo", "messages": [...]}'
```

## 🎯 模型映射管理

### 为什么需要模型映射？

模型映射允许您：
- 对外提供统一的模型名称
- 隐藏真实的提供商和模型信息
- 灵活切换底层提供商
- 设置价格倍数

### 创建模型映射

1. 在认证管理页面点击"创建映射"
2. 填写映射信息：
   - **公开名称**: 用户看到的模型名（如 `gpt-4`）
   - **提供商**: 实际的提供商（`openai` 或 `openrouter`）
   - **真实模型名**: 提供商的实际模型名
   - **描述**: 模型描述
   - **价格倍数**: 定价倍数

### 映射示例

| 公开名称 | 提供商 | 真实模型 | 描述 |
|---------|--------|----------|------|
| `gpt-3.5-turbo` | openai | `gpt-3.5-turbo` | GPT-3.5 Turbo |
| `gpt-4` | openai | `gpt-4` | GPT-4 |
| `claude-3-sonnet` | openrouter | `anthropic/claude-3-sonnet` | Claude 3 Sonnet |
| `gemini-pro` | openrouter | `google/gemini-pro` | Gemini Pro |

## 👥 用户角色

### Admin（管理员）
- 可以访问所有功能
- 管理API密钥和模型映射
- 查看所有统计信息

### User（用户）
- 可以调用API
- 受模型和提供商限制
- 受速率限制

### Guest（访客）
- 有限的API访问
- 严格的速率限制
- 只能访问指定模型

## 🚦 权限控制

### 模型访问控制

```json
{
  "allowed_models": ["gpt-3.5-turbo", "claude-3-sonnet"]
}
```

### 提供商访问控制

```json
{
  "allowed_providers": ["openai", "openrouter"]
}
```

### 通配符支持

```json
{
  "allowed_models": ["*"],      // 所有模型
  "allowed_providers": ["*"]    // 所有提供商
}
```

## 📊 使用统计

系统会自动记录：
- API密钥使用次数
- 最后使用时间
- 速率限制状态
- 错误统计

## 🔧 配置文件

认证配置存储在 `auth_config.json` 文件中：

```json
{
  "api_keys": [
    {
      "key_id": "abc123",
      "key_hash": "hashed_key",
      "name": "用户密钥",
      "role": "user",
      "allowed_models": ["gpt-3.5-turbo"],
      "allowed_providers": ["openai"],
      "rate_limit": 100,
      "created_at": "2024-01-01T00:00:00",
      "expires_at": null,
      "is_active": true,
      "usage_count": 0
    }
  ],
  "model_mappings": [
    {
      "public_name": "gpt-3.5-turbo",
      "provider": "openai",
      "real_model": "gpt-3.5-turbo",
      "description": "GPT-3.5 Turbo",
      "is_active": true,
      "price_multiplier": 1.0
    }
  ]
}
```

## 🛡️ 安全最佳实践

### 1. 密钥管理
- 定期轮换API密钥
- 为不同用途创建不同密钥
- 及时停用不需要的密钥

### 2. 权限最小化
- 只授予必要的模型访问权限
- 设置合理的速率限制
- 使用过期时间限制密钥有效期

### 3. 监控
- 定期检查使用统计
- 监控异常访问模式
- 设置告警机制

## 📝 API参考

### 认证端点

```bash
# 列出API密钥
GET /api/auth/keys

# 创建API密钥
POST /api/auth/keys

# 停用API密钥
POST /api/auth/keys/{key_id}/deactivate

# 激活API密钥
POST /api/auth/keys/{key_id}/activate
```

### 模型映射端点

```bash
# 列出模型映射
GET /api/auth/models

# 创建模型映射
POST /api/auth/models
```

## 🚀 快速开始

### 1. 启动服务
```bash
python -m api_proxy --web --host 0.0.0.0 --port 8000
```

### 2. 访问管理界面
```
http://your-server:8000/admin/auth
```

### 3. 创建第一个API密钥
- 系统会自动创建一个管理员密钥
- 密钥会在启动时显示在控制台

### 4. 配置模型映射
- 添加您想要对外提供的模型
- 映射到实际的提供商模型

### 5. 测试API
```bash
curl -X POST http://your-server:8000/v1/chat/completions \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [{"role": "user", "content": "Hello!"}]
  }'
```

## 🔍 故障排除

### 常见错误

1. **401 Unauthorized**
   - 检查API密钥格式
   - 确认密钥未过期
   - 验证密钥是否激活

2. **403 Forbidden**
   - 检查模型访问权限
   - 验证提供商访问权限
   - 确认角色权限

3. **429 Rate Limit Exceeded**
   - 检查速率限制设置
   - 等待限制重置
   - 考虑提高限制

### 日志查看

```bash
# 查看服务日志
tail -f logs/api_proxy.log

# 查看认证相关日志
grep "auth" logs/api_proxy.log
```

这样，您就有了一个完整的认证和权限管理系统，可以安全地对外提供AI API服务！
