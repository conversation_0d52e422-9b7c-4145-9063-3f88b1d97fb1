# 开发依赖
-r requirements.txt

# 测试工具
pytest==8.0.2
pytest-cov==4.1.0
pytest-mock==3.12.0
pytest-asyncio==0.23.2
coverage==7.3.2

# 代码质量工具
black==23.12.1
flake8==6.1.0
isort==5.13.2
mypy==1.8.0
pre-commit==3.6.0

# 文档工具
sphinx==7.2.6
sphinx-rtd-theme==2.0.0
sphinx-autodoc-typehints==1.25.2

# 开发工具
ipython==8.18.1
jupyter==1.0.0
notebook==7.0.6

# 性能分析
memory-profiler==0.61.0
line-profiler==4.1.1

# 安全检查
bandit==1.7.5
safety==2.3.5

# 类型检查
types-requests==*********
types-python-dateutil==*********

# 构建工具
build==1.0.3
twine==4.0.2
wheel==0.42.0

# 调试工具
pdb++==0.10.3
ipdb==0.13.13
