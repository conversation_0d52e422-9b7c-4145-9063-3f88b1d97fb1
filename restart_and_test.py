#!/usr/bin/env python3
"""
重启服务并测试
"""

import os
import sys
import time
import subprocess
import requests

def check_service():
    """检查服务是否运行"""
    try:
        response = requests.get("http://localhost:8000/api/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def wait_for_service(max_wait=30):
    """等待服务启动"""
    print("⏳ 等待服务启动...")
    for i in range(max_wait):
        if check_service():
            print("✅ 服务已启动")
            return True
        time.sleep(1)
        if i % 5 == 0:
            print(f"   等待中... ({i}/{max_wait})")
    
    print("❌ 服务启动超时")
    return False

def main():
    """主函数"""
    print("🔄 重启服务并测试流式响应修复")
    print("=" * 50)
    
    # 检查当前服务状态
    if check_service():
        print("📍 检测到服务正在运行")
        print("💡 请手动重启服务以应用更改:")
        print("   1. 在服务控制台按 Ctrl+C 停止服务")
        print("   2. 重新运行: python start_api_service.py")
        print("   3. 然后运行: python quick_test.py")
    else:
        print("📍 服务未运行")
        print("💡 请启动服务:")
        print("   python start_api_service.py")
        print("   然后运行: python quick_test.py")
    
    print("\n🧪 或者直接运行测试:")
    print("   python quick_test.py")
    
    print("\n📋 修复内容:")
    print("   ✅ 添加了流式响应检测")
    print("   ✅ 修复了JSON解析错误")
    print("   ✅ 改进了SSE数据处理")
    print("   ✅ 添加了详细的调试日志")

if __name__ == "__main__":
    main()
