#!/usr/bin/env python3
"""
AI代理服务客户端测试脚本

测试各种API端点的功能
"""

import requests
import json
import time
from typing import Dict, Any


class AIProxyClient:
    """AI代理服务客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000", token: str = None):
        self.base_url = base_url.rstrip('/')
        self.token = token
        self.session = requests.Session()
        
        # 设置默认头部
        self.session.headers.update({
            "Content-Type": "application/json"
        })
        
        if token:
            self.session.headers.update({
                "Authorization": f"Bearer {token}"
            })
    
    def chat_completion(self, message: str, model: str = "gpt-3.5-turbo", provider: str = "openai") -> Dict[str, Any]:
        """调用聊天补全API"""
        url = f"{self.base_url}/v1/chat/completions"
        
        headers = {}
        if provider != "openai":
            headers["X-Provider"] = provider
        
        data = {
            "model": model,
            "messages": [
                {"role": "user", "content": message}
            ],
            "temperature": 0.7,
            "max_tokens": 150
        }
        
        response = self.session.post(url, json=data, headers=headers)
        response.raise_for_status()
        return response.json()
    
    def proxy_request(self, provider: str, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """通用代理请求"""
        url = f"{self.base_url}/api/proxy"
        
        request_data = {
            "provider": provider,
            "endpoint": endpoint,
            "data": data
        }
        
        response = self.session.post(url, json=request_data)
        response.raise_for_status()
        return response.json()
    
    def list_models(self) -> Dict[str, Any]:
        """列出可用模型"""
        url = f"{self.base_url}/v1/models"
        response = self.session.get(url)
        response.raise_for_status()
        return response.json()
    
    def list_providers(self) -> Dict[str, Any]:
        """列出可用提供商"""
        url = f"{self.base_url}/api/providers"
        response = self.session.get(url)
        response.raise_for_status()
        return response.json()
    
    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        url = f"{self.base_url}/api/service/status"
        response = self.session.get(url)
        response.raise_for_status()
        return response.json()
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        url = f"{self.base_url}/api/health"
        response = self.session.get(url)
        response.raise_for_status()
        return response.json()


def test_openai_compatible_api(client: AIProxyClient):
    """测试OpenAI兼容API"""
    print("🧪 测试OpenAI兼容API")
    print("-" * 40)
    
    try:
        # 测试聊天补全
        print("📝 测试聊天补全...")
        response = client.chat_completion("Hello, how are you?")
        
        if "choices" in response and len(response["choices"]) > 0:
            content = response["choices"][0]["message"]["content"]
            print(f"✅ 响应: {content[:100]}...")
        else:
            print("❌ 响应格式异常")
        
        # 测试模型列表
        print("\n📋 测试模型列表...")
        models = client.list_models()
        
        if "data" in models:
            print(f"✅ 找到 {len(models['data'])} 个模型")
            for model in models["data"][:3]:  # 显示前3个
                print(f"   - {model['id']} ({model.get('provider', 'unknown')})")
        else:
            print("❌ 模型列表格式异常")
            
    except Exception as e:
        print(f"❌ OpenAI兼容API测试失败: {e}")


def test_proxy_api(client: AIProxyClient):
    """测试通用代理API"""
    print("\n🔄 测试通用代理API")
    print("-" * 40)
    
    try:
        # 测试OpenAI代理
        print("📝 测试OpenAI代理...")
        response = client.proxy_request(
            provider="openai",
            endpoint="chat/completions",
            data={
                "model": "gpt-3.5-turbo",
                "messages": [{"role": "user", "content": "What is AI?"}],
                "max_tokens": 100
            }
        )
        
        if "choices" in response:
            print("✅ OpenAI代理调用成功")
        else:
            print("❌ OpenAI代理响应异常")
        
        # 测试OpenRouter代理（如果可用）
        print("\n📝 测试OpenRouter代理...")
        try:
            response = client.proxy_request(
                provider="openrouter",
                endpoint="chat/completions",
                data={
                    "model": "openai/gpt-3.5-turbo",
                    "messages": [{"role": "user", "content": "Hello from OpenRouter!"}],
                    "max_tokens": 50
                }
            )
            
            if "choices" in response:
                print("✅ OpenRouter代理调用成功")
            else:
                print("❌ OpenRouter代理响应异常")
                
        except Exception as e:
            print(f"⚠️  OpenRouter代理不可用: {e}")
            
    except Exception as e:
        print(f"❌ 代理API测试失败: {e}")


def test_service_apis(client: AIProxyClient):
    """测试服务管理API"""
    print("\n🔧 测试服务管理API")
    print("-" * 40)
    
    try:
        # 测试提供商列表
        print("📋 测试提供商列表...")
        providers = client.list_providers()
        
        if "providers" in providers:
            print(f"✅ 找到 {len(providers['providers'])} 个提供商")
            for provider in providers["providers"]:
                print(f"   - {provider['type']}: {provider['count']} 个实例")
        else:
            print("❌ 提供商列表格式异常")
        
        # 测试服务状态
        print("\n📊 测试服务状态...")
        status = client.get_service_status()
        
        if "config" in status:
            config = status["config"]
            print(f"✅ 服务状态正常")
            print(f"   - 提供商类型: {config.get('provider_types', [])}")
            print(f"   - 总提供商数: {config.get('total_providers', 0)}")
            print(f"   - 监控状态: {'启用' if config.get('monitoring_enabled') else '禁用'}")
        else:
            print("❌ 服务状态格式异常")
        
        # 测试健康检查
        print("\n🏥 测试健康检查...")
        health = client.health_check()
        
        if "status" in health:
            print(f"✅ 健康检查: {health['status']}")
            print(f"   - 健康提供商: {health.get('healthy_providers', [])}")
        else:
            print("❌ 健康检查格式异常")
            
    except Exception as e:
        print(f"❌ 服务管理API测试失败: {e}")


def test_different_models(client: AIProxyClient):
    """测试不同模型"""
    print("\n🎯 测试不同模型")
    print("-" * 40)
    
    test_cases = [
        ("openai", "gpt-3.5-turbo", "Explain machine learning in one sentence"),
        ("openrouter", "openai/gpt-4o", "What is the capital of France?"),
        ("openrouter", "anthropic/claude-3-sonnet", "Write a haiku about coding"),
    ]
    
    for provider, model, prompt in test_cases:
        try:
            print(f"🧪 测试 {provider}/{model}...")
            
            if provider == "openai":
                response = client.chat_completion(prompt, model=model, provider=provider)
            else:
                response = client.proxy_request(
                    provider=provider,
                    endpoint="chat/completions",
                    data={
                        "model": model,
                        "messages": [{"role": "user", "content": prompt}],
                        "max_tokens": 100
                    }
                )
            
            if "choices" in response and len(response["choices"]) > 0:
                content = response["choices"][0]["message"]["content"]
                print(f"✅ 响应: {content[:80]}...")
            else:
                print("❌ 响应格式异常")
                
        except Exception as e:
            print(f"⚠️  {provider}/{model} 不可用: {e}")
        
        time.sleep(1)  # 避免请求过快


def main():
    """主函数"""
    print("🚀 AI代理服务客户端测试")
    print("=" * 50)
    
    # 配置客户端
    base_url = input("请输入服务地址 (默认: http://localhost:8000): ").strip()
    if not base_url:
        base_url = "http://localhost:8000"
    
    token = input("请输入认证令牌 (可选，直接回车跳过): ").strip()
    if not token:
        token = None
    
    client = AIProxyClient(base_url, token)
    
    print(f"\n🎯 测试目标: {base_url}")
    print(f"🔑 认证令牌: {'已设置' if token else '未设置'}")
    
    try:
        # 基础连接测试
        print(f"\n🔗 测试连接...")
        health = client.health_check()
        print("✅ 服务连接正常")
        
        # 运行各项测试
        test_openai_compatible_api(client)
        test_proxy_api(client)
        test_service_apis(client)
        test_different_models(client)
        
        print("\n🎉 测试完成！")
        print("\n💡 使用提示:")
        print("  - 确保配置文件中有有效的API密钥")
        print("  - 某些模型可能需要特定的提供商配置")
        print("  - 生产环境建议启用认证和HTTPS")
        
    except requests.exceptions.ConnectionError:
        print(f"❌ 无法连接到服务: {base_url}")
        print("请确保服务正在运行: python -m api_proxy --web")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")


if __name__ == "__main__":
    main()
