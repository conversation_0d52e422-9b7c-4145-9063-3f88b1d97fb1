#!/usr/bin/env python3
"""
测试客户端请求日志记录功能
"""

import requests
import json
import time

def test_detailed_request_logging():
    """测试详细的请求日志记录"""
    print("🧪 测试客户端请求日志记录...")
    
    # 测试数据
    test_data = {
        "model": "deepseek/deepseek-chat-v3-0324:free",
        "messages": [
            {"role": "system", "content": "你是一个有用的助手"},
            {"role": "user", "content": "请简单回复'收到'，这是一个日志测试"}
        ],
        "max_tokens": 10,
        "temperature": 0.5,
        "top_p": 0.9,
        "frequency_penalty": 0.1,
        "presence_penalty": 0.2,
        "stream": False
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer sk-test-key-for-logging-demo",
        "X-Provider": "openrouter",
        "User-Agent": "TestClient/1.0 (RequestLoggingTest)",
        "Accept": "application/json"
    }
    
    print("📡 发送测试请求...")
    print(f"   URL: http://localhost:8000/v1/chat/completions")
    print(f"   方法: POST")
    print(f"   请求头: {json.dumps(headers, indent=2)}")
    print(f"   请求体: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            headers=headers,
            json=test_data,
            timeout=15
        )
        
        print(f"\n📥 收到响应:")
        print(f"   状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
            print("✅ 请求成功")
        else:
            print(f"   错误响应: {response.text}")
            print("❌ 请求失败")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_stream_request_logging():
    """测试流式请求的日志记录"""
    print("\n🧪 测试流式请求日志记录...")
    
    test_data = {
        "model": "deepseek/deepseek-chat-v3-0324:free",
        "messages": [
            {"role": "user", "content": "请逐字回复'流式日志测试'"}
        ],
        "max_tokens": 20,
        "temperature": 0.3,
        "stream": True  # 启用流式
    }
    
    headers = {
        "Content-Type": "application/json",
        "X-Provider": "openrouter",
        "User-Agent": "StreamTestClient/1.0",
        "Accept": "text/event-stream"
    }
    
    print("📡 发送流式测试请求...")
    print(f"   流式模式: {test_data['stream']}")
    
    try:
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            headers=headers,
            json=test_data,
            timeout=15,
            stream=True
        )
        
        print(f"\n📥 收到流式响应:")
        print(f"   状态码: {response.status_code}")
        print(f"   Content-Type: {response.headers.get('Content-Type')}")
        
        if response.status_code == 200:
            print("✅ 流式请求成功")
            # 只读取前几行来验证
            count = 0
            for line in response.iter_lines(decode_unicode=True):
                if line and count < 3:
                    print(f"   数据: {line}")
                    count += 1
                elif count >= 3:
                    print("   ... (更多数据)")
                    break
        else:
            print(f"   ❌ 流式请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 流式请求异常: {e}")

def test_proxy_request_logging():
    """测试通用代理请求的日志记录"""
    print("\n🧪 测试通用代理请求日志记录...")
    
    proxy_data = {
        "provider": "openrouter",
        "endpoint": "chat/completions",
        "data": {
            "model": "deepseek/deepseek-chat-v3-0324:free",
            "messages": [
                {"role": "user", "content": "通用代理测试"}
            ],
            "max_tokens": 10,
            "stream": False
        }
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer sk-proxy-test-key",
        "User-Agent": "ProxyTestClient/1.0"
    }
    
    print("📡 发送通用代理测试请求...")
    print(f"   提供商: {proxy_data['provider']}")
    print(f"   端点: {proxy_data['endpoint']}")
    
    try:
        response = requests.post(
            "http://localhost:8000/api/proxy",
            headers=headers,
            json=proxy_data,
            timeout=15
        )
        
        print(f"\n📥 收到代理响应:")
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 代理请求成功")
        else:
            print(f"   ❌ 代理请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 代理请求异常: {e}")

def test_health_check():
    """测试健康检查请求的日志记录"""
    print("\n🧪 测试健康检查日志记录...")
    
    try:
        response = requests.get(
            "http://localhost:8000/api/health",
            headers={"User-Agent": "HealthCheckClient/1.0"},
            timeout=5
        )
        
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ 健康检查成功")
        else:
            print("❌ 健康检查失败")
            
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")

if __name__ == "__main__":
    print("🔍 客户端请求日志记录测试")
    print("=" * 50)
    print("此测试将发送多种类型的请求来验证日志记录功能")
    print("请观察服务端控制台输出的详细日志信息")
    print("=" * 50)
    
    # 等待用户确认
    input("按回车键开始测试...")
    
    # 测试健康检查
    test_health_check()
    
    time.sleep(1)
    
    # 测试详细请求日志
    test_detailed_request_logging()
    
    time.sleep(1)
    
    # 测试流式请求日志
    test_stream_request_logging()
    
    time.sleep(1)
    
    # 测试通用代理请求日志
    test_proxy_request_logging()
    
    print("\n" + "=" * 50)
    print("🎯 测试完成！请检查服务端日志中的以下信息:")
    print("1. 🌐 HTTP请求基本信息（IP、User-Agent、方法、路径）")
    print("2. 📥 客户端请求详情（模型、消息、参数）")
    print("3. 📤 HTTP响应信息（状态码、处理时间）")
    print("4. 🔒 敏感信息脱敏（Authorization头部分隐藏）")
    print("5. 📊 不同类型请求的专门日志记录")
    print("\n💡 这些详细日志将帮助你:")
    print("   - 调试API调用问题")
    print("   - 监控请求流量")
    print("   - 分析客户端行为")
    print("   - 排查网络问题")
