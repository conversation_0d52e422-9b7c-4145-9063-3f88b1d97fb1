/* AI代理服务自定义样式 */

:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #43e97b;
    --warning-color: #fa709a;
    --danger-color: #f5576c;
    --info-color: #4facfe;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.sidebar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 15px;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.metric-card {
    background: linear-gradient(135deg, var(--warning-color) 0%, var(--danger-color) 100%);
    color: white;
}

.metric-card-blue {
    background: linear-gradient(135deg, var(--info-color) 0%, #00f2fe 100%);
    color: white;
}

.metric-card-green {
    background: linear-gradient(135deg, var(--success-color) 0%, #38f9d7 100%);
    color: white;
}

.metric-card-orange {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: white;
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.nav-link {
    border-radius: 8px;
    margin: 2px 0;
    transition: all 0.3s ease;
}

.nav-link:hover {
    background-color: rgba(255,255,255,0.1);
    transform: translateX(5px);
}

.status-badge {
    font-size: 0.8em;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
}

.status-running {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-stopped {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.card {
    animation: fadeIn 0.5s ease-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .main-content {
        margin-left: 0;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

/* 表格样式 */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    font-weight: 600;
}

/* 模态框样式 */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 15px 15px 0 0;
}

/* 工具提示 */
.tooltip-inner {
    background-color: var(--primary-color);
    border-radius: 8px;
}

/* 进度条 */
.progress {
    height: 8px;
    border-radius: 10px;
    background-color: #e9ecef;
}

.progress-bar {
    background: linear-gradient(135deg, var(--success-color) 0%, #38f9d7 100%);
    border-radius: 10px;
}

/* 徽章 */
.badge {
    border-radius: 8px;
    font-weight: 500;
}

/* 警告框 */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
