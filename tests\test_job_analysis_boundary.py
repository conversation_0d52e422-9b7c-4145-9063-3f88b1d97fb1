"""作业分析边界测试

测试JobAnalysis类的边界情况处理
"""

import pytest
from api_proxy.job_analysis import JobAnalysis


class TestJobAnalysisBoundary:
    """作业分析边界测试类"""

    def test_empty_log(self):
        """测试空日志处理"""
        analyzer = JobAnalysis()
        results = analyzer.parse_log("")
        assert len(results) == 0

    def test_whitespace_log(self):
        """测试全空白日志处理"""
        analyzer = JobAnalysis()
        results = analyzer.parse_log("   \n\t  ")
        assert len(results) == 0

    def test_long_log(self):
        """测试长日志处理性能"""
        analyzer = JobAnalysis()
        long_log = "Test log\n" * 10000 + "FAILED test.py"
        results = analyzer.parse_log(long_log)
        assert len(results) == 1

    def test_no_error_log(self):
        """测试无错误日志处理"""
        analyzer = JobAnalysis()
        log = "This is a normal log message"
        results = analyzer.parse_log(log)
        assert len(results) == 0
