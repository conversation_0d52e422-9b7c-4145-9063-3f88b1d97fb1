"""作业失败分析模块

提供自动化CI/CD作业失败分析功能，包括：
- 日志解析
- 错误分类
- 解决方案生成
"""

from datetime import datetime
from enum import Enum, auto
from dataclasses import dataclass
from typing import List
import re


class JobErrorType(Enum):
    """作业错误类型枚举

    成员变量:
        TEST_FAILURE: 测试失败
        LINT_ERROR: 代码规范问题
        DEPENDENCY_ERROR: 依赖缺失或版本冲突
        CONFIG_ERROR: 配置错误
        TIMEOUT: 执行超时
        MEMORY_ERROR: 内存不足
        NETWORK_ERROR: 网络问题
        PERMISSION_ERROR: 权限不足
        BUILD_ERROR: 构建失败
        DEPLOY_ERROR: 部署失败
    """

    TEST_FAILURE = auto()
    LINT_ERROR = auto()
    DEPENDENCY_ERROR = auto()
    CONFIG_ERROR = auto()
    TIMEOUT = auto()
    MEMORY_ERROR = auto()
    NETWORK_ERROR = auto()
    PERMISSION_ERROR = auto()
    BUILD_ERROR = auto()
    DEPLOY_ERROR = auto()


@dataclass
class JobFailureAnalysis:
    """作业失败分析结果"""

    error_type: JobErrorType
    error_message: str
    solution: str
    timestamp: datetime = datetime.now()


class JobAnalyzer:
    """作业日志分析器"""

    ERROR_PATTERNS = [
        # 测试失败（增强模式）
        (re.compile(r"^FAILED\s.*\.py"), JobErrorType.TEST_FAILURE),
        (re.compile(r"^====+ FAILURES ====+"), JobErrorType.TEST_FAILURE),
        (re.compile(r">\s+def test_.*"), JobErrorType.TEST_FAILURE),
        (re.compile(r"E\s+assert.*"), JobErrorType.TEST_FAILURE),
        (re.compile(r"AssertionError"), JobErrorType.TEST_FAILURE),
        # 代码规范错误
        (re.compile(r"black\s.*failed"), JobErrorType.LINT_ERROR),
        (re.compile(r"flake8\s.*failed"), JobErrorType.LINT_ERROR),
        (re.compile(r"^E[0-9]{3}:"), JobErrorType.LINT_ERROR),
        # 依赖错误
        (re.compile(r"ModuleNotFoundError"), JobErrorType.DEPENDENCY_ERROR),
        (re.compile(r"ImportError"), JobErrorType.DEPENDENCY_ERROR),
        # 超时错误
        (re.compile(r"TimeoutError"), JobErrorType.TIMEOUT),
        (re.compile(r"timed\sout"), JobErrorType.TIMEOUT),
        # 内存错误
        (re.compile(r"MemoryError"), JobErrorType.MEMORY_ERROR),
        (re.compile(r"OutOfMemory"), JobErrorType.MEMORY_ERROR),
        # 网络错误
        (re.compile(r"ConnectionError"), JobErrorType.NETWORK_ERROR),
        (re.compile(r"Failed\sto\sconnect"), JobErrorType.NETWORK_ERROR),
        # 权限错误
        (re.compile(r"PermissionError"), JobErrorType.PERMISSION_ERROR),
        (re.compile(r"Permission\sdenied"), JobErrorType.PERMISSION_ERROR),
        # 配置错误
        (re.compile(r"ConfigurationError"), JobErrorType.CONFIG_ERROR),
        (re.compile(r"Missing.*configuration"), JobErrorType.CONFIG_ERROR),
    ]

    SOLUTIONS = {
        JobErrorType.TEST_FAILURE: (
            "测试失败修复步骤:\n"
            "1. 检查测试代码和实现代码是否同步\n"
            "2. 运行失败测试单独验证: `pytest path/to/test.py::TestClass::test_method`\n"
            "3. 检查错误堆栈中的具体断言位置\n"
            "4. 确保测试数据和环境配置正确"
        ),
        JobErrorType.LINT_ERROR: (
            "代码规范问题修复步骤:\n"
            "1. 运行 `black .` 自动格式化代码\n"
            "2. 运行 `flake8` 检查并提供具体行号\n"
            "3. 修复所有报告的问题"
        ),
        JobErrorType.DEPENDENCY_ERROR: (
            "依赖问题修复步骤:\n"
            "1. 检查requirements.txt中的依赖版本\n"
            "2. 确认virtualenv/pipenv配置\n"
            "3. 查看错误信息中的具体依赖包"
        ),
        JobErrorType.CONFIG_ERROR: "验证配置文件和环境变量设置",
        JobErrorType.TIMEOUT: "增加超时时间或优化性能",
        JobErrorType.MEMORY_ERROR: "增加内存限制或优化内存使用",
        JobErrorType.NETWORK_ERROR: "检查网络连接和代理设置",
        JobErrorType.PERMISSION_ERROR: "检查文件和目录权限设置",
    }

    def analyze_log(self, log_text: str) -> List[JobFailureAnalysis]:
        """分析作业日志并返回错误分析结果

        Args:
            log_text: 作业日志文本

        Returns:
            包含所有识别错误的分析结果列表
        """
        if not log_text.strip():
            return []

        results = []

        for line in log_text.split('\n'):
            for pattern, error_type in self.ERROR_PATTERNS:
                if pattern.search(line):
                    solution = self.SOLUTIONS.get(error_type, "请检查日志获取详细信息")
                    results.append(
                        JobFailureAnalysis(
                            error_type=error_type,
                            error_message=line.strip(),
                            solution=solution,
                        )
                    )
                    break

        return results
