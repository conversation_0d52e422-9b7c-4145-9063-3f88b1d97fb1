# AI代理服务 Docker Compose 开发环境配置
# 用于本地开发和测试

version: '3.8'

services:
  # AI代理服务开发版本
  ai-proxy-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
      args:
        - VERSION=dev
        - BUILD_DATE=${BUILD_DATE:-$(date -u +'%Y-%m-%dT%H:%M:%SZ')}
        - VCS_REF=${VCS_REF:-$(git rev-parse --short HEAD)}
    container_name: ai-proxy-dev
    restart: unless-stopped
    ports:
      - "8080:8080"
      - "5678:5678"  # 调试端口
    environment:
      # 开发环境配置
      - APP_ENV=development
      - LOG_LEVEL=DEBUG
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      
      # API配置
      - OPENAI_API_KEY_PRIMARY=${OPENAI_API_KEY_PRIMARY:-sk-test-key}
      - DEFAULT_TIMEOUT=30
      - MAX_RETRIES=3
      
      # 缓存配置 (可选)
      - REDIS_URL=redis://redis-dev:6379/0
      
      # 开发工具配置
      - ENABLE_HOT_RELOAD=true
      - ENABLE_DEBUG=true
      - ENABLE_PROFILING=true
    volumes:
      # 挂载源代码以支持热重载
      - .:/app
      - ./logs:/app/logs
      - ./data:/app/data
      - ./.secrets:/app/.secrets:ro
      # 排除不需要同步的目录
      - /app/__pycache__
      - /app/.pytest_cache
      - /app/venv
    depends_on:
      - redis-dev
    networks:
      - ai-proxy-dev-network
    command: ["python", "-m", "api_proxy", "--web", "--host", "0.0.0.0", "--port", "8080", "--reload"]



  # Redis 开发缓存
  redis-dev:
    image: redis:7-alpine
    container_name: ai-proxy-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_dev_data:/data
    networks:
      - ai-proxy-dev-network



  # Redis 测试缓存
  redis-test:
    image: redis:7-alpine
    container_name: ai-proxy-redis-test
    restart: "no"
    command: redis-server --appendonly no
    networks:
      - ai-proxy-dev-network
    profiles:
      - testing

  # Mailhog 邮件测试服务
  mailhog:
    image: mailhog/mailhog:latest
    container_name: ai-proxy-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - ai-proxy-dev-network
    profiles:
      - development

  # 文档服务
  docs:
    build:
      context: .
      dockerfile: Dockerfile.docs
    container_name: ai-proxy-docs
    restart: unless-stopped
    ports:
      - "8000:8000"
    volumes:
      - ./docs:/app/docs
      - ./README.md:/app/README.md:ro
    networks:
      - ai-proxy-dev-network
    profiles:
      - docs

  # Jupyter Notebook 用于数据分析
  jupyter:
    image: jupyter/scipy-notebook:latest
    container_name: ai-proxy-jupyter
    restart: unless-stopped
    ports:
      - "8888:8888"
    environment:
      - JUPYTER_ENABLE_LAB=yes
      - JUPYTER_TOKEN=${JUPYTER_TOKEN:-aiproxy}
    volumes:
      - ./notebooks:/home/<USER>/work
      - ./data:/home/<USER>/work/data:ro
      - ./logs:/home/<USER>/work/logs:ro
    networks:
      - ai-proxy-dev-network
    profiles:
      - analysis

# 网络配置
networks:
  ai-proxy-dev-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 开发数据卷
volumes:
  redis_dev_data:
    driver: local
