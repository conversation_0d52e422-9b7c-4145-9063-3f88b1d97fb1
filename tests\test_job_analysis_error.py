"""作业分析错误测试

测试JobAnalysis类的错误处理
"""

import pytest
from api_proxy.job_analysis import JobAnalysis, JobErrorType


class TestJobAnalysisError:
    """作业分析错误测试类"""

    @pytest.mark.parametrize(
        "invalid_input",
        [
            None,
            123,
            {},
            [],
        ],
    )
    def test_invalid_log_input(self, invalid_input):
        """测试无效日志输入处理"""
        analyzer = JobAnalysis()
        with pytest.raises(ValueError):
            analyzer.parse_log(invalid_input)

    def test_invalid_error_type(self):
        """测试无效错误类型处理"""
        analyzer = JobAnalysis()
        with pytest.raises(ValueError):
            analyzer._get_solution_for_error("InvalidErrorType")

    @pytest.mark.parametrize(
        "sensitive_data",
        [
            "ApiKey=secret123",
            "password=abc123",
            "token=xyz456",
            "authorization: Bearer ey<PERSON>hbGci",
        ],
    )
    def test_sensitive_data_redaction(self, sensitive_data):
        """测试各种敏感信息过滤"""
        analyzer = JobAnalysis()
        log = f"{sensitive_data} FAILED test.py"
        results = analyzer.parse_log(log, redact_sensitive=True)
        assert sensitive_data not in str(results[0])
