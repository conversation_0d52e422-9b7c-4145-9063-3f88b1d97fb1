#!/usr/bin/env python3
"""
启动简单版本的Web界面
"""
import uvicorn
from api_proxy.simple_web import create_simple_app
from api_proxy.proxy_service import ProxyService
from api_proxy.config import Config
import os

def main():
    """启动简单Web应用"""
    print("🚀 启动简单版本的AI代理服务Web界面...")
    
    # 尝试加载配置
    proxy_service = None
    config_file = "config.json"
    
    if os.path.exists(config_file):
        try:
            config = Config.from_file(config_file)
            proxy_service = ProxyService(config)
            print(f"✅ 代理服务初始化成功，提供商: {list(config.providers.keys())}")
        except Exception as e:
            print(f"⚠️  加载配置失败: {e}")
            print("💡 将使用默认配置")
    else:
        print(f"⚠️  配置文件不存在: {config_file}")
        print("💡 将使用默认配置")
    
    # 创建应用
    app = create_simple_app(proxy_service)
    
    # 启动服务器
    print("🌐 Web界面地址: http://localhost:8000")
    print("📝 认证管理: http://localhost:8000/auth")
    print("🔧 按 Ctrl+C 停止服务")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )

if __name__ == "__main__":
    main()
