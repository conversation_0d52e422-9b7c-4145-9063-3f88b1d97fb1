#!/usr/bin/env python3
"""
测试路由注册脚本

检查所有路由是否正确注册
"""

import requests
import sys


def test_routes():
    """测试所有路由"""
    base_url = "http://127.0.0.1:8000"
    
    routes_to_test = [
        ("/", "仪表板"),
        ("/config", "配置管理"),
        ("/api-info", "API信息"),
        ("/admin/auth", "认证管理"),
        ("/api/health", "健康检查"),
        ("/api/service/status", "服务状态"),
        ("/api/auth/keys", "API密钥列表"),
        ("/api/auth/models", "模型映射列表"),
        ("/v1/models", "模型列表"),
        ("/docs", "API文档"),
    ]
    
    print("🔍 测试路由注册状态")
    print("=" * 50)
    
    success_count = 0
    total_count = len(routes_to_test)
    
    for path, name in routes_to_test:
        try:
            url = f"{base_url}{path}"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                print(f"✅ {name} ({path}): 正常")
                success_count += 1
            elif response.status_code == 401:
                print(f"🔒 {name} ({path}): 需要认证 (正常)")
                success_count += 1
            elif response.status_code == 404:
                print(f"❌ {name} ({path}): 路由未找到")
            else:
                print(f"⚠️  {name} ({path}): HTTP {response.status_code}")
                success_count += 1
                
        except requests.exceptions.ConnectionError:
            print(f"🔌 {name} ({path}): 服务未启动")
        except Exception as e:
            print(f"❌ {name} ({path}): 错误 - {e}")
    
    print("=" * 50)
    print(f"📊 测试结果: {success_count}/{total_count} 路由正常")
    
    if success_count == 0:
        print("\n💡 建议:")
        print("1. 确保服务正在运行: python -m api_proxy --web")
        print("2. 检查端口是否正确: http://127.0.0.1:8000")
        return False
    elif success_count < total_count:
        print("\n⚠️  部分路由有问题，可能需要重启服务")
        return False
    else:
        print("\n🎉 所有路由都正常工作！")
        return True


def test_specific_route():
    """专门测试API信息路由"""
    print("\n🎯 专门测试 /api-info 路由")
    print("-" * 30)
    
    try:
        url = "http://127.0.0.1:8000/api-info"
        response = requests.get(url, timeout=5)
        
        print(f"URL: {url}")
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ API信息页面正常访问")
            # 检查响应内容
            content = response.text
            if "API服务信息" in content:
                print("✅ 页面内容正确")
            else:
                print("⚠️  页面内容可能有问题")
        elif response.status_code == 404:
            print("❌ 路由未找到 - 需要重启服务")
            print("\n🔧 解决方法:")
            print("1. 停止当前服务 (Ctrl+C)")
            print("2. 重新启动: python -m api_proxy --web")
        else:
            print(f"⚠️  意外状态码: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务")
        print("请确保服务正在运行: python -m api_proxy --web")
    except Exception as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    print("🚀 路由测试工具")
    
    # 测试所有路由
    all_good = test_routes()
    
    # 如果有问题，专门测试API信息路由
    if not all_good:
        test_specific_route()
    
    print("\n" + "=" * 50)
    print("测试完成！")
