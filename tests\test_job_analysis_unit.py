"""作业分析单元测试

测试JobAnalysis类的核心功能
"""

import pytest
from datetime import datetime
from api_proxy.job_analysis import JobAnalysis, JobErrorType, JobFailureAnalysis


class TestJobAnalysisUnit:
    """作业分析单元测试类"""

    @pytest.fixture
    def analyzer(self):
        """测试分析器实例"""
        return JobAnalysis()

    def test_parse_test_failure(self, analyzer):
        """测试识别测试失败错误"""
        log = "FAILED test_service.py - AssertionError: expected True, got False"
        results = analyzer.parse_log(log)

        assert len(results) == 1
        assert results[0].error_type == JobErrorType.TEST_FAILURE
        assert "检查失败的测试用例" in results[0].solution

    def test_parse_lint_error(self, analyzer):
        """测试识别代码规范错误"""
        log = "black would reformat file.py"
        results = analyzer.parse_log(log)

        assert len(results) == 1
        assert results[0].error_type == JobErrorType.LINT_ERROR

    def test_parse_dependency_error(self, analyzer):
        """测试识别依赖错误"""
        log = "ModuleNotFoundError: No module named 'missing'"
        results = analyzer.parse_log(log)

        assert len(results) == 1
        assert results[0].error_type == JobErrorType.DEPENDENCY_ERROR

    def test_solution_not_empty(self, analyzer):
        """测试所有错误类型都有解决方案"""
        for error_type in JobErrorType:
            assert analyzer._get_solution_for_error(error_type)
