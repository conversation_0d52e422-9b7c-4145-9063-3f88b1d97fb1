# OpenRouter提供商实现
import json
import logging
from typing import Dict, Any, Optional
import requests
import time
from datetime import datetime
from .base import BaseProvider


class OpenRouterProvider(BaseProvider):
    """OpenRouter API提供商实现
    
    特性:
    - 支持多种AI模型的统一API接口
    - 自动重试机制
    - 超时处理
    - 使用时间追踪
    - 支持应用归属标识
    """

    @property
    def name(self) -> str:
        return "openrouter"

    @property
    def last_used(self) -> Optional[datetime]:
        """返回最后API调用时间戳"""
        return self._last_used

    def __init__(self, api_key: str, timeout: int = 30, site_url: str = "", site_name: str = ""):
        """初始化OpenRouter提供者

        Args:
            api_key: OpenRouter API密钥
            timeout: 请求超时(秒)
            site_url: 可选的网站URL，用于OpenRouter排行榜
            site_name: 可选的网站名称，用于OpenRouter排行榜

        Raises:
            ValueError: API密钥无效或超时参数无效
        """
        if not api_key or not isinstance(api_key, str) or not api_key.strip():
            raise ValueError("API密钥必须是非空字符串")
        if not isinstance(timeout, int) or timeout <= 0:
            raise ValueError("超时时间必须是正整数")

        self.api_key = api_key.strip()
        self.timeout = timeout
        self.base_url = "https://openrouter.ai/api/v1"
        self.site_url = site_url.strip() if site_url else ""
        self.site_name = site_name.strip() if site_name else ""
        self.logger = logging.getLogger(__name__)
        self._last_used = None

    def call(self, endpoint: str, max_retries: int = 3, **kwargs) -> Dict[str, Any]:
        """调用OpenRouter API

        Args:
            endpoint: API端点路径
            max_retries: 最大重试次数(1-5)
            **kwargs: API请求参数

        Returns:
            Dict[str, Any]: API响应数据

        Raises:
            ValueError: 参数无效
            requests.HTTPError: API请求失败
            TimeoutError: 请求超时
            RateLimitError: 达到速率限制
            ProviderError: 提供商特定错误
        """
        if not endpoint or not isinstance(endpoint, str):
            raise ValueError("端点路径必须是非空字符串")
        if not 1 <= max_retries <= 5:
            raise ValueError("重试次数必须在1-5之间")

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

        # 添加可选的应用归属标识头部
        if self.site_url:
            headers["HTTP-Referer"] = self.site_url
        if self.site_name:
            headers["X-Title"] = self.site_name

        url = f"{self.base_url}/{endpoint.lstrip('/')}"

        # 记录请求详情（隐藏敏感信息）
        masked_headers = headers.copy()
        if "Authorization" in masked_headers:
            auth_value = masked_headers["Authorization"]
            if len(auth_value) > 20:
                masked_headers["Authorization"] = f"{auth_value[:20]}***"

        self.logger.info(f"🚀 OpenRouter API请求开始:")
        self.logger.info(f"   URL: {url}")
        self.logger.debug(f"   Headers: {masked_headers}")
        self.logger.debug(f"   Payload keys: {list(kwargs.keys())}")
        if 'model' in kwargs:
            self.logger.info(f"   Model: {kwargs['model']}")

        # 记录请求体（日志预览版本，可以截断）
        safe_payload = kwargs.copy()
        if 'messages' in safe_payload and len(safe_payload['messages']) > 0:
            # 创建日志预览版本，截断长内容
            safe_messages = []
            for msg in safe_payload['messages']:
                safe_msg = msg.copy()
                if 'content' in safe_msg and len(safe_msg['content']) > 200:
                    safe_msg['content'] = safe_msg['content'][:200] + f"...(总长度:{len(msg['content'])}字符)"
                safe_messages.append(safe_msg)
            safe_payload['messages'] = safe_messages

        self.logger.debug(f"   请求体预览: {json.dumps(safe_payload, ensure_ascii=False, indent=2)}")

        # 记录消息统计信息
        if 'messages' in kwargs:
            self.logger.debug(f"   消息统计:")
            total_chars = 0
            for i, msg in enumerate(kwargs['messages']):
                role = msg.get('role', 'unknown')
                content = msg.get('content', '')
                content_len = len(content)
                total_chars += content_len
                self.logger.debug(f"     消息{i+1}: [{role}] {content_len} 字符")
            self.logger.debug(f"   总字符数: {total_chars}")

        # 注意：实际发送的是完整的 kwargs，不是 safe_payload

        for attempt in range(max_retries):
            try:
                self.logger.info(f"📡 发送请求 (尝试 {attempt + 1}/{max_retries})...")
                response = requests.post(url, headers=headers, json=kwargs, timeout=self.timeout)

                # 记录响应详情
                self.logger.info(f"📥 收到响应:")
                self.logger.info(f"   状态码: {response.status_code}")
                self.logger.debug(f"   响应头: {dict(response.headers)}")
                self.logger.debug(f"   响应大小: {len(response.content)} bytes")

                # 强制刷新日志输出
                import sys
                sys.stdout.flush()
                sys.stderr.flush()

                # 尝试获取响应文本（用于调试）
                try:
                    response_text = response.text
                    if len(response_text) > 1000:
                        self.logger.debug(f"   响应内容预览: {response_text[:1000]}...")
                    else:
                        self.logger.debug(f"   响应内容完整: {response_text}")
                except Exception as text_error:
                    self.logger.warning(f"   无法读取响应文本: {text_error}")

                # 检查HTTP状态码
                response.raise_for_status()

                # 检查是否为流式响应
                content_type = response.headers.get('Content-Type', '').lower()
                is_stream = content_type.startswith('text/event-stream') or kwargs.get('stream', False)

                if is_stream:
                    self.logger.info(f"🌊 检测到流式响应")
                    self.logger.debug(f"   Content-Type: {content_type}")
                    self.logger.debug(f"   流式参数: {kwargs.get('stream', False)}")
                    self.logger.info(f"   返回原始Response对象供调用方处理")
                    self._last_used = datetime.now()
                    return response  # 返回原始response对象，让调用方处理流式数据

                # 尝试解析JSON（非流式响应）
                try:
                    self.logger.info(f"🔄 开始解析JSON响应...")

                    # 使用线程超时保护（跨平台兼容）
                    import threading
                    import queue

                    result_queue = queue.Queue()

                    def parse_json():
                        try:
                            json_data = response.json()
                            result_queue.put(('success', json_data))
                        except Exception as e:
                            result_queue.put(('error', e))

                    # 启动解析线程
                    parse_thread = threading.Thread(target=parse_json)
                    parse_thread.daemon = True
                    parse_thread.start()

                    # 等待结果，最多5秒
                    try:
                        result_type, result_data = result_queue.get(timeout=5)

                        if result_type == 'success':
                            json_data = result_data
                            self.logger.info(f"✅ JSON解析成功")
                            self.logger.debug(f"   解析后数据类型: {type(json_data)}")

                            if isinstance(json_data, dict):
                                self.logger.debug(f"   响应数据键: {list(json_data.keys())}")
                                if 'choices' in json_data:
                                    self.logger.debug(f"   choices数量: {len(json_data['choices'])}")
                                    # 检查第一个choice的内容
                                    if len(json_data['choices']) > 0:
                                        first_choice = json_data['choices'][0]
                                        if 'message' in first_choice:
                                            content = first_choice['message'].get('content', '')
                                            content_preview = content[:100] + "..." if len(content) > 100 else content
                                            self.logger.debug(f"   第一个choice内容: {content_preview}")
                                if 'usage' in json_data:
                                    self.logger.info(f"   token使用情况: {json_data['usage']}")

                            self._last_used = datetime.now()
                            return json_data
                        else:
                            # 解析过程中出错
                            raise result_data

                    except queue.Empty:
                        self.logger.error(f"❌ JSON解析超时（5秒）")
                        self.logger.debug(f"   响应大小: {len(response.content)} bytes")
                        response_preview = response.text[:500] + "..." if len(response.text) > 500 else response.text
                        self.logger.debug(f"   响应预览: {response_preview}")
                        raise ValueError("JSON解析超时") from None

                except ValueError as json_error:
                    self.logger.error(f"❌ JSON解析失败: {json_error}")
                    response_preview = response.text[:1000] + "..." if len(response.text) > 1000 else response.text
                    self.logger.debug(f"   原始响应预览: {response_preview}")
                    raise ValueError(f"JSON解析失败: {json_error}") from json_error
                except Exception as e:
                    self.logger.error(f"❌ 解析过程中发生未知错误: {e}", exc_info=True)
                    raise

            except requests.Timeout as e:
                self.logger.warning(f"⏰ 请求超时 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                if attempt == max_retries - 1:
                    self.logger.error("❌ 达到最大重试次数，请求超时")
                    raise
            except requests.ConnectionError as e:
                self.logger.warning(f"🔌 连接错误 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                if attempt == max_retries - 1:
                    self.logger.error("❌ 达到最大重试次数，连接失败")
                    raise
            except requests.HTTPError as e:
                self.logger.warning(f"🚫 HTTP错误 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                self.logger.debug(f"   状态码: {e.response.status_code if e.response else 'Unknown'}")
                if e.response:
                    self.logger.debug(f"   错误响应: {e.response.text}")
                if attempt == max_retries - 1:
                    self.logger.error("❌ 达到最大重试次数，HTTP错误")
                    raise
            except requests.RequestException as e:
                self.logger.warning(f"🔥 请求异常 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                self.logger.debug(f"   异常类型: {type(e).__name__}")
                if hasattr(e, 'response') and e.response:
                    self.logger.debug(f"   响应状态码: {e.response.status_code}")
                    self.logger.debug(f"   响应内容: {e.response.text}")
                if attempt == max_retries - 1:
                    self.logger.error("❌ 达到最大重试次数，请求异常")
                    raise
            except Exception as e:
                self.logger.error(f"🔥 未知异常 (尝试 {attempt + 1}/{max_retries}): {str(e)}", exc_info=True)
                if attempt == max_retries - 1:
                    self.logger.critical("❌ 达到最大重试次数，未知错误")
                    raise

            # 如果不是最后一次尝试，等待后重试
            if attempt < max_retries - 1:
                wait_time = 2**attempt
                self.logger.info(f"⏳ 等待 {wait_time}秒后重试...")
                time.sleep(wait_time)

    def __str__(self) -> str:
        """安全的字符串表示，隐藏API密钥"""
        masked_key = f"{self.api_key[:8]}***" if len(self.api_key) > 8 else "***"
        return f"OpenRouterProvider(api_key={masked_key}, timeout={self.timeout}, site_name={self.site_name})"

    def __repr__(self) -> str:
        """安全的对象表示"""
        return self.__str__()
