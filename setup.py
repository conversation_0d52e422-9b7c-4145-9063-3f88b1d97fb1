# 包安装配置
from setuptools import setup, find_packages
import os

# 读取README文件
def read_readme():
    with open("README.md", "r", encoding="utf-8") as f:
        return f.read()

# 读取requirements文件
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as f:
        return [line.strip() for line in f if line.strip() and not line.startswith("#")]

setup(
    name="ai-proxy",
    version="0.2.0",
    author="AI Proxy Team",
    author_email="<EMAIL>",
    description="AI代理服务 - 多提供商API负载均衡和故障转移",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/ai-proxy/ai-proxy",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
        "Topic :: System :: Monitoring",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=8.0.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
            "coverage>=7.0.0",
        ],
        "docs": [
            "sphinx>=5.0.0",
            "sphinx-rtd-theme>=1.0.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "ai-proxy=api_proxy.__main__:main",
        ],
    },
    include_package_data=True,
    package_data={
        "api_proxy": ["*.json", "*.yml", "*.yaml"],
    },
    keywords="ai api proxy load-balancer openai chatgpt",
    project_urls={
        "Bug Reports": "https://github.com/ai-proxy/ai-proxy/issues",
        "Source": "https://github.com/ai-proxy/ai-proxy",
        "Documentation": "https://ai-proxy.readthedocs.io/",
    },
)
