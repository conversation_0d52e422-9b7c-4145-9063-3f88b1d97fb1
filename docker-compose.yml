# AI代理服务 Docker Compose 配置
# 生产环境部署配置

version: '3.8'

services:
  # AI代理服务主应用
  ai-proxy:
    image: ${REGISTRY_URL:-localhost}/ai-proxy:${VERSION:-latest}
    container_name: ai-proxy-app
    restart: unless-stopped
    ports:
      - "${APP_PORT:-8080}:8080"
    environment:
      # 应用配置
      - APP_ENV=production
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - PYTHONUNBUFFERED=1
      
      # API配置
      - OPENAI_API_KEY_PRIMARY=${OPENAI_API_KEY_PRIMARY}
      - OPENAI_API_KEY_BACKUP=${OPENAI_API_KEY_BACKUP}
      - DEFAULT_TIMEOUT=${DEFAULT_TIMEOUT:-30}
      - MAX_RETRIES=${MAX_RETRIES:-3}
      
      # 缓存配置 (可选)
      - REDIS_URL=redis://redis:6379/0
      
      # 监控配置
      - ENABLE_MONITORING=${ENABLE_MONITORING:-true}
      - HEALTH_CHECK_INTERVAL=${HEALTH_CHECK_INTERVAL:-300}
      
      # 安全配置
      - ENABLE_API_KEY_ROTATION=${ENABLE_API_KEY_ROTATION:-false}
      - API_KEY_ROTATION_INTERVAL=${API_KEY_ROTATION_INTERVAL:-86400}
    volumes:
      - ./config.json:/app/config.json:ro
      - ./logs:/app/logs
      - ./data:/app/data
      - ./.secrets:/app/.secrets:ro
    depends_on:
      - redis
    networks:
      - ai-proxy-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 256M



  # Redis 缓存 (可选，用于缓存和会话存储)
  redis:
    image: redis:7-alpine
    container_name: ai-proxy-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    volumes:
      - redis_data:/data
    networks:
      - ai-proxy-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 64M
    profiles:
      - cache

  # Nginx 反向代理
  nginx:
    image: nginx:1.25-alpine
    container_name: ai-proxy-nginx
    restart: unless-stopped
    ports:
      - "${NGINX_HTTP_PORT:-80}:80"
      - "${NGINX_HTTPS_PORT:-443}:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - ai-proxy
    networks:
      - ai-proxy-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus 监控
  prometheus:
    image: prom/prometheus:latest
    container_name: ai-proxy-prometheus
    restart: unless-stopped
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - ai-proxy-network
    profiles:
      - monitoring

  # Grafana 可视化
  grafana:
    image: grafana/grafana:latest
    container_name: ai-proxy-grafana
    restart: unless-stopped
    ports:
      - "${GRAFANA_PORT:-3000}:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./config/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - ai-proxy-network
    profiles:
      - monitoring

# 网络配置
networks:
  ai-proxy-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷
volumes:
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
