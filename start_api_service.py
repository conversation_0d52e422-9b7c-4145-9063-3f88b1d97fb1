#!/usr/bin/env python3
"""
AI代理服务启动脚本

快速启动API中转服务
"""

import os
import sys
import json
import argparse
from pathlib import Path


def create_sample_config():
    """创建示例配置文件"""
    config_file = Path("config.json")
    
    if config_file.exists():
        print(f"⚠️  配置文件 {config_file} 已存在")
        return str(config_file)
    
    sample_config = {
        "providers": {
            "openai": [
                {
                    "name": "primary",
                    "api_key": "sk-your-openai-key-here",
                    "config": {}
                }
            ],
            "openrouter": [
                {
                    "name": "primary",
                    "api_key": "sk-or-your-openrouter-key-here",
                    "config": {
                        "timeout": 30,
                        "site_url": "https://yourapp.com",
                        "site_name": "Your App Name"
                    }
                }
            ]
        },
        "default_timeout": 30,
        "max_retries": 3,
        "health_check_interval": 300,
        "enable_monitoring": True,
        "log_level": "INFO"
    }
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(sample_config, f, indent=2, ensure_ascii=False)
        print(f"✅ 创建示例配置文件: {config_file}")
        return str(config_file)
    except Exception as e:
        print(f"❌ 创建配置文件失败: {e}")
        return None


def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    required_modules = [
        ("fastapi", "Web框架"),
        ("uvicorn", "ASGI服务器"),
        ("requests", "HTTP客户端"),
    ]
    
    optional_modules = [
        ("watchdog", "文件监控（热重载）"),
    ]
    
    missing_required = []
    missing_optional = []
    
    for module, description in required_modules:
        try:
            __import__(module)
            print(f"✅ {description} ({module})")
        except ImportError:
            print(f"❌ {description} ({module}) - 缺失")
            missing_required.append(module)
    
    for module, description in optional_modules:
        try:
            __import__(module)
            print(f"✅ {description} ({module})")
        except ImportError:
            print(f"⚠️  {description} ({module}) - 可选，建议安装")
            missing_optional.append(module)
    
    if missing_required:
        print(f"\n❌ 缺少必需依赖: {', '.join(missing_required)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    if missing_optional:
        print(f"\n💡 可安装可选依赖: pip install {' '.join(missing_optional)}")
    
    return True


def show_usage_examples():
    """显示使用示例"""
    print("\n📖 API使用示例:")
    print("-" * 50)
    
    print("1. OpenAI兼容接口:")
    print("""
curl -X POST http://localhost:8000/v1/chat/completions \\
  -H "Content-Type: application/json" \\
  -H "X-Provider: openai" \\
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [{"role": "user", "content": "Hello!"}],
    "max_tokens": 150
  }'
""")
    
    print("2. 通用代理接口:")
    print("""
curl -X POST http://localhost:8000/api/proxy \\
  -H "Content-Type: application/json" \\
  -d '{
    "provider": "openrouter",
    "endpoint": "chat/completions",
    "data": {
      "model": "anthropic/claude-3-sonnet",
      "messages": [{"role": "user", "content": "Hello Claude!"}]
    }
  }'
""")
    
    print("3. 使用OpenAI SDK:")
    print("""
import openai
openai.api_base = "http://localhost:8000/v1"
response = openai.ChatCompletion.create(
    model="gpt-3.5-turbo",
    messages=[{"role": "user", "content": "Hello!"}]
)
""")
    
    print("4. 服务管理:")
    print("""
# 列出可用模型
curl http://localhost:8000/v1/models

# 查看服务状态
curl http://localhost:8000/api/service/status

# 健康检查
curl http://localhost:8000/api/health
""")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AI代理服务启动脚本")
    parser.add_argument("--host", default="0.0.0.0", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    parser.add_argument("--config", help="配置文件路径")
    parser.add_argument("--no-hot-reload", action="store_true", help="禁用热重载")
    parser.add_argument("--create-config", action="store_true", help="创建示例配置文件")
    parser.add_argument("--check-deps", action="store_true", help="检查依赖")
    parser.add_argument("--show-examples", action="store_true", help="显示使用示例")
    parser.add_argument("--test-client", action="store_true", help="运行客户端测试")
    
    args = parser.parse_args()
    
    print("🚀 AI代理服务启动脚本")
    print("=" * 50)
    
    # 检查依赖
    if args.check_deps or not check_dependencies():
        if not check_dependencies():
            sys.exit(1)
        if args.check_deps:
            return
    
    # 创建配置文件
    if args.create_config:
        config_file = create_sample_config()
        if config_file:
            print(f"\n💡 请编辑 {config_file} 文件，填入真实的API密钥")
        return
    
    # 显示使用示例
    if args.show_examples:
        show_usage_examples()
        return
    
    # 运行客户端测试
    if args.test_client:
        print("🧪 启动客户端测试...")
        os.system("python test_api_client.py")
        return
    
    # 检查配置文件
    config_file = args.config or "config.json"
    if not Path(config_file).exists():
        print(f"❌ 配置文件不存在: {config_file}")
        print("请运行: python start_api_service.py --create-config")
        sys.exit(1)
    
    # 启动服务
    print(f"🎯 启动AI代理服务...")
    print(f"📁 配置文件: {config_file}")
    print(f"🌐 服务地址: http://{args.host}:{args.port}")
    print(f"🔥 热重载: {'禁用' if args.no_hot_reload else '启用'}")
    
    # 构建启动命令
    cmd_parts = [
        "python", "-m", "api_proxy", "--web",
        "--host", args.host,
        "--port", str(args.port)
    ]
    
    if args.no_hot_reload:
        cmd_parts.append("--no-hot-reload")
    
    print(f"\n⚡ 启动命令: {' '.join(cmd_parts)}")
    print("\n🎉 服务启动中...")
    print("📱 Web管理界面: http://{}:{}".format(args.host, args.port))
    print("🔗 API文档: http://{}:{}/docs".format(args.host, args.port))
    print("⏹️  按 Ctrl+C 停止服务")
    
    # 显示快速测试命令
    print(f"\n🧪 快速测试:")
    print(f"curl http://{args.host}:{args.port}/api/health")
    
    try:
        os.execvp("python", cmd_parts)
    except KeyboardInterrupt:
        print("\n⏹️  服务已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
