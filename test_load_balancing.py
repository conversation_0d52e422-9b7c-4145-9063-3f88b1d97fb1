#!/usr/bin/env python3
"""
负载均衡功能测试脚本
演示多个提供商实例之间的负载均衡
"""

import json
import time
from api_proxy.config import Config
from api_proxy.proxy_service import ProxyService
from api_proxy.models import ProviderConfig


def test_load_balancing():
    """测试负载均衡功能"""
    print("🔄 负载均衡功能测试")
    print("=" * 50)
    
    # 创建多个OpenRouter实例的配置
    config_data = {
        "providers": {
            "openrouter": [
                {
                    "name": "instance_1",
                    "api_key": "sk-or-test-key-1",
                    "config": {
                        "timeout": 30,
                        "site_url": "https://test1.com",
                        "site_name": "Test App 1"
                    }
                },
                {
                    "name": "instance_2", 
                    "api_key": "sk-or-test-key-2",
                    "config": {
                        "timeout": 30,
                        "site_url": "https://test2.com",
                        "site_name": "Test App 2"
                    }
                },
                {
                    "name": "instance_3",
                    "api_key": "sk-or-test-key-3", 
                    "config": {
                        "timeout": 30,
                        "site_url": "https://test3.com",
                        "site_name": "Test App 3"
                    }
                }
            ]
        },
        "default_timeout": 30,
        "max_retries": 3,
        "health_check_interval": 300,
        "enable_monitoring": True,
        "log_level": "INFO"
    }
    
    # 创建配置对象
    providers = {}
    for provider_type, provider_configs in config_data["providers"].items():
        provider_list = []
        for pc in provider_configs:
            provider_list.append(ProviderConfig(
                name=pc["name"],
                api_key=pc["api_key"],
                config=pc.get("config", {})
            ))
        providers[provider_type] = provider_list
    
    config = Config(
        providers=providers,
        default_timeout=config_data["default_timeout"],
        max_retries=config_data["max_retries"],
        health_check_interval=config_data["health_check_interval"],
        enable_monitoring=config_data["enable_monitoring"],
        log_level=config_data["log_level"]
    )
    
    # 创建代理服务
    service = ProxyService(config)
    
    print(f"📊 配置了 {len(service.providers['openrouter'])} 个OpenRouter实例")
    
    # 测试负载均衡
    print("\n🔄 测试负载均衡 - 连续获取10次提供商实例:")
    print("-" * 50)
    
    provider_usage = {}
    
    for i in range(10):
        try:
            provider = service.get_provider("openrouter")
            provider_name = provider.site_name if hasattr(provider, 'site_name') else f"Provider_{id(provider)}"
            
            # 统计使用次数
            if provider_name not in provider_usage:
                provider_usage[provider_name] = 0
            provider_usage[provider_name] += 1
            
            print(f"第 {i+1:2d} 次: {provider_name}")
            
        except Exception as e:
            print(f"第 {i+1:2d} 次: 错误 - {e}")
    
    # 显示负载均衡统计
    print("\n📈 负载均衡统计:")
    print("-" * 30)
    for provider_name, count in provider_usage.items():
        percentage = (count / 10) * 100
        print(f"{provider_name}: {count} 次 ({percentage:.1f}%)")
    
    # 验证负载均衡效果
    print("\n✅ 负载均衡验证:")
    if len(provider_usage) == 3:
        print("✓ 所有3个实例都被使用")
    else:
        print(f"✗ 只有 {len(provider_usage)} 个实例被使用")
    
    # 检查分布是否相对均匀
    counts = list(provider_usage.values())
    if max(counts) - min(counts) <= 1:
        print("✓ 负载分布相对均匀")
    else:
        print("✗ 负载分布不均匀")


def test_provider_selection_with_real_config():
    """使用真实配置测试提供商选择"""
    print("\n🔧 使用真实配置测试")
    print("=" * 50)
    
    try:
        # 尝试加载真实配置
        config = Config.from_file("config.json")
        service = ProxyService(config)
        
        print("📋 当前配置的提供商:")
        for provider_type, providers in service.providers.items():
            print(f"  {provider_type}: {len(providers)} 个实例")
            
            if len(providers) > 1:
                print(f"\n🔄 测试 {provider_type} 的负载均衡:")
                for i in range(5):
                    provider = service.get_provider(provider_type)
                    provider_id = id(provider)
                    print(f"  第 {i+1} 次: Provider_{provider_id}")
            else:
                print(f"  {provider_type} 只有1个实例，无需负载均衡")
                
    except Exception as e:
        print(f"❌ 加载真实配置失败: {e}")


def show_load_balancing_info():
    """显示负载均衡功能说明"""
    print("\n📚 负载均衡功能说明")
    print("=" * 50)
    
    info = """
🎯 负载均衡算法: Round-Robin (轮询)

📋 工作原理:
1. 为每个提供商类型维护一个轮询索引
2. 每次请求时，选择下一个提供商实例
3. 到达列表末尾时，重新从第一个开始

✅ 优势:
- 简单高效
- 确保所有实例都被使用
- 分布相对均匀
- 无需额外配置

🔧 使用场景:
- 多个API密钥负载分担
- 提高请求并发能力
- 避免单个密钥限流
- 提供故障转移能力

📊 配置示例:
{
  "providers": {
    "openrouter": [
      {"name": "primary", "api_key": "key1", "config": {...}},
      {"name": "backup", "api_key": "key2", "config": {...}},
      {"name": "tertiary", "api_key": "key3", "config": {...}}
    ]
  }
}

🚀 自动功能:
- 每次API调用自动轮询
- 无需手动指定实例
- 透明的负载分配
- 实时统计监控
    """
    
    print(info)


if __name__ == "__main__":
    print("🤖 AI代理服务 - 负载均衡功能测试")
    print("=" * 60)
    
    # 显示功能说明
    show_load_balancing_info()
    
    # 测试负载均衡
    test_load_balancing()
    
    # 测试真实配置
    test_provider_selection_with_real_config()
    
    print("\n🎉 测试完成!")
    print("\n💡 提示: 在Web界面中添加多个同类型提供商实例即可自动启用负载均衡")
