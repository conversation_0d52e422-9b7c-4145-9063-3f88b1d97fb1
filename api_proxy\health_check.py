"""提供者健康检查模块
实现供应商API的健康状态检查和自动故障切换
"""

import time
import logging
from typing import Dict, List
from .providers.base import BaseProvider


class HealthChecker:
    """提供者健康检查

    特性:
    - 定期健康检查
    - 状态缓存
    - 指数避退重试

    Args:
        interval: 检查间隔(秒)
        timeout: 健康检查超时(秒)
        max_retries: 最大重试次数
    """

    def __init__(self, interval: int = 300, timeout: int = 10, max_retries: int = 3):
        if interval <= 0:
            raise ValueError("检查间隔必须大于0")
        if timeout <= 0:
            raise ValueError("超时时间必须大于0")
        if max_retries < 0:
            raise ValueError("最大重试次数不能为负数")

        self.interval = interval
        self.timeout = timeout
        self.max_retries = max_retries
        self.last_check: Dict[str, float] = {}
        self.status: Dict[str, bool] = {}
        self.retry_count: Dict[str, int] = {}
        self.logger = logging.getLogger(__name__)

    def check_provider(self, provider: BaseProvider) -> bool:
        """检查单个提供者健康状况

        Args:
            provider: 要检查的提供者

        Returns:
            bool: 提供者是否健康
        """
        try:
            # 对于OpenAI提供者，使用简单的模型列表请求进行健康检查
            if provider.name == "openai":
                provider.call("models", timeout=self.timeout)
            else:
                # 其他提供者的健康检查逻辑
                pass
            return True
        except Exception as e:
            self.logger.warning(f"提供者 {provider.name} 健康检查失败: {str(e)}")
            return False

    def check_all(self, providers: Dict[str, List[BaseProvider]]):
        """检查所有提供者

        Args:
            providers: 按类型分组的提供者字典
        """
        current_time = time.time()
        for provider_type, provider_list in providers.items():
            for i, provider in enumerate(provider_list):
                provider_key = f"{provider_type}_{i}"

                if (
                    provider_key not in self.last_check
                    or current_time - self.last_check[provider_key] > self.interval
                ):
                    is_healthy = self.check_provider(provider)
                    self.status[provider_key] = is_healthy
                    self.last_check[provider_key] = current_time

                    if is_healthy:
                        self.retry_count[provider_key] = 0
                        self.logger.debug(f"提供者 {provider_key} 健康检查通过")
                    else:
                        self.retry_count[provider_key] = self.retry_count.get(provider_key, 0) + 1
                        self.logger.warning(f"提供者 {provider_key} 健康检查失败 (重试次数: {self.retry_count[provider_key]})")

    def is_healthy(self, provider_type: str, provider_index: int = 0) -> bool:
        """检查指定提供者是否健康

        Args:
            provider_type: 提供者类型
            provider_index: 提供者索引

        Returns:
            bool: 是否健康
        """
        provider_key = f"{provider_type}_{provider_index}"
        return self.status.get(provider_key, True)  # 默认认为是健康的

    def get_healthy_providers(self, providers: Dict[str, List[BaseProvider]]) -> Dict[str, List[BaseProvider]]:
        """获取所有健康的提供者

        Args:
            providers: 所有提供者

        Returns:
            Dict[str, List[BaseProvider]]: 健康的提供者
        """
        healthy_providers = {}

        for provider_type, provider_list in providers.items():
            healthy_list = []
            for i, provider in enumerate(provider_list):
                if self.is_healthy(provider_type, i):
                    healthy_list.append(provider)

            if healthy_list:
                healthy_providers[provider_type] = healthy_list

        return healthy_providers
