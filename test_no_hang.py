#!/usr/bin/env python3
"""
测试修复后是否还会卡住
"""

import requests
import json
import time
import threading

def test_with_timeout():
    """带超时的测试"""
    print("🧪 测试是否还会卡住...")
    
    test_data = {
        "model": "deepseek/deepseek-chat-v3-0324:free",
        "messages": [{"role": "user", "content": "请简单回复'测试'"}],
        "max_tokens": 10,
        "stream": False
    }
    
    headers = {
        "Content-Type": "application/json",
        "X-Provider": "openrouter"
    }
    
    def make_request():
        """发送请求的函数"""
        try:
            print("📡 开始发送请求...")
            response = requests.post(
                "http://localhost:8000/v1/chat/completions",
                headers=headers,
                json=test_data,
                timeout=20  # 20秒超时
            )
            
            print(f"✅ 请求完成，状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"📝 响应内容: {json.dumps(result, ensure_ascii=False)}")
                return True
            else:
                print(f"❌ 请求失败: {response.text}")
                return False
                
        except requests.exceptions.Timeout:
            print("⏰ 请求超时（20秒）")
            return False
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False
    
    # 使用线程来监控请求是否卡住
    result = [None]
    
    def request_thread():
        result[0] = make_request()
    
    thread = threading.Thread(target=request_thread)
    thread.daemon = True
    thread.start()
    
    # 等待最多30秒
    for i in range(30):
        if not thread.is_alive():
            print(f"✅ 请求在{i+1}秒内完成")
            return result[0]
        
        time.sleep(1)
        if i % 5 == 4:  # 每5秒打印一次
            print(f"⏳ 等待中... ({i+1}/30秒)")
    
    print("❌ 请求超过30秒未完成，可能卡住了")
    return False

def test_multiple_requests():
    """测试多个连续请求"""
    print("\n🧪 测试多个连续请求...")
    
    success_count = 0
    total_requests = 3
    
    for i in range(total_requests):
        print(f"\n--- 请求 {i+1}/{total_requests} ---")
        
        test_data = {
            "model": "deepseek/deepseek-chat-v3-0324:free",
            "messages": [{"role": "user", "content": f"第{i+1}个测试请求"}],
            "max_tokens": 5,
            "stream": False
        }
        
        try:
            start_time = time.time()
            response = requests.post(
                "http://localhost:8000/v1/chat/completions",
                headers={"Content-Type": "application/json", "X-Provider": "openrouter"},
                json=test_data,
                timeout=15
            )
            end_time = time.time()
            
            duration = end_time - start_time
            print(f"⏱️  请求耗时: {duration:.2f}秒")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 请求{i+1}成功")
                success_count += 1
            else:
                print(f"❌ 请求{i+1}失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求{i+1}异常: {e}")
        
        # 请求间隔
        if i < total_requests - 1:
            time.sleep(2)
    
    print(f"\n📊 结果: {success_count}/{total_requests} 个请求成功")
    return success_count == total_requests

if __name__ == "__main__":
    print("🔍 测试修复后是否还会卡住")
    print("=" * 40)
    
    # 测试单个请求
    single_success = test_with_timeout()
    
    # 测试多个请求
    multiple_success = test_multiple_requests()
    
    print("\n" + "=" * 40)
    print("📊 测试结果:")
    print(f"  单个请求: {'✅ 正常' if single_success else '❌ 有问题'}")
    print(f"  多个请求: {'✅ 正常' if multiple_success else '❌ 有问题'}")
    
    if single_success and multiple_success:
        print("\n🎉 修复成功！请求不再卡住")
        print("✅ 日志输出正常")
        print("✅ JSON解析正常")
        print("✅ 响应处理正常")
    else:
        print("\n⚠️  仍有问题需要进一步调试")
        print("\n💡 建议:")
        print("1. 检查服务端日志是否有错误")
        print("2. 确认OpenRouter API密钥正确")
        print("3. 检查网络连接")
        print("4. 查看是否有其他进程占用资源")
