// AI代理服务 - 主应用JavaScript文件
// 这个文件包含所有的应用逻辑，避免内联JavaScript导致的CSP问题

// 全局变量
let currentPage = 'dashboard';

// 页面配置
const pageConfig = {
    'dashboard': {
        title: '仪表板',
        endpoint: '/api/dashboard-content'
    },
    'config': {
        title: '配置管理',
        endpoint: '/api/config-content'
    },
    'api-info': {
        title: 'API信息',
        endpoint: '/api/api-info-content'
    },
    'auth': {
        title: '认证管理',
        endpoint: '/api/auth-content'
    }
};

// 显示页面
function showPage(pageId) {
    console.log('📄 showPage被调用，pageId:', pageId, '当前页面:', currentPage);
    if (currentPage === pageId) {
        console.log('📄 页面相同，跳过加载');
        return;
    }

    // 更新导航状态
    console.log('📄 更新导航状态');
    updateNavigation(pageId);

    // 显示加载指示器
    console.log('📄 显示加载指示器');
    showLoading();

    // 加载页面内容
    console.log('📄 开始加载页面内容，端点:', pageConfig[pageId]?.endpoint);
    loadPageContent(pageId);

    currentPage = pageId;
    console.log('📄 页面切换完成，当前页面:', currentPage);
}

// 更新导航状态
function updateNavigation(pageId) {
    // 移除所有活动状态
    document.querySelectorAll('.sidebar .nav-link').forEach(link => {
        link.classList.remove('active');
    });

    // 添加当前页面的活动状态
    const currentLink = document.querySelector(`[onclick="showPage('${pageId}')"]`);
    if (currentLink) {
        currentLink.classList.add('active');
    }

    // 更新页面标题
    const config = pageConfig[pageId];
    if (config) {
        document.getElementById('pageTitle').textContent = config.title;
        document.title = `${config.title} - AI代理服务`;
    }
}

// 显示加载指示器
function showLoading() {
    document.getElementById('pageContent').style.display = 'none';
    document.getElementById('loadingIndicator').style.display = 'block';
}

// 隐藏加载指示器
function hideLoading() {
    document.getElementById('loadingIndicator').style.display = 'none';
    document.getElementById('pageContent').style.display = 'block';
}

// 加载页面内容
function loadPageContent(pageId, retryCount = 0) {
    console.log('🌐 loadPageContent被调用，pageId:', pageId, '重试次数:', retryCount);
    const config = pageConfig[pageId];
    if (!config) {
        console.error('❌ 未知页面配置:', pageId);
        showErrorContent('未知页面');
        return;
    }

    const maxRetries = 3;
    const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 5000); // 指数退避，最大5秒

    console.log('🌐 开始fetch请求:', config.endpoint);
    console.log('🌐 当前页面URL:', window.location.href);
    console.log('🌐 完整请求URL:', new URL(config.endpoint, window.location.origin).href);
    const startTime = performance.now();

    fetch(config.endpoint, {
        timeout: 10000 // 10秒超时
    })
        .then(response => {
            const fetchTime = performance.now() - startTime;
            console.log('🌐 fetch响应收到，耗时:', fetchTime.toFixed(2), 'ms, 状态:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.text();
        })
        .then(html => {
            const totalTime = performance.now() - startTime;
            console.log('🌐 HTML内容获取完成，总耗时:', totalTime.toFixed(2), 'ms, 内容长度:', html.length);
            document.getElementById('pageContent').innerHTML = html;
            console.log('🌐 页面内容已插入DOM');
            hideLoading();

            // 执行页面特定的初始化
            console.log('🌐 准备执行页面特定初始化:', pageId);
            setTimeout(() => {
                initPageSpecificFeatures(pageId);
            }, 50); // 小延迟确保DOM更新完成
        })
        .catch(error => {
            const totalTime = performance.now() - startTime;
            console.error(`❌ 加载${config.title}失败 (尝试 ${retryCount + 1}/${maxRetries + 1}):`, error);
            console.error('❌ 错误详情:', {
                message: error.message,
                name: error.name,
                stack: error.stack,
                endpoint: config.endpoint,
                totalTime: totalTime.toFixed(2) + 'ms'
            });

            if (retryCount < maxRetries) {
                // 显示重试提示
                console.log(`🔄 将在${retryDelay}ms后重试`);
                showRetryMessage(config.title, retryCount + 1, maxRetries + 1);

                // 延迟后重试
                setTimeout(() => {
                    loadPageContent(pageId, retryCount + 1);
                }, retryDelay);
            } else {
                // 最终失败，显示错误
                console.error('❌ 达到最大重试次数，显示错误页面');
                showErrorContent(`加载${config.title}内容失败: ${error.message}`, pageId);
            }
        });
}

function showRetryMessage(title, currentAttempt, maxAttempts) {
    const retryHtml = `
        <div class="alert alert-info" role="alert">
            <i class="fas fa-sync fa-spin"></i>
            正在重试加载${title}... (${currentAttempt}/${maxAttempts})
        </div>
    `;
    document.getElementById('pageContent').innerHTML = retryHtml;
}

// 初始化页面特定功能
function initPageSpecificFeatures(pageId) {
    console.log('🔧 initPageSpecificFeatures被调用，pageId:', pageId);
    switch(pageId) {
        case 'dashboard':
            console.log('🔧 初始化仪表板功能');
            if (typeof initDashboard === 'function') {
                initDashboard();
            } else {
                console.warn('⚠️ initDashboard函数未定义');
            }
            break;
        case 'config':
            console.log('🔧 初始化配置管理功能');
            if (typeof initConfigManagement === 'function') {
                initConfigManagement();
            } else {
                console.warn('⚠️ initConfigManagement函数未定义');
            }
            break;
        case 'api-info':
            console.log('🔧 初始化API信息功能');
            if (typeof initApiInfo === 'function') {
                initApiInfo();
            } else {
                console.warn('⚠️ initApiInfo函数未定义');
            }
            break;
        case 'auth':
            console.log('🔧 初始化认证管理功能');
            if (typeof initAuthManagement === 'function') {
                initAuthManagement();
            } else {
                console.warn('⚠️ initAuthManagement函数未定义');
            }
            break;
        default:
            console.warn('⚠️ 未知页面类型:', pageId);
    }
    console.log('🔧 页面特定功能初始化完成');
}

// 显示错误内容
function showErrorContent(message, pageId = null) {
    const pageContent = document.getElementById('pageContent');

    // 清空内容
    pageContent.innerHTML = '';

    // 创建主错误容器
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger';
    alertDiv.setAttribute('role', 'alert');

    const flexContainer = document.createElement('div');
    flexContainer.className = 'd-flex align-items-center';

    // 错误图标
    const icon = document.createElement('i');
    icon.className = 'fas fa-exclamation-triangle me-2';

    // 错误信息
    const messageDiv = document.createElement('div');
    messageDiv.className = 'flex-grow-1';
    messageDiv.innerHTML = `<strong>加载失败</strong><br><small>${message}</small>`;

    // 重试按钮
    const buttonDiv = document.createElement('div');
    const retryButton = document.createElement('button');
    retryButton.className = 'btn btn-outline-danger btn-sm';
    retryButton.innerHTML = '<i class="fas fa-redo"></i> 重试';
    retryButton.addEventListener('click', () => {
        if (pageId) {
            showPage(pageId);
        } else {
            refreshCurrentPage();
        }
    });

    buttonDiv.appendChild(retryButton);
    flexContainer.appendChild(icon);
    flexContainer.appendChild(messageDiv);
    flexContainer.appendChild(buttonDiv);
    alertDiv.appendChild(flexContainer);

    // 创建帮助信息
    const helpDiv = document.createElement('div');
    helpDiv.className = 'text-center mt-4';

    const helpText = document.createElement('p');
    helpText.className = 'text-muted';
    helpText.textContent = '如果问题持续存在，请检查网络连接或联系管理员';

    const buttonGroup = document.createElement('div');
    buttonGroup.className = 'btn-group';

    // 返回首页按钮
    const homeButton = document.createElement('button');
    homeButton.className = 'btn btn-outline-primary btn-sm';
    homeButton.innerHTML = '<i class="fas fa-home"></i> 返回首页';
    homeButton.addEventListener('click', () => showPage('dashboard'));

    // 刷新页面按钮
    const refreshButton = document.createElement('button');
    refreshButton.className = 'btn btn-outline-secondary btn-sm';
    refreshButton.innerHTML = '<i class="fas fa-sync"></i> 刷新页面';
    refreshButton.addEventListener('click', () => location.reload());

    buttonGroup.appendChild(homeButton);
    buttonGroup.appendChild(refreshButton);
    helpDiv.appendChild(helpText);
    helpDiv.appendChild(buttonGroup);

    // 添加到页面
    pageContent.appendChild(alertDiv);
    pageContent.appendChild(helpDiv);

    hideLoading();
}

// 刷新当前页面
function refreshCurrentPage() {
    showPage(currentPage);
}

// 安全的元素更新函数
function safeUpdateElement(elementId, content) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = content;
    }
}

// 安全的HTML更新函数
function safeUpdateHTML(elementId, html) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = html;
    }
}

// 全局提示函数
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// 复制API地址
function copyApiUrl() {
    const baseUrl = window.location.origin;
    const apiUrl = baseUrl + '/v1/chat/completions';

    navigator.clipboard.writeText(apiUrl).then(() => {
        showAlert('API地址已复制到剪贴板', 'success');
    }).catch(() => {
        showAlert('复制失败', 'danger');
    });
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 DOM加载完成，开始初始化应用...');
    console.log('📍 当前时间:', new Date().toISOString());

    // 添加页面加载指示器
    console.log('📍 显示页面加载指示器');
    showPageLoadingIndicator();

    // 延迟一小段时间确保DOM完全准备好
    setTimeout(() => {
        try {
            console.log('📍 开始应用初始化流程');
            // 默认显示仪表板
            currentPage = 'dashboard';
            console.log('📍 设置当前页面为dashboard，准备调用showPage');
            showPage('dashboard');

            // 初始化页面加载动画
            const mainContent = document.querySelector('main');
            if (mainContent) {
                console.log('📍 找到main元素，添加页面过渡动画');
                mainContent.classList.add('page-transition');
                setTimeout(() => {
                    mainContent.classList.add('loaded');
                    console.log('📍 页面过渡动画完成，隐藏页面加载指示器');
                    hidePageLoadingIndicator();
                }, 100);
            } else {
                console.warn('⚠️ 未找到main元素');
                hidePageLoadingIndicator();
            }

            // 初始化导航链接点击效果和事件处理
            document.querySelectorAll('.sidebar .nav-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // 添加点击效果
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);

                    // 处理页面导航
                    const page = this.getAttribute('data-page');
                    const action = this.getAttribute('data-action');

                    if (page) {
                        showPage(page);
                    } else if (action) {
                        switch(action) {
                            case 'showLogs':
                                showLogs();
                                break;
                            case 'testAPI':
                                testAPI();
                                break;
                        }
                    }
                });
            });

            // 初始化其他按钮事件处理
            document.addEventListener('click', function(e) {
                const target = e.target.closest('[data-action]');
                if (target) {
                    e.preventDefault();
                    const action = target.getAttribute('data-action');
                    const provider = target.getAttribute('data-provider');

                    switch(action) {
                        case 'refreshCurrentPage':
                            refreshCurrentPage();
                            break;
                        case 'runTest':
                            runTest();
                            break;
                        case 'copyApiUrl':
                            copyApiUrl();
                            break;
                        case 'saveConfig':
                            saveConfig();
                            break;
                        case 'addProvider':
                            if (provider) {
                                addProvider(provider);
                            }
                            break;
                        case 'testConfiguration':
                            testConfiguration();
                            break;
                        case 'reloadConfiguration':
                            reloadConfiguration();
                            break;
                        case 'exportConfiguration':
                            exportConfiguration();
                            break;
                        case 'toggleAllProviders':
                            toggleAllProviders();
                            break;
                        case 'refreshStats':
                            refreshStats();
                            break;
                        case 'resetStats':
                            resetStats();
                            break;
                        case 'saveStats':
                            saveStats();
                            break;
                        case 'showDetailedStats':
                            showDetailedStats();
                            break;
                    }
                }

                // 处理页面导航
                const pageTarget = e.target.closest('[data-page]');
                if (pageTarget) {
                    e.preventDefault();
                    const page = pageTarget.getAttribute('data-page');
                    showPage(page);
                }
            });

            // 预热API端点，提高后续加载速度
            console.log('📍 开始预热API端点');
            preloadApiEndpoints();

            console.log('✅ 应用初始化完成');
        } catch (error) {
            console.error('❌ 应用初始化失败:', error);
            console.error('❌ 错误堆栈:', error.stack);
            hidePageLoadingIndicator();
            showAlert('应用初始化失败，请刷新页面重试', 'danger');
        }
    }, 100); // 100ms延迟确保DOM完全准备好
});

function showPageLoadingIndicator() {
    const indicator = document.createElement('div');
    indicator.id = 'pageLoadingIndicator';
    indicator.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center';
    indicator.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
    indicator.style.zIndex = '9999';
    indicator.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <div class="mt-2 text-muted">正在初始化应用...</div>
        </div>
    `;
    document.body.appendChild(indicator);
}

function hidePageLoadingIndicator() {
    const indicator = document.getElementById('pageLoadingIndicator');
    if (indicator) {
        indicator.style.opacity = '0';
        indicator.style.transition = 'opacity 0.3s ease';
        setTimeout(() => {
            indicator.remove();
        }, 300);
    }
}

// 预加载API端点，提高后续访问速度
function preloadApiEndpoints() {
    console.log('开始预加载API端点...');

    const endpoints = [
        '/api/stats',
        '/api/health',
        '/api/auth/keys',
        '/api/auth/models'
    ];

    // 并发预加载，但不阻塞主流程
    endpoints.forEach(endpoint => {
        fetch(endpoint, {
            method: 'GET', // 改为GET请求，因为有些端点不支持HEAD
            cache: 'force-cache'
        })
        .then(response => {
            if (response.ok) {
                console.log(`预加载成功: ${endpoint}`);
            } else {
                console.log(`预加载失败: ${endpoint} - ${response.status}`);
            }
        })
        .catch(error => {
            console.log(`预加载失败: ${endpoint}`, error.message);
        });
    });

    // 预加载页面内容
    setTimeout(() => {
        Object.values(pageConfig).forEach(config => {
            if (config.endpoint !== '/api/dashboard-content') { // 仪表板已经在加载
                fetch(config.endpoint, {
                    method: 'GET',
                    cache: 'force-cache'
                })
                .then(response => {
                    if (response.ok) {
                        console.log(`预加载页面成功: ${config.endpoint}`);
                    } else {
                        console.log(`预加载页面失败: ${config.endpoint} - ${response.status}`);
                    }
                })
                .catch(error => {
                    console.log(`预加载页面失败: ${config.endpoint}`, error.message);
                });
            }
        });
    }, 1000); // 1秒后开始预加载其他页面
}

// 仪表板相关函数
function initDashboard() {
    console.log('📊 initDashboard被调用');
    console.log('📊 当前时间:', new Date().toISOString());

    // 检查必要的DOM元素是否存在
    const pageContent = document.getElementById('pageContent');
    const statsElements = ['totalRequests', 'successfulRequests', 'failedRequests', 'avgResponseTime'];

    console.log('📊 检查DOM元素:');
    console.log('📊 pageContent存在:', !!pageContent);
    statsElements.forEach(id => {
        const element = document.getElementById(id);
        console.log(`📊 ${id}存在:`, !!element);
    });

    // 延迟一点时间确保页面内容已经加载完成
    console.log('📊 设置100ms延迟后开始加载仪表板数据');
    setTimeout(() => {
        console.log('📊 延迟时间到，开始显示加载状态');
        showDashboardLoading();
        console.log('📊 开始更新仪表板数据');
        updateDashboard();
    }, 100);
}

function showDashboardLoading() {
    console.log('显示仪表板加载状态...');

    // 为主要的统计卡片添加加载状态
    const statsElements = ['totalRequests', 'successfulRequests', 'failedRequests', 'avgResponseTime'];
    statsElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.innerHTML = '<i class="fas fa-spinner fa-spin text-primary"></i>';
            element.setAttribute('data-loading', 'true');
        }
    });

    const healthElement = document.getElementById('healthStatus');
    if (healthElement) {
        healthElement.innerHTML = '<i class="fas fa-spinner fa-spin text-primary"></i>';
        healthElement.setAttribute('data-loading', 'true');
    }

    // 在页面内容区域添加一个加载提示（如果还没有的话）
    const pageContent = document.getElementById('pageContent');
    if (pageContent && !pageContent.querySelector('.dashboard-loading-overlay')) {
        const loadingOverlay = document.createElement('div');
        loadingOverlay.className = 'dashboard-loading-overlay position-absolute top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center';
        loadingOverlay.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
        loadingOverlay.style.zIndex = '10';
        loadingOverlay.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-2 text-muted">正在加载仪表板数据...</div>
            </div>
        `;

        // 确保父容器有相对定位
        if (pageContent.style.position !== 'relative') {
            pageContent.style.position = 'relative';
        }

        pageContent.appendChild(loadingOverlay);
    }
}

function hideDashboardLoading() {
    console.log('隐藏仪表板加载状态...');

    // 移除加载覆盖层
    const loadingOverlay = document.querySelector('.dashboard-loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.style.opacity = '0';
        loadingOverlay.style.transition = 'opacity 0.3s ease';
        setTimeout(() => {
            loadingOverlay.remove();
        }, 300);
    }

    // 清除统计元素中的加载图标（只清除标记为loading的元素）
    const statsElements = ['totalRequests', 'successfulRequests', 'failedRequests', 'avgResponseTime'];
    statsElements.forEach(id => {
        const element = document.getElementById(id);
        if (element && element.getAttribute('data-loading') === 'true') {
            element.removeAttribute('data-loading');
            // 不要在这里设置默认值，让updateStatsDisplay处理
        }
    });

    // 清除健康状态中的加载图标
    const healthElement = document.getElementById('healthStatus');
    if (healthElement && healthElement.getAttribute('data-loading') === 'true') {
        healthElement.removeAttribute('data-loading');
        // 不要在这里设置默认值，让updateHealthDisplay处理
    }
}

function updateDashboard() {
    console.log('📈 updateDashboard被调用');
    const startTime = performance.now();

    // 使用Promise.allSettled并发调用API，提高加载速度
    console.log('📈 开始并发API调用');
    const statsPromise = fetch('/api/stats')
        .then(response => {
            console.log('📈 /api/stats响应状态:', response.status);
            return response.json();
        })
        .catch(error => {
            console.warn('📈 获取统计信息失败:', error);
            return { error: error.message };
        });

    const healthPromise = fetch('/api/health')
        .then(response => {
            console.log('📈 /api/health响应状态:', response.status);
            return response.json();
        })
        .catch(error => {
            console.warn('📈 获取健康状态失败:', error);
            return { error: error.message };
        });

    // 并发执行所有API调用
    console.log('📈 等待API调用完成...');
    Promise.allSettled([statsPromise, healthPromise])
        .then(results => {
            const totalTime = performance.now() - startTime;
            console.log('📈 所有API调用完成，总耗时:', totalTime.toFixed(2), 'ms');

            const [statsResult, healthResult] = results;
            console.log('📈 统计信息结果:', statsResult);
            console.log('📈 健康状态结果:', healthResult);

            // 处理统计信息
            if (statsResult.status === 'fulfilled' && !statsResult.value.error) {
                console.log('📈 更新统计信息显示');
                updateStatsDisplay(statsResult.value);
            } else {
                console.warn('📈 统计信息加载失败，使用默认值:', statsResult);
                updateStatsDisplay({
                    summary: {
                        total_requests: 0,
                        total_success: 0,
                        avg_time: 0
                    }
                });
            }

            // 处理健康状态
            if (healthResult.status === 'fulfilled' && !healthResult.value.error) {
                console.log('📈 更新健康状态显示');
                updateHealthDisplay(healthResult.value);
            } else {
                console.warn('📈 健康状态加载失败，使用默认值:', healthResult);
                updateHealthDisplay({
                    status: 'unknown',
                    healthy_providers: [],
                    total_providers: 0
                });
            }

            console.log('📈 仪表板数据加载完成');

            // 隐藏加载状态
            console.log('📈 隐藏仪表板加载状态');
            hideDashboardLoading();
        })
        .catch(error => {
            console.error('仪表板更新失败:', error);
            // 隐藏加载状态并显示错误
            hideDashboardLoading();
            showDashboardError();
        });
}

function showDashboardError() {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'alert alert-warning';
    errorDiv.setAttribute('role', 'alert');

    const icon = document.createElement('i');
    icon.className = 'fas fa-exclamation-triangle';

    const text = document.createTextNode(' 仪表板数据加载失败，请检查服务状态或刷新页面重试。');

    const retryButton = document.createElement('button');
    retryButton.className = 'btn btn-outline-warning btn-sm ms-2';
    retryButton.innerHTML = '<i class="fas fa-redo"></i> 重试';
    retryButton.addEventListener('click', updateDashboard);

    errorDiv.appendChild(icon);
    errorDiv.appendChild(text);
    errorDiv.appendChild(retryButton);

    const pageContent = document.getElementById('pageContent');
    if (pageContent) {
        pageContent.insertBefore(errorDiv, pageContent.firstChild);
    }
}

function updateStatsDisplay(stats) {
    // 安全地获取统计数据，支持嵌套的summary结构
    const summary = stats.summary || stats;

    // 更新请求统计，添加动画效果
    updateStatWithAnimation('totalRequests', summary.total_requests || 0);
    updateStatWithAnimation('successfulRequests', summary.total_success || summary.successful_requests || 0);
    updateStatWithAnimation('failedRequests', (summary.total_requests || 0) - (summary.total_success || summary.successful_requests || 0));

    // 更新响应时间
    const avgTime = summary.avg_time || summary.avg_response_time || 0;
    updateStatWithAnimation('avgResponseTime', avgTime.toFixed(2) + 'ms');

    // 更新提供商统计
    if (stats.provider_stats || Object.keys(stats).length > 1) {
        updateProviderStats(stats);
    }

    console.log('统计数据更新完成:', summary);
}

function updateStatWithAnimation(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
        // 添加更新动画
        element.style.transition = 'all 0.3s ease';
        element.style.transform = 'scale(1.05)';
        element.textContent = value;

        setTimeout(() => {
            element.style.transform = 'scale(1)';
        }, 300);
    }
}

function updateHealthDisplay(health) {
    const statusElement = document.getElementById('healthStatus');
    if (statusElement) {
        let statusClass, statusText;

        switch(health.status) {
            case 'healthy':
                statusClass = 'bg-success';
                statusText = '健康';
                break;
            case 'unhealthy':
                statusClass = 'bg-danger';
                statusText = '异常';
                break;
            default:
                statusClass = 'bg-secondary';
                statusText = '未知';
        }

        statusElement.className = `badge ${statusClass}`;
        statusElement.textContent = statusText;

        // 添加动画效果
        statusElement.style.transition = 'all 0.3s ease';
        statusElement.style.transform = 'scale(1.1)';
        setTimeout(() => {
            statusElement.style.transform = 'scale(1)';
        }, 300);
    }

    updateStatWithAnimation('healthyProviders', health.healthy_providers?.length || 0);
    updateStatWithAnimation('totalProviders', health.total_providers || 0);

    // 更新提供商实例数
    updateStatWithAnimation('providerInstanceCount', health.total_providers || 0);

    console.log('健康状态更新完成:', health);
}

function updateProviderStats(providerStats) {
    // 更新提供商统计信息
    for (const [provider, stats] of Object.entries(providerStats)) {
        const element = document.getElementById(`provider-${provider}-stats`);
        if (element) {
            element.innerHTML = `
                <small class="text-muted">
                    请求: ${stats.requests || 0} |
                    成功: ${stats.success || 0} |
                    失败: ${stats.failures || 0}
                </small>
            `;
        }
    }
}

// 自动刷新数据
setInterval(function() {
    if (currentPage === 'dashboard') {
        updateDashboard();
    }
}, 30000); // 30秒刷新一次

// 全局工具函数
function refreshData() {
    location.reload();
}

function showLogs() {
    fetch('/api/logs')
        .then(response => response.json())
        .then(data => {
            const logContent = document.getElementById('logContent');
            if (data.logs) {
                logContent.textContent = data.logs.join('\n');
            } else {
                logContent.textContent = data.message || '无日志数据';
            }
            new bootstrap.Modal(document.getElementById('logModal')).show();
        })
        .catch(error => {
            console.error('获取日志失败:', error);
            showAlert('获取日志失败', 'danger');
        });
}

function testAPI() {
    new bootstrap.Modal(document.getElementById('testModal')).show();
}

function runTest() {
    const form = document.getElementById('testForm');
    const formData = new FormData(form);

    fetch('/api/test', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        showAlert(data.message || data.error || '测试完成', data.error ? 'danger' : 'success');
        bootstrap.Modal.getInstance(document.getElementById('testModal')).hide();
    })
    .catch(error => {
        console.error('API测试失败:', error);
        showAlert('API测试失败', 'danger');
    });
}

// 配置管理相关函数
function saveConfig() {
    const formData = new FormData(document.getElementById('configForm'));
    const config = {};

    for (const [key, value] of formData.entries()) {
        if (key === 'enable_monitoring') {
            config[key] = true;
        } else if (key.includes('_')) {
            config[key] = isNaN(value) ? value : parseInt(value);
        } else {
            config[key] = value;
        }
    }

    if (!formData.has('enable_monitoring')) {
        config.enable_monitoring = false;
    }

    // 添加提供商配置
    config.providers = getCurrentProviders();

    console.log('保存的配置数据:', config);
    console.log('提供商配置:', config.providers);

    fetch('/api/config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showAlert('配置保存成功', 'success');
        } else {
            showAlert('配置保存失败: ' + data.detail, 'danger');
        }
    })
    .catch(error => {
        showAlert('配置保存失败: ' + error.message, 'danger');
    });
}

// 获取当前提供商配置
function getCurrentProviders() {
    const providers = {
        openai: [],
        openrouter: []
    };

    // 从动态表单中收集提供商配置
    const container = document.getElementById('providersContainer');
    if (container) {
        const providerCards = container.querySelectorAll('[id^="provider_"]');

        providerCards.forEach(card => {
            const cardId = card.id;
            const typeInput = card.querySelector(`input[name="${cardId}_type"]`);
            const nameInput = card.querySelector(`input[name="${cardId}_name"]`);
            const keyInput = card.querySelector(`input[name="${cardId}_api_key"]`);
            const timeoutInput = card.querySelector(`input[name="${cardId}_timeout"]`);

            if (typeInput && nameInput && keyInput) {
                const providerType = typeInput.value;
                const config = {
                    timeout: parseInt(timeoutInput?.value || '30')
                };

                // 添加OpenRouter特有配置
                if (providerType === 'openrouter') {
                    const siteUrlInput = card.querySelector(`input[name="${cardId}_site_url"]`);
                    const siteNameInput = card.querySelector(`input[name="${cardId}_site_name"]`);

                    if (siteUrlInput?.value) config.site_url = siteUrlInput.value;
                    if (siteNameInput?.value) config.site_name = siteNameInput.value;
                }

                providers[providerType].push({
                    name: nameInput.value,
                    api_key: keyInput.value,
                    config: config
                });
            }
        });
    }

    // 如果没有动态配置，尝试从现有配置获取
    if (providers.openai.length === 0 && providers.openrouter.length === 0) {
        try {
            const configElement = document.getElementById('currentConfig');
            if (configElement && configElement.textContent) {
                const config = JSON.parse(configElement.textContent);
                const existingProviders = config.providers || {};

                // 只返回有实际配置的提供商
                const filteredProviders = {};
                Object.keys(existingProviders).forEach(providerType => {
                    const configs = existingProviders[providerType];
                    if (configs && configs.length > 0) {
                        filteredProviders[providerType] = configs;
                    }
                });

                return filteredProviders;
            }
        } catch (e) {
            console.warn('无法获取当前配置，使用默认值');
        }
    }

    // 过滤掉空的提供商配置
    const filteredProviders = {};
    Object.keys(providers).forEach(providerType => {
        if (providers[providerType].length > 0) {
            filteredProviders[providerType] = providers[providerType];
        }
    });

    return filteredProviders;
}

// 添加提供商实例配置
function addProvider(providerType) {
    // 兼容旧的调用方式
    addProviderInstance(providerType);
}

// 添加提供商实例
function addProviderInstance(providerType) {
    console.log('添加提供商实例:', providerType);

    // 确保提供商分组存在
    ensureProviderGroupExists(providerType);

    const container = document.getElementById(`instances-container-${providerType}`);
    if (!container) {
        console.error(`找不到${providerType}实例容器: instances-container-${providerType}`);
        console.log('当前DOM中的容器:', document.querySelectorAll('[id*="instances-container"]'));
        return;
    }

    console.log(`找到${providerType}实例容器，开始添加实例...`);

    // 计算当前该类型提供商的实例数量
    const existingInstances = container.querySelectorAll(`[data-provider-type="${providerType}"]`).length;
    const instanceName = existingInstances === 0 ? 'primary' : `instance_${existingInstances + 1}`;

    // 生成唯一ID
    const providerId = `provider_${providerType}_${Date.now()}`;

    // 创建实例数据
    const instanceData = {
        name: instanceName,
        api_key: '',
        config: {
            timeout: 30
        }
    };

    // 创建实例卡片
    const instanceHtml = createProviderInstanceCard(providerType, providerId, instanceData);

    // 如果容器中有提示信息，先清除
    const emptyMessage = container.querySelector('.text-center.text-muted');
    if (emptyMessage) {
        emptyMessage.remove();
    }

    // 添加到容器
    container.insertAdjacentHTML('beforeend', instanceHtml);

    // 更新实例计数
    updateProviderInstanceCount(providerType);

    const providerName = providerType === 'openai' ? 'OpenAI' : 'OpenRouter';
    showAlert(`已添加${providerName}实例 (${instanceName})`, 'success');

    // 更新统计信息
    updateProviderStats();
}

// 删除提供商实例
function removeProviderInstance(instanceId, providerType) {
    const element = document.getElementById(instanceId);
    if (element) {
        element.remove();

        // 更新实例计数
        updateProviderInstanceCount(providerType);

        // 如果没有实例了，显示提示信息
        const container = document.getElementById(`instances-container-${providerType}`);
        const instances = container.querySelectorAll(`[data-provider-type="${providerType}"]`);

        if (instances.length === 0) {
            const providerName = providerType === 'openai' ? 'OpenAI' : 'OpenRouter';
            const icon = providerType === 'openai' ? 'robot' : 'route';

            const emptyHtml = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-${icon} fa-3x mb-3 opacity-50"></i>
                    <p>还没有配置${providerName}实例</p>
                    <small>点击上方"添加实例"按钮开始配置</small>
                </div>
            `;
            container.innerHTML = emptyHtml;
        }

        const providerName = providerType === 'openai' ? 'OpenAI' : 'OpenRouter';
        showAlert(`已删除${providerName}实例`, 'info');
    }
}

// 更新提供商实例计数
function updateProviderInstanceCount(providerType) {
    const container = document.getElementById(`instances-container-${providerType}`);
    const badge = document.querySelector(`#provider-group-${providerType} .badge`);

    if (container && badge) {
        const count = container.querySelectorAll(`[data-provider-type="${providerType}"]`).length;
        badge.textContent = `${count} 个实例`;
    }
}

// 确保提供商分组存在
function ensureProviderGroupExists(providerType) {
    console.log(`检查${providerType}提供商分组是否存在...`);

    const existingGroup = document.getElementById(`provider-group-${providerType}`);
    if (existingGroup) {
        console.log(`${providerType}分组已存在`);
        return; // 分组已存在
    }

    // 创建空的提供商分组
    const container = document.getElementById('providersContainer');
    if (!container) {
        console.error('找不到主提供商容器 providersContainer');
        return;
    }

    console.log(`为${providerType}创建新的提供商分组...`);

    // 创建分组HTML
    const groupHtml = createProviderGroup(providerType, []);
    container.insertAdjacentHTML('beforeend', groupHtml);

    // 绑定事件
    bindProviderGroupEvents(providerType);

    console.log(`已创建${providerType}提供商分组`);
}

// 创建提供商配置表单
function createProviderForm(providerType, providerId, defaultName = 'primary') {
    const providerName = providerType === 'openai' ? 'OpenAI' : 'OpenRouter';
    const placeholderKey = providerType === 'openai' ? 'sk-your-openai-key-here' : 'sk-or-your-openrouter-key-here';

    let extraFields = '';
    if (providerType === 'openrouter') {
        extraFields = `
            <div class="col-md-6">
                <label class="form-label">网站URL (可选)</label>
                <input type="text" class="form-control" name="${providerId}_site_url"
                       placeholder="https://yourapp.com">
            </div>
            <div class="col-md-6">
                <label class="form-label">网站名称 (可选)</label>
                <input type="text" class="form-control" name="${providerId}_site_name"
                       placeholder="Your App Name">
            </div>
        `;
    }

    return `
        <div class="card mb-3" id="${providerId}" data-provider-type="${providerType}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-${providerType === 'openai' ? 'robot' : 'route'}"></i>
                    ${providerName} 实例 - <span class="text-muted">${defaultName}</span>
                </h6>
                <button type="button" class="btn btn-outline-danger btn-sm delete-provider-btn">
                    <i class="fas fa-trash"></i> 删除实例
                </button>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">实例名称</label>
                        <input type="text" class="form-control" name="${providerId}_name"
                               value="${defaultName}" placeholder="primary" required>
                        <small class="form-text text-muted">用于区分同一提供商的不同实例</small>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">API密钥</label>
                        <input type="password" class="form-control" name="${providerId}_api_key"
                               placeholder="${placeholderKey}" required>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">超时时间 (秒)</label>
                        <input type="number" class="form-control" name="${providerId}_timeout"
                               value="30" min="1" max="300">
                    </div>
                    ${extraFields}
                </div>
                <input type="hidden" name="${providerId}_type" value="${providerType}">
            </div>
        </div>
    `;
}

// 删除提供商配置
function removeProvider(providerId) {
    const element = document.getElementById(providerId);
    if (element) {
        element.remove();
        showAlert('提供商配置已删除', 'info');
    }
}

// 加载现有提供商配置
function loadExistingProviders() {
    try {
        const configElement = document.getElementById('currentConfig');
        if (configElement && configElement.textContent) {
            const config = JSON.parse(configElement.textContent);
            const providers = config.providers || {};

            const container = document.getElementById('providersContainer');
            if (container) {
                container.innerHTML = ''; // 清空容器

                // 为每种提供商类型创建独立的分组
                ['openai', 'openrouter'].forEach(providerType => {
                    const providerList = providers[providerType] || [];
                    const providerGroupHtml = createProviderGroup(providerType, providerList);
                    container.insertAdjacentHTML('beforeend', providerGroupHtml);

                    // 绑定该组内的事件
                    bindProviderGroupEvents(providerType);
                });
            }
        }
    } catch (e) {
        console.error('加载现有提供商配置失败:', e);
    }
}

// 创建提供商分组
function createProviderGroup(providerType, providerList) {
    const providerName = providerType === 'openai' ? 'OpenAI' : 'OpenRouter';
    const icon = providerType === 'openai' ? 'robot' : 'route';
    const color = providerType === 'openai' ? 'primary' : 'info';
    const instanceCount = providerList.length;

    let instancesHtml = '';

    if (instanceCount > 0) {
        providerList.forEach((provider, index) => {
            const providerId = `provider_${providerType}_existing_${index}`;
            instancesHtml += createProviderInstanceCard(providerType, providerId, provider);
        });
    } else {
        instancesHtml = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-${icon} fa-3x mb-3 opacity-50"></i>
                <p>还没有配置${providerName}实例</p>
                <small>点击下方"添加实例"按钮开始配置</small>
            </div>
        `;
    }

    // 检查是否有保存的折叠状态
    const isCollapsed = localStorage.getItem(`provider-${providerType}-collapsed`) === 'true';
    const expandedState = isCollapsed ? 'false' : 'true';
    const showClass = isCollapsed ? '' : 'show';
    const iconClass = isCollapsed ? 'fa-chevron-right' : 'fa-chevron-down';

    return `
        <div class="card mb-4" id="provider-group-${providerType}">
            <div class="card-header provider-group-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center cursor-pointer" data-bs-toggle="collapse" data-bs-target="#collapse-${providerType}" aria-expanded="${expandedState}">
                        <i class="fas ${iconClass} me-2 collapse-icon" id="collapse-icon-${providerType}"></i>
                        <i class="fas fa-${icon} text-${color} me-2"></i>
                        <h6 class="mb-0">${providerName} 提供商</h6>
                        <span class="badge bg-${color} ms-2">${instanceCount} 个实例</span>
                    </div>
                    <button class="btn btn-outline-${color} btn-sm" data-action="addProviderInstance" data-provider="${providerType}">
                        <i class="fas fa-plus"></i> 添加实例
                    </button>
                </div>
            </div>
            <div class="collapse ${showClass}" id="collapse-${providerType}">
                <div class="card-body" id="instances-container-${providerType}">
                    ${instancesHtml}
                </div>
            </div>
        </div>
    `;
}

// 创建提供商实例卡片
function createProviderInstanceCard(providerType, providerId, providerData) {
    const timeout = providerData.config?.timeout || 30;
    const maskedKey = providerData.api_key ? providerData.api_key.substring(0, 8) + '***' : '';

    let extraFields = '';
    if (providerType === 'openrouter') {
        const siteUrl = providerData.config?.site_url || '';
        const siteName = providerData.config?.site_name || '';

        extraFields = `
            <div class="col-md-6">
                <label class="form-label">网站URL (可选)</label>
                <input type="text" class="form-control" name="${providerId}_site_url"
                       value="${siteUrl}" placeholder="https://yourapp.com">
            </div>
            <div class="col-md-6">
                <label class="form-label">网站名称 (可选)</label>
                <input type="text" class="form-control" name="${providerId}_site_name"
                       value="${siteName}" placeholder="Your App Name">
            </div>
        `;
    }

    return `
        <div class="border rounded p-3 mb-3 provider-instance-card" id="${providerId}" data-provider-type="${providerType}">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0">
                    <i class="fas fa-server me-1"></i>
                    实例: <span class="text-primary">${providerData.name}</span>
                </h6>
                <button type="button" class="btn btn-outline-danger btn-sm delete-instance-btn" data-instance-id="${providerId}">
                    <i class="fas fa-trash"></i> 删除
                </button>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <label class="form-label">实例名称</label>
                    <input type="text" class="form-control" name="${providerId}_name"
                           value="${providerData.name}" required>
                    <small class="form-text text-muted">用于区分同一提供商的不同实例</small>
                </div>
                <div class="col-md-6">
                    <label class="form-label">API密钥</label>
                    <div class="input-group">
                        <input type="password" class="form-control" name="${providerId}_api_key"
                               value="${providerData.api_key}" required>
                        <button type="button" class="btn btn-outline-secondary" onclick="togglePasswordVisibility('${providerId}_api_key')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <small class="text-muted">当前: ${maskedKey}</small>
                </div>
                <div class="col-md-6">
                    <label class="form-label">超时时间 (秒)</label>
                    <input type="number" class="form-control" name="${providerId}_timeout"
                           value="${timeout}" min="1" max="300">
                </div>
                ${extraFields}
            </div>
            <input type="hidden" name="${providerId}_type" value="${providerType}">
        </div>
    `;
}

// 绑定提供商分组事件
function bindProviderGroupEvents(providerType) {
    // 绑定添加实例按钮
    const addBtn = document.querySelector(`[data-action="addProviderInstance"][data-provider="${providerType}"]`);
    if (addBtn) {
        addBtn.addEventListener('click', function() {
            addProviderInstance(providerType);
        });
    }

    // 绑定删除实例按钮
    const container = document.getElementById(`instances-container-${providerType}`);
    if (container) {
        container.addEventListener('click', function(e) {
            const deleteBtn = e.target.closest('.delete-instance-btn');
            if (deleteBtn) {
                const instanceId = deleteBtn.getAttribute('data-instance-id');
                removeProviderInstance(instanceId, providerType);
            }
        });
    }

    // 绑定折叠事件
    const collapseElement = document.getElementById(`collapse-${providerType}`);
    const collapseIcon = document.getElementById(`collapse-icon-${providerType}`);

    if (collapseElement && collapseIcon) {
        collapseElement.addEventListener('show.bs.collapse', function() {
            // 展开时图标向下
            collapseIcon.className = 'fas fa-chevron-down me-2 collapse-icon';
            // 保存展开状态
            localStorage.setItem(`provider-${providerType}-collapsed`, 'false');
        });

        collapseElement.addEventListener('hide.bs.collapse', function() {
            // 折叠时图标向右
            collapseIcon.className = 'fas fa-chevron-right me-2 collapse-icon';
            // 保存折叠状态
            localStorage.setItem(`provider-${providerType}-collapsed`, 'true');
        });
    }
}

// 创建带有数据的提供商配置表单
function createProviderFormWithData(providerType, providerId, providerData) {
    const providerName = providerType === 'openai' ? 'OpenAI' : 'OpenRouter';

    let extraFields = '';
    if (providerType === 'openrouter') {
        const siteUrl = providerData.config?.site_url || '';
        const siteName = providerData.config?.site_name || '';

        extraFields = `
            <div class="col-md-6">
                <label class="form-label">网站URL (可选)</label>
                <input type="text" class="form-control" name="${providerId}_site_url"
                       value="${siteUrl}" placeholder="https://yourapp.com">
            </div>
            <div class="col-md-6">
                <label class="form-label">网站名称 (可选)</label>
                <input type="text" class="form-control" name="${providerId}_site_name"
                       value="${siteName}" placeholder="Your App Name">
            </div>
        `;
    }

    const timeout = providerData.config?.timeout || 30;
    const maskedKey = providerData.api_key ? providerData.api_key.substring(0, 8) + '***' : '';

    return `
        <div class="card mb-3" id="${providerId}" data-provider-type="${providerType}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-${providerType === 'openai' ? 'robot' : 'route'}"></i>
                    ${providerName} 实例 - <span class="text-primary">${providerData.name}</span>
                </h6>
                <button type="button" class="btn btn-outline-danger btn-sm delete-provider-btn">
                    <i class="fas fa-trash"></i> 删除实例
                </button>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">实例名称</label>
                        <input type="text" class="form-control" name="${providerId}_name"
                               value="${providerData.name}" required>
                        <small class="form-text text-muted">用于区分同一提供商的不同实例</small>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">API密钥</label>
                        <div class="input-group">
                            <input type="password" class="form-control" name="${providerId}_api_key"
                                   value="${providerData.api_key}" required>
                            <button type="button" class="btn btn-outline-secondary" onclick="togglePasswordVisibility('${providerId}_api_key')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <small class="text-muted">当前: ${maskedKey}</small>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">超时时间 (秒)</label>
                        <input type="number" class="form-control" name="${providerId}_timeout"
                               value="${timeout}" min="1" max="300">
                    </div>
                    ${extraFields}
                </div>
                <input type="hidden" name="${providerId}_type" value="${providerType}">
            </div>
        </div>
    `;
}

// 创建提供商统计信息
function createProviderStats(providers) {
    const openaiCount = providers.openai?.length || 0;
    const openrouterCount = providers.openrouter?.length || 0;
    const totalCount = openaiCount + openrouterCount;

    return `
        <div class="row mb-3">
            <div class="col-md-4">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-robot fa-2x mb-2"></i>
                        <h5>${openaiCount}</h5>
                        <small>OpenAI 实例</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-route fa-2x mb-2"></i>
                        <h5>${openrouterCount}</h5>
                        <small>OpenRouter 实例</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-plug fa-2x mb-2"></i>
                        <h5>${totalCount}</h5>
                        <small>总实例数</small>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// 创建提供商分组标题
function createProviderGroupTitle(providerType, count) {
    const providerName = providerType === 'openai' ? 'OpenAI' : 'OpenRouter';
    const icon = providerType === 'openai' ? 'robot' : 'route';
    const color = providerType === 'openai' ? 'primary' : 'info';

    return `
        <div class="d-flex align-items-center mb-3 mt-4">
            <i class="fas fa-${icon} text-${color} me-2"></i>
            <h6 class="mb-0 text-${color}">${providerName} 提供商</h6>
            <span class="badge bg-${color} ms-2">${count} 个实例</span>
            <hr class="flex-grow-1 ms-3">
        </div>
    `;
}

// 更新提供商统计信息
function updateProviderStats() {
    const providers = getCurrentProviders();
    const container = document.getElementById('providersContainer');

    if (container) {
        const statsElement = container.querySelector('.row.mb-3');
        if (statsElement) {
            const newStats = createProviderStats(providers);
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = newStats;
            statsElement.replaceWith(tempDiv.firstElementChild);
        }
    }
}

// 初始化配置管理
function initConfigManagement() {
    console.log('初始化配置管理功能...');

    // 延迟一点时间确保DOM已经渲染
    setTimeout(() => {
        const container = document.getElementById('providersContainer');
        if (container) {
            loadExistingProviders();
        } else {
            console.warn('providersContainer未找到，延迟重试...');
            // 如果容器还没有渲染，再延迟一点
            setTimeout(() => {
                loadExistingProviders();
            }, 500);
        }
    }, 100);
}

// 切换所有提供商的展开/折叠状态
function toggleAllProviders() {
    const collapseElements = document.querySelectorAll('[id^="collapse-"]');
    const toggleBtn = document.getElementById('toggleAllProvidersBtn');

    if (collapseElements.length === 0) {
        return;
    }

    // 检查当前状态 - 如果有任何一个是展开的，就全部折叠；否则全部展开
    const hasExpanded = Array.from(collapseElements).some(el => el.classList.contains('show'));

    if (hasExpanded) {
        // 全部折叠
        collapseElements.forEach(el => {
            const bsCollapse = new bootstrap.Collapse(el, { toggle: false });
            bsCollapse.hide();
        });

        if (toggleBtn) {
            toggleBtn.innerHTML = '<i class="fas fa-expand-alt"></i> 全部展开';
        }
    } else {
        // 全部展开
        collapseElements.forEach(el => {
            const bsCollapse = new bootstrap.Collapse(el, { toggle: false });
            bsCollapse.show();
        });

        if (toggleBtn) {
            toggleBtn.innerHTML = '<i class="fas fa-compress-alt"></i> 全部折叠';
        }
    }
}

// 切换密码可见性
function togglePasswordVisibility(inputId) {
    const input = document.querySelector(`input[name="${inputId}"]`);
    const button = input?.nextElementSibling?.querySelector('i');

    if (input && button) {
        if (input.type === 'password') {
            input.type = 'text';
            button.className = 'fas fa-eye-slash';
        } else {
            input.type = 'password';
            button.className = 'fas fa-eye';
        }
    }
}

// 刷新统计数据
function refreshStats() {
    console.log('刷新统计数据...');

    fetch('/api/stats')
        .then(response => {
            console.log('响应状态:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('完整响应数据:', JSON.stringify(data, null, 2));
            if (data.status === 'success') {
                showAlert('统计数据已刷新', 'success');
                // 如果当前在仪表板页面，重新加载页面内容
                if (currentPage === 'dashboard') {
                    showPage('dashboard');
                }
            } else {
                const errorMsg = data.error || data.message || JSON.stringify(data);
                console.error('API错误详情:', {
                    status: data.status,
                    error: data.error,
                    message: data.message,
                    fullResponse: data
                });
                showAlert('刷新统计失败: ' + errorMsg, 'danger');
            }
        })
        .catch(error => {
            console.error('请求失败:', error);
            showAlert('刷新统计失败: ' + error.message, 'danger');
        });
}

// 显示详细统计
function showDetailedStats() {
    console.log('显示详细统计...');

    fetch('/api/stats')
        .then(response => {
            console.log('详细统计响应状态:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('详细统计响应数据:', data);
            if (data.status === 'success') {
                displayDetailedStatsModal(data.data);
            } else {
                const errorMsg = data.error || data.message || '未知错误';
                console.error('详细统计API错误:', errorMsg);
                showAlert('获取统计失败: ' + errorMsg, 'danger');
            }
        })
        .catch(error => {
            console.error('详细统计请求失败:', error);
            showAlert('获取统计失败: ' + error.message, 'danger');
        });
}

// 显示详细统计模态框
function displayDetailedStatsModal(stats) {
    // 过滤出实例级别的统计
    const instanceStats = Object.keys(stats)
        .filter(key => key !== 'summary' && typeof stats[key] === 'object')
        .reduce((obj, key) => {
            obj[key] = stats[key];
            return obj;
        }, {});

    if (Object.keys(instanceStats).length === 0) {
        showAlert('暂无详细统计数据', 'info');
        return;
    }

    // 生成模态框HTML
    let modalHtml = `
        <div class="modal fade" id="detailedStatsModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-chart-pie"></i> 详细统计报告
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${generateDetailedStatsContent(instanceStats)}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        <button type="button" class="btn btn-primary" onclick="exportStatsReport()">
                            <i class="fas fa-download"></i> 导出报告
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除现有模态框
    const existingModal = document.getElementById('detailedStatsModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('detailedStatsModal'));
    modal.show();
}

// 生成详细统计内容
function generateDetailedStatsContent(instanceStats) {
    let html = '';

    // 按提供商类型分组
    const grouped = {};
    Object.keys(instanceStats).forEach(instanceName => {
        const [providerType, instanceId] = instanceName.includes(':')
            ? instanceName.split(':', 2)
            : [instanceName, 'default'];

        if (!grouped[providerType]) {
            grouped[providerType] = [];
        }

        grouped[providerType].push({
            instanceId,
            instanceName,
            data: instanceStats[instanceName]
        });
    });

    // 为每个提供商类型生成内容
    Object.keys(grouped).forEach(providerType => {
        const instances = grouped[providerType];
        const providerName = providerType === 'openai' ? 'OpenAI' : 'OpenRouter';
        const icon = providerType === 'openai' ? 'robot' : 'route';
        const color = providerType === 'openai' ? 'primary' : 'info';

        html += `
            <div class="mb-4">
                <h6 class="text-${color} mb-3">
                    <i class="fas fa-${icon}"></i> ${providerName} 提供商 (${instances.length} 个实例)
                </h6>
        `;

        instances.forEach(instance => {
            const data = instance.data;
            const modelUsage = data.model_usage || {};
            const endpointUsage = data.endpoint_usage || {};

            html += `
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">实例: ${instance.instanceId}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>📱 模型使用统计</h6>
                                ${generateModelUsageTable(modelUsage, data.total_requests)}
                            </div>
                            <div class="col-md-6">
                                <h6>🔗 端点使用统计</h6>
                                ${generateEndpointUsageTable(endpointUsage, data.total_requests)}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        html += '</div>';
    });

    return html;
}

// 生成模型使用表格
function generateModelUsageTable(modelUsage, totalRequests) {
    if (Object.keys(modelUsage).length === 0) {
        return '<p class="text-muted">暂无模型使用数据</p>';
    }

    const sorted = Object.entries(modelUsage).sort((a, b) => b[1] - a[1]);

    let html = '<div class="table-responsive"><table class="table table-sm">';
    html += '<thead><tr><th>模型</th><th>次数</th><th>占比</th></tr></thead><tbody>';

    sorted.forEach(([model, count]) => {
        const percentage = totalRequests > 0 ? (count / totalRequests * 100).toFixed(1) : 0;
        const displayName = model.length > 30 ? model.substring(0, 27) + '...' : model;

        html += `
            <tr>
                <td title="${model}">${displayName}</td>
                <td>${count}</td>
                <td>${percentage}%</td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    return html;
}

// 生成端点使用表格
function generateEndpointUsageTable(endpointUsage, totalRequests) {
    if (Object.keys(endpointUsage).length === 0) {
        return '<p class="text-muted">暂无端点使用数据</p>';
    }

    const sorted = Object.entries(endpointUsage).sort((a, b) => b[1] - a[1]);

    let html = '<div class="table-responsive"><table class="table table-sm">';
    html += '<thead><tr><th>端点</th><th>次数</th><th>占比</th></tr></thead><tbody>';

    sorted.forEach(([endpoint, count]) => {
        const percentage = totalRequests > 0 ? (count / totalRequests * 100).toFixed(1) : 0;

        html += `
            <tr>
                <td>${endpoint}</td>
                <td>${count}</td>
                <td>${percentage}%</td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    return html;
}

// 导出统计报告
function exportStatsReport() {
    fetch('/api/stats')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                const reportData = {
                    timestamp: new Date().toISOString(),
                    stats: data.data,
                    file_info: data.file_info
                };

                const dataStr = JSON.stringify(reportData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `stats_report_${new Date().toISOString().split('T')[0]}.json`;
                link.click();

                showAlert('统计报告已导出', 'success');
            }
        })
        .catch(error => {
            showAlert('导出失败: ' + error.message, 'danger');
        });
}

// 保存统计数据
function saveStats() {
    console.log('保存统计数据...');

    fetch('/api/stats/save', {
        method: 'POST'
    })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                const fileInfo = data.file_info || {};
                const fileSize = fileInfo.file_size ? `(${(fileInfo.file_size / 1024).toFixed(1)}KB)` : '';
                showAlert(`统计数据已保存 ${fileSize}`, 'success');
            } else {
                showAlert('保存统计失败: ' + (data.message || '未知错误'), 'danger');
            }
        })
        .catch(error => {
            showAlert('保存统计失败: ' + error.message, 'danger');
        });
}

// 重置统计数据
function resetStats() {
    if (!confirm('确定要重置所有统计数据吗？此操作不可撤销。')) {
        return;
    }

    console.log('重置统计数据...');

    fetch('/api/stats/reset', {
        method: 'POST'
    })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                showAlert('统计数据已重置', 'success');
                // 如果当前在仪表板页面，重新加载页面内容
                if (currentPage === 'dashboard') {
                    showPage('dashboard');
                }
            } else {
                showAlert('重置统计失败: ' + (data.message || '未知错误'), 'danger');
            }
        })
        .catch(error => {
            showAlert('重置统计失败: ' + error.message, 'danger');
        });
}

// 测试配置
function testConfiguration() {
    fetch('/api/health')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'healthy') {
                showAlert('配置测试成功！所有提供商正常', 'success');
            } else {
                showAlert('配置测试警告：' + data.status, 'warning');
            }
        })
        .catch(error => {
            showAlert('配置测试失败: ' + error.message, 'danger');
        });
}

// 重载配置
function reloadConfiguration() {
    fetch('/api/reload', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showAlert('配置重载成功', 'success');
            setTimeout(() => {
                showPage('config'); // 刷新页面
            }, 1000);
        } else {
            showAlert('配置重载失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        showAlert('配置重载失败: ' + error.message, 'danger');
    });
}

// 导出配置
function exportConfiguration() {
    const config = getCurrentProviders();
    const dataStr = JSON.stringify(config, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = 'ai_proxy_config.json';
    link.click();

    showAlert('配置已导出', 'success');
}

// 认证管理相关函数
function initAuthManagement() {
    console.log('初始化认证管理功能...');
    loadApiKeys();
    loadModelMappings();

    // 绑定按钮事件
    const createKeyBtn = document.getElementById('createKeyBtn');
    if (createKeyBtn) {
        createKeyBtn.addEventListener('click', function() {
            console.log('创建密钥按钮被点击');
            showCreateKeyModal();
        });
    }

    const createMappingBtn = document.getElementById('createMappingBtn');
    if (createMappingBtn) {
        createMappingBtn.addEventListener('click', function() {
            console.log('创建映射按钮被点击');
            showCreateMappingModal();
        });
    }

    // 绑定复制按钮事件
    document.addEventListener('click', function(e) {
        if (e.target.closest('[data-copy-target]')) {
            const button = e.target.closest('[data-copy-target]');
            const targetId = button.getAttribute('data-copy-target');
            copyToClipboard(targetId);
        }

        if (e.target.closest('[data-action]')) {
            const button = e.target.closest('[data-action]');
            const action = button.getAttribute('data-action');
            const keyId = button.getAttribute('data-key-id');
            const mappingName = button.getAttribute('data-mapping-name');

            if (action === 'deactivate') {
                deactivateKey(keyId);
            } else if (action === 'activate') {
                activateKey(keyId);
            } else if (action === 'edit') {
                editApiKey(keyId);
            } else if (action === 'delete') {
                deleteApiKey(keyId);
            } else if (action === 'edit-mapping') {
                editModelMapping(mappingName);
            } else if (action === 'delete-mapping') {
                deleteModelMapping(mappingName);
            }
        }
    });
}

function loadApiKeys() {
    fetch('/api/auth/keys')
        .then(response => response.json())
        .then(data => {
            const tbody = document.querySelector('#apiKeysTable tbody');
            if (!tbody) return;

            tbody.innerHTML = '';
            data.keys.forEach(key => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${key.name}</td>
                    <td><code>${key.key_id}</code></td>
                    <td><span class="badge bg-secondary">${key.role}</span></td>
                    <td>${key.allowed_models.join(', ')}</td>
                    <td>${key.rate_limit}/分钟</td>
                    <td>
                        <span class="badge ${key.is_active ? 'bg-success' : 'bg-danger'}">
                            ${key.is_active ? '激活' : '停用'}
                        </span>
                    </td>
                    <td>${new Date(key.created_at).toLocaleString()}</td>
                    <td>
                        <div class="btn-group" role="group">
                            <button class="btn btn-sm btn-outline-primary" data-action="edit" data-key-id="${key.key_id}" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            ${key.is_active ?
                                `<button class="btn btn-sm btn-warning" data-action="deactivate" data-key-id="${key.key_id}" title="停用">
                                    <i class="fas fa-pause"></i>
                                </button>` :
                                `<button class="btn btn-sm btn-success" data-action="activate" data-key-id="${key.key_id}" title="激活">
                                    <i class="fas fa-play"></i>
                                </button>`
                            }
                            <button class="btn btn-sm btn-danger" data-action="delete" data-key-id="${key.key_id}" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        })
        .catch(error => {
            console.error('加载API密钥失败:', error);
        });
}

function loadModelMappings() {
    fetch('/api/auth/models')
        .then(response => response.json())
        .then(data => {
            const tbody = document.querySelector('#modelMappingsTable tbody');
            if (!tbody) return;

            tbody.innerHTML = '';
            data.mappings.forEach(mapping => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><strong>${mapping.public_name}</strong></td>
                    <td><span class="badge bg-info">${mapping.provider}</span></td>
                    <td><code>${mapping.real_model}</code></td>
                    <td>${mapping.description}</td>
                    <td>${mapping.price_multiplier}x</td>
                    <td>
                        <span class="badge ${mapping.is_active ? 'bg-success' : 'bg-danger'}">
                            ${mapping.is_active ? '激活' : '停用'}
                        </span>
                    </td>
                    <td>
                        <div class="btn-group" role="group">
                            <button class="btn btn-sm btn-outline-primary" data-action="edit-mapping" data-mapping-name="${mapping.public_name}" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" data-action="delete-mapping" data-mapping-name="${mapping.public_name}" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        })
        .catch(error => {
            console.error('加载模型映射失败:', error);
        });
}

function deactivateKey(keyId) {
    if (confirm('确定要停用此API密钥吗？')) {
        fetch(`/api/auth/keys/${keyId}/deactivate`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                loadApiKeys();
                showAlert('API密钥已停用', 'success');
            }
        });
    }
}

function activateKey(keyId) {
    fetch(`/api/auth/keys/${keyId}/activate`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            loadApiKeys();
            showAlert('API密钥已激活', 'success');
        }
    });
}

function deleteApiKey(keyId) {
    if (confirm('确定要删除此API密钥吗？此操作不可撤销！')) {
        fetch(`/api/auth/keys/${keyId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                loadApiKeys();
                showAlert('API密钥已删除', 'success');
            }
        })
        .catch(error => {
            console.error('删除API密钥失败:', error);
            showAlert('删除API密钥失败', 'danger');
        });
    }
}

function editApiKey(keyId) {
    // 获取当前密钥信息
    fetch('/api/auth/keys')
        .then(response => response.json())
        .then(data => {
            const key = data.keys.find(k => k.key_id === keyId);
            if (!key) {
                showAlert('找不到API密钥', 'danger');
                return;
            }
            showEditKeyModal(key);
        })
        .catch(error => {
            console.error('获取API密钥信息失败:', error);
            showAlert('获取API密钥信息失败', 'danger');
        });
}

function showCreateKeyModal() {
    // 创建模态框HTML
    const modalHtml = `
        <div class="modal fade" id="createKeyModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">创建API密钥</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="createKeyForm">
                            <div class="mb-3">
                                <label class="form-label">密钥名称</label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">角色</label>
                                <select class="form-control" name="role" required>
                                    <option value="user">用户</option>
                                    <option value="admin">管理员</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">允许的模型 (逗号分隔)</label>
                                <input type="text" class="form-control" name="allowed_models" value="*">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">允许的提供商 (逗号分隔)</label>
                                <input type="text" class="form-control" name="allowed_providers" value="*">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">速率限制 (每分钟)</label>
                                <input type="number" class="form-control" name="rate_limit" value="100">
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="submit" class="btn btn-primary">创建</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 绑定表单提交事件
    const form = document.getElementById('createKeyForm');
    form.addEventListener('submit', createApiKey);

    // 显示模态框
    const modalElement = document.getElementById('createKeyModal');
    new bootstrap.Modal(modalElement).show();
}

function showEditKeyModal(key) {
    // 创建编辑模态框HTML
    const modalHtml = `
        <div class="modal fade" id="editKeyModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">编辑API密钥</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="editKeyForm">
                            <input type="hidden" name="key_id" value="${key.key_id}">
                            <div class="mb-3">
                                <label class="form-label">密钥名称</label>
                                <input type="text" class="form-control" name="name" value="${key.name}" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">允许的模型 (逗号分隔)</label>
                                <input type="text" class="form-control" name="allowed_models" value="${key.allowed_models.join(', ')}">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">允许的提供商 (逗号分隔)</label>
                                <input type="text" class="form-control" name="allowed_providers" value="${key.allowed_providers.join(', ')}">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">速率限制 (每分钟)</label>
                                <input type="number" class="form-control" name="rate_limit" value="${key.rate_limit}" min="1">
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="submit" class="btn btn-primary">更新</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 绑定表单提交事件
    const form = document.getElementById('editKeyForm');
    form.addEventListener('submit', updateApiKey);

    // 显示模态框
    const modalElement = document.getElementById('editKeyModal');
    new bootstrap.Modal(modalElement).show();
}

function updateApiKey(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const keyId = formData.get('key_id');

    fetch(`/api/auth/keys/${keyId}`, {
        method: 'PUT',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // 隐藏编辑模态框
            bootstrap.Modal.getInstance(document.getElementById('editKeyModal')).hide();

            // 重新加载列表
            loadApiKeys();

            showAlert('API密钥更新成功', 'success');
        } else {
            showAlert(data.detail || '更新失败', 'danger');
        }
    })
    .catch(error => {
        console.error('更新API密钥失败:', error);
        showAlert('更新API密钥失败', 'danger');
    });
}

function createApiKey(event) {
    event.preventDefault();

    const formData = new FormData(event.target);

    fetch('/api/auth/keys', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // 隐藏创建模态框
            bootstrap.Modal.getInstance(document.getElementById('createKeyModal')).hide();

            // 显示新密钥
            showNewKeyModal(data.key);

            // 重新加载列表
            loadApiKeys();

            // 重置表单
            event.target.reset();

            showAlert('API密钥创建成功', 'success');
        } else {
            showAlert(data.detail || '创建失败', 'danger');
        }
    })
    .catch(error => {
        console.error('创建API密钥失败:', error);
        showAlert('创建API密钥失败', 'danger');
    });
}

function showNewKeyModal(keyData) {
    // 创建模态框元素
    const modalDiv = document.createElement('div');
    modalDiv.className = 'modal fade';
    modalDiv.id = 'newKeyModal';
    modalDiv.setAttribute('tabindex', '-1');

    const modalDialog = document.createElement('div');
    modalDialog.className = 'modal-dialog';

    const modalContent = document.createElement('div');
    modalContent.className = 'modal-content';

    // 模态框头部
    const modalHeader = document.createElement('div');
    modalHeader.className = 'modal-header';
    modalHeader.innerHTML = `
        <h5 class="modal-title">新API密钥</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
    `;

    // 模态框主体
    const modalBody = document.createElement('div');
    modalBody.className = 'modal-body';

    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-warning';
    alertDiv.innerHTML = '<strong>重要：</strong> 请立即复制并保存此密钥，它只会显示一次！';

    const formGroup = document.createElement('div');
    formGroup.className = 'mb-3';

    const label = document.createElement('label');
    label.className = 'form-label';
    label.textContent = 'API密钥';

    const inputGroup = document.createElement('div');
    inputGroup.className = 'input-group';

    const input = document.createElement('input');
    input.type = 'text';
    input.className = 'form-control';
    input.id = 'newApiKey';
    input.value = keyData;
    input.readOnly = true;

    const copyButton = document.createElement('button');
    copyButton.className = 'btn btn-outline-secondary';
    copyButton.type = 'button';
    copyButton.innerHTML = '<i class="fas fa-copy"></i> 复制';
    copyButton.addEventListener('click', () => copyToClipboard('newApiKey'));

    inputGroup.appendChild(input);
    inputGroup.appendChild(copyButton);
    formGroup.appendChild(label);
    formGroup.appendChild(inputGroup);
    modalBody.appendChild(alertDiv);
    modalBody.appendChild(formGroup);

    // 模态框底部
    const modalFooter = document.createElement('div');
    modalFooter.className = 'modal-footer';
    modalFooter.innerHTML = '<button type="button" class="btn btn-primary" data-bs-dismiss="modal">我已保存</button>';

    // 组装模态框
    modalContent.appendChild(modalHeader);
    modalContent.appendChild(modalBody);
    modalContent.appendChild(modalFooter);
    modalDialog.appendChild(modalContent);
    modalDiv.appendChild(modalDialog);

    document.body.appendChild(modalDiv);
    new bootstrap.Modal(modalDiv).show();
}

function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.select();
        document.execCommand('copy');
        showAlert('已复制到剪贴板', 'success');
    }
}

// 模型映射相关函数
function showCreateMappingModal() {
    console.log('showCreateMappingModal 被调用');
    const modal = document.getElementById('createMappingModal');
    if (modal) {
        console.log('显示现有模态框');
        new bootstrap.Modal(modal).show();
    } else {
        console.log('创建新模态框');
        createMappingModal();
    }
}

function createMappingModal() {
    const modalHtml = `
        <div class="modal fade" id="createMappingModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">创建模型映射</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="createMappingForm">
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label">公开名称</label>
                                <input type="text" class="form-control" name="public_name"
                                       placeholder="如: gpt-4" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">提供商</label>
                                <select class="form-select" name="provider" required>
                                    <option value="openai">OpenAI</option>
                                    <option value="openrouter">OpenRouter</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">实际模型名称</label>
                                <input type="text" class="form-control" name="real_model"
                                       placeholder="如: gpt-4-turbo-preview" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">描述</label>
                                <input type="text" class="form-control" name="description"
                                       placeholder="模型描述" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">价格倍数</label>
                                <input type="number" class="form-control" name="price_multiplier"
                                       value="1.0" step="0.1" min="0.1" required>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="submit" class="btn btn-primary">创建</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 绑定表单提交事件
    document.getElementById('createMappingForm').addEventListener('submit', createModelMapping);

    // 显示模态框
    new bootstrap.Modal(document.getElementById('createMappingModal')).show();
}

function createModelMapping(event) {
    event.preventDefault();

    const formData = new FormData(event.target);

    fetch('/api/auth/models', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // 隐藏模态框
            bootstrap.Modal.getInstance(document.getElementById('createMappingModal')).hide();

            // 重新加载列表
            loadModelMappings();

            // 重置表单
            event.target.reset();

            showAlert('模型映射创建成功', 'success');
        } else {
            showAlert(data.detail || '创建失败', 'danger');
        }
    })
    .catch(error => {
        console.error('创建模型映射失败:', error);
        showAlert('创建模型映射失败', 'danger');
    });
}

function deleteModelMapping(publicName) {
    if (confirm('确定要删除此模型映射吗？此操作不可撤销！')) {
        fetch(`/api/auth/models/${encodeURIComponent(publicName)}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                loadModelMappings();
                showAlert('模型映射已删除', 'success');
            }
        })
        .catch(error => {
            console.error('删除模型映射失败:', error);
            showAlert('删除模型映射失败', 'danger');
        });
    }
}

function editModelMapping(publicName) {
    // 获取当前映射信息
    fetch('/api/auth/models')
        .then(response => response.json())
        .then(data => {
            const mapping = data.mappings.find(m => m.public_name === publicName);
            if (!mapping) {
                showAlert('找不到模型映射', 'danger');
                return;
            }
            showEditMappingModal(mapping);
        })
        .catch(error => {
            console.error('获取模型映射信息失败:', error);
            showAlert('获取模型映射信息失败', 'danger');
        });
}

function showEditMappingModal(mapping) {
    // 创建编辑模态框HTML
    const modalHtml = `
        <div class="modal fade" id="editMappingModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">编辑模型映射</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="editMappingForm">
                            <input type="hidden" name="original_name" value="${mapping.public_name}">
                            <div class="mb-3">
                                <label class="form-label">公开名称</label>
                                <input type="text" class="form-control" name="public_name" value="${mapping.public_name}" readonly>
                                <small class="form-text text-muted">公开名称不可修改</small>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">提供商</label>
                                <select class="form-control" name="provider" required>
                                    <option value="openai" ${mapping.provider === 'openai' ? 'selected' : ''}>OpenAI</option>
                                    <option value="openrouter" ${mapping.provider === 'openrouter' ? 'selected' : ''}>OpenRouter</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">真实模型名</label>
                                <input type="text" class="form-control" name="real_model" value="${mapping.real_model}" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">描述</label>
                                <input type="text" class="form-control" name="description" value="${mapping.description}" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">价格倍数</label>
                                <input type="number" class="form-control" name="price_multiplier" value="${mapping.price_multiplier}" step="0.1" min="0.1">
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" name="is_active" ${mapping.is_active ? 'checked' : ''}>
                                    <label class="form-check-label">激活状态</label>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="submit" class="btn btn-primary">更新</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 绑定表单提交事件
    const form = document.getElementById('editMappingForm');
    form.addEventListener('submit', updateModelMapping);

    // 显示模态框
    const modalElement = document.getElementById('editMappingModal');
    new bootstrap.Modal(modalElement).show();
}

function updateModelMapping(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const originalName = formData.get('original_name');

    // 处理复选框
    if (!formData.has('is_active')) {
        formData.append('is_active', 'false');
    } else {
        formData.set('is_active', 'true');
    }

    fetch(`/api/auth/models/${encodeURIComponent(originalName)}`, {
        method: 'PUT',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // 隐藏编辑模态框
            bootstrap.Modal.getInstance(document.getElementById('editMappingModal')).hide();

            // 重新加载列表
            loadModelMappings();

            showAlert('模型映射更新成功', 'success');
        } else {
            showAlert(data.detail || '更新失败', 'danger');
        }
    })
    .catch(error => {
        console.error('更新模型映射失败:', error);
        showAlert('更新模型映射失败', 'danger');
    });
}

// 导出全局函数供其他脚本使用
window.showPage = showPage;
window.showAlert = showAlert;
window.copyApiUrl = copyApiUrl;
window.refreshCurrentPage = refreshCurrentPage;
window.safeUpdateElement = safeUpdateElement;
window.safeUpdateHTML = safeUpdateHTML;
window.refreshData = refreshData;
window.showLogs = showLogs;
window.testAPI = testAPI;
window.runTest = runTest;
window.saveConfig = saveConfig;
window.getCurrentProviders = getCurrentProviders;
window.addProvider = addProvider;
window.addProviderInstance = addProviderInstance;
window.removeProviderInstance = removeProviderInstance;
window.createProviderGroup = createProviderGroup;
window.createProviderInstanceCard = createProviderInstanceCard;
window.bindProviderGroupEvents = bindProviderGroupEvents;
window.updateProviderInstanceCount = updateProviderInstanceCount;
window.ensureProviderGroupExists = ensureProviderGroupExists;
window.createProviderForm = createProviderForm;
window.removeProvider = removeProvider;
window.loadExistingProviders = loadExistingProviders;
window.createProviderFormWithData = createProviderFormWithData;
window.initConfigManagement = initConfigManagement;
window.toggleAllProviders = toggleAllProviders;
window.refreshStats = refreshStats;
window.showDetailedStats = showDetailedStats;
window.displayDetailedStatsModal = displayDetailedStatsModal;
window.generateDetailedStatsContent = generateDetailedStatsContent;
window.generateModelUsageTable = generateModelUsageTable;
window.generateEndpointUsageTable = generateEndpointUsageTable;
window.exportStatsReport = exportStatsReport;
window.saveStats = saveStats;
window.resetStats = resetStats;
window.togglePasswordVisibility = togglePasswordVisibility;
window.testConfiguration = testConfiguration;
window.reloadConfiguration = reloadConfiguration;
window.exportConfiguration = exportConfiguration;
window.initAuthManagement = initAuthManagement;
window.loadApiKeys = loadApiKeys;
window.loadModelMappings = loadModelMappings;
window.deactivateKey = deactivateKey;
window.activateKey = activateKey;
window.deleteApiKey = deleteApiKey;
window.editApiKey = editApiKey;
window.showCreateKeyModal = showCreateKeyModal;
window.showEditKeyModal = showEditKeyModal;
window.createApiKey = createApiKey;
window.updateApiKey = updateApiKey;
window.deleteModelMapping = deleteModelMapping;
window.editModelMapping = editModelMapping;
window.showEditMappingModal = showEditMappingModal;
window.updateModelMapping = updateModelMapping;
window.copyToClipboard = copyToClipboard;
window.initDashboard = initDashboard;
window.updateDashboard = updateDashboard;
window.showCreateMappingModal = showCreateMappingModal;
window.createMappingModal = createMappingModal;
window.createModelMapping = createModelMapping;
window.showDashboardLoading = showDashboardLoading;
window.hideDashboardLoading = hideDashboardLoading;
window.showDashboardError = showDashboardError;
window.updateStatWithAnimation = updateStatWithAnimation;
window.preloadApiEndpoints = preloadApiEndpoints;
