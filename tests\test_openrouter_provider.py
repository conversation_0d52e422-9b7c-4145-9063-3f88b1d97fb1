# OpenRouter提供商测试
import pytest
import requests
from unittest.mock import Mock, patch
from datetime import datetime
from api_proxy.providers.openrouter import OpenRouterProvider


class TestOpenRouterProvider:
    """OpenRouter提供商测试类"""

    def test_init_valid_params(self):
        """测试有效参数初始化"""
        provider = OpenRouterProvider("sk-or-test123", timeout=30, site_url="https://test.com", site_name="Test App")
        assert provider.api_key == "sk-or-test123"
        assert provider.timeout == 30
        assert provider.base_url == "https://openrouter.ai/api/v1"
        assert provider.site_url == "https://test.com"
        assert provider.site_name == "Test App"
        assert provider.name == "openrouter"
        assert provider.last_used is None

    def test_init_minimal_params(self):
        """测试最小参数初始化"""
        provider = OpenRouterProvider("sk-or-test123")
        assert provider.api_key == "sk-or-test123"
        assert provider.timeout == 30  # 默认值
        assert provider.site_url == ""
        assert provider.site_name == ""

    def test_init_invalid_api_key(self):
        """测试无效API密钥"""
        with pytest.raises(ValueError, match="API密钥必须是非空字符串"):
            OpenRouterProvider("")
        
        with pytest.raises(ValueError, match="API密钥必须是非空字符串"):
            OpenRouterProvider("   ")
        
        with pytest.raises(ValueError, match="API密钥必须是非空字符串"):
            OpenRouterProvider(None)

    def test_init_invalid_timeout(self):
        """测试无效超时参数"""
        with pytest.raises(ValueError, match="超时时间必须是正整数"):
            OpenRouterProvider("sk-or-test123", timeout=0)
        
        with pytest.raises(ValueError, match="超时时间必须是正整数"):
            OpenRouterProvider("sk-or-test123", timeout=-1)
        
        with pytest.raises(ValueError, match="超时时间必须是正整数"):
            OpenRouterProvider("sk-or-test123", timeout="30")

    @patch('requests.post')
    def test_call_success(self, mock_post):
        """测试成功的API调用"""
        # 模拟成功响应
        mock_response = Mock()
        mock_response.json.return_value = {
            "id": "chatcmpl-test123",
            "model": "openai/gpt-4o",
            "choices": [{"message": {"content": "Hello!"}}],
            "usage": {"total_tokens": 10}
        }
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response

        provider = OpenRouterProvider("sk-or-test123", site_url="https://test.com", site_name="Test App")
        
        result = provider.call("chat/completions", model="openai/gpt-4o", messages=[{"role": "user", "content": "Hello"}])
        
        # 验证请求参数
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        assert call_args[1]['url'] == "https://openrouter.ai/api/v1/chat/completions"
        assert call_args[1]['headers']['Authorization'] == "Bearer sk-or-test123"
        assert call_args[1]['headers']['Content-Type'] == "application/json"
        assert call_args[1]['headers']['HTTP-Referer'] == "https://test.com"
        assert call_args[1]['headers']['X-Title'] == "Test App"
        assert call_args[1]['json']['model'] == "openai/gpt-4o"
        assert call_args[1]['timeout'] == 30
        
        # 验证响应
        assert result["id"] == "chatcmpl-test123"
        assert result["model"] == "openai/gpt-4o"
        assert provider.last_used is not None

    @patch('requests.post')
    def test_call_without_optional_headers(self, mock_post):
        """测试不带可选头部的API调用"""
        mock_response = Mock()
        mock_response.json.return_value = {"id": "test"}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response

        provider = OpenRouterProvider("sk-or-test123")  # 不设置site_url和site_name
        
        provider.call("chat/completions", model="openai/gpt-4o", messages=[])
        
        # 验证请求头部不包含可选字段
        call_args = mock_post.call_args
        headers = call_args[1]['headers']
        assert 'HTTP-Referer' not in headers
        assert 'X-Title' not in headers

    def test_call_invalid_endpoint(self):
        """测试无效端点"""
        provider = OpenRouterProvider("sk-or-test123")
        
        with pytest.raises(ValueError, match="端点路径必须是非空字符串"):
            provider.call("")
        
        with pytest.raises(ValueError, match="端点路径必须是非空字符串"):
            provider.call(None)

    def test_call_invalid_retries(self):
        """测试无效重试次数"""
        provider = OpenRouterProvider("sk-or-test123")
        
        with pytest.raises(ValueError, match="重试次数必须在1-5之间"):
            provider.call("chat/completions", max_retries=0)
        
        with pytest.raises(ValueError, match="重试次数必须在1-5之间"):
            provider.call("chat/completions", max_retries=6)

    @patch('requests.post')
    @patch('time.sleep')
    def test_call_with_retries(self, mock_sleep, mock_post):
        """测试重试机制"""
        # 前两次调用失败，第三次成功
        mock_response_fail = Mock()
        mock_response_fail.raise_for_status.side_effect = requests.RequestException("Network error")
        
        mock_response_success = Mock()
        mock_response_success.json.return_value = {"id": "success"}
        mock_response_success.raise_for_status.return_value = None
        
        mock_post.side_effect = [mock_response_fail, mock_response_fail, mock_response_success]

        provider = OpenRouterProvider("sk-or-test123")
        
        result = provider.call("chat/completions", max_retries=3, model="openai/gpt-4o", messages=[])
        
        # 验证重试了3次
        assert mock_post.call_count == 3
        assert mock_sleep.call_count == 2  # 前两次失败后的等待
        assert result["id"] == "success"

    @patch('requests.post')
    def test_call_max_retries_exceeded(self, mock_post):
        """测试超过最大重试次数"""
        mock_response = Mock()
        mock_response.raise_for_status.side_effect = requests.RequestException("Persistent error")
        mock_post.return_value = mock_response

        provider = OpenRouterProvider("sk-or-test123")
        
        with pytest.raises(requests.RequestException):
            provider.call("chat/completions", max_retries=2, model="openai/gpt-4o", messages=[])
        
        assert mock_post.call_count == 2

    def test_str_representation(self):
        """测试字符串表示"""
        provider = OpenRouterProvider("sk-or-verylongkey123456789", site_name="Test App")
        str_repr = str(provider)
        
        assert "sk-or-ve***" in str_repr
        assert "Test App" in str_repr
        assert "OpenRouterProvider" in str_repr

    def test_str_representation_short_key(self):
        """测试短密钥的字符串表示"""
        provider = OpenRouterProvider("short")
        str_repr = str(provider)
        
        assert "***" in str_repr
        assert "short" not in str_repr

    def test_repr_representation(self):
        """测试repr表示"""
        provider = OpenRouterProvider("sk-or-test123")
        assert repr(provider) == str(provider)
