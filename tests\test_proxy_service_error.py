"""代理服务错误测试。

测试ProxyService类的错误处理逻辑。
"""

import pytest
from unittest.mock import MagicMock
from api_proxy.proxy_service import ProxyService
from api_proxy.config import Config


class TestProxyServiceError:
    """代理服务错误测试类"""

    def test_invalid_provider_registration(self):
        """测试无效提供者注册"""
        from api_proxy.models import ProviderConfig
        config = Config({
            "test": [ProviderConfig("test1", "test-key", {})]
        })
        service = ProxyService(config)

        # 注册无效类型的提供者应该在运行时检查
        # 这里我们测试空的提供者类型名称
        with pytest.raises(ValueError):
            service.register_provider("", MagicMock())

    def test_provider_call_error_handling(self):
        """测试提供者调用错误处理"""
        from api_proxy.models import ProviderConfig
        mock_provider = MagicMock()
        mock_provider.call.side_effect = Exception("API Error")

        config = Config({
            "test": [ProviderConfig("test1", "test-key", {})]
        })
        service = ProxyService(config)
        # 替换自动创建的提供者
        service.providers["test"] = [mock_provider]

        with pytest.raises(Exception):
            service.call("test", "endpoint")

    def test_invalid_call_parameters(self):
        """测试无效调用参数"""
        from api_proxy.models import ProviderConfig
        config = Config({
            "test": [ProviderConfig("test1", "test-key", {})]
        })
        service = ProxyService(config)

        # 测试空的提供者类型
        with pytest.raises(ValueError):
            service.call("", "endpoint")

        # 测试空的端点
        with pytest.raises(ValueError):
            service.call("test", "")

    def test_job_failure_analysis(self):
        """测试作业失败分析功能"""
        from api_proxy.models import ProviderConfig
        config = Config({
            "test": [ProviderConfig("test1", "test-key", {})]
        })
        service = ProxyService(config)

        # 测试日志分析
        log_text = "FAILED test.py::test_function - AssertionError: Test failed"
        results = service.analyze_job_failure(log_text)

        assert isinstance(results, list)
        # 应该能识别测试失败
