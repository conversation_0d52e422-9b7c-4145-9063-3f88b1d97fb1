"""
简单的Web界面 - 重新架构
"""
from fastapi import FastAPI, Request, Form, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
import os
from .auth_manager import Auth<PERSON>anager, UserRole
from .proxy_service import ProxyService

class SimpleWebApp:
    def __init__(self, proxy_service=None):
        self.app = FastAPI(title="AI代理服务管理")
        self.proxy_service = proxy_service
        self.auth_manager = AuthManager()
        
        # 设置模板
        template_dir = os.path.join(os.path.dirname(__file__), "simple_templates")
        os.makedirs(template_dir, exist_ok=True)
        self.templates = Jinja2Templates(directory=template_dir)
        
        # 创建模板文件
        self._create_templates()
        
        # 注册路由
        self._register_routes()
    
    def _create_templates(self):
        """创建模板文件"""
        template_dir = os.path.join(os.path.dirname(__file__), "simple_templates")
        
        # 基础模板
        base_template = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}AI代理服务{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/"><i class="fas fa-robot"></i> AI代理服务</a>
            <div class="navbar-nav">
                <a class="nav-link" href="/">首页</a>
                <a class="nav-link" href="/auth">认证管理</a>
                <a class="nav-link" href="/config">配置</a>
            </div>
        </div>
    </nav>
    
    <div class="container mt-4">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.type }} alert-dismissible fade show">
                    {{ message.text }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
        
        {% block content %}{% endblock %}
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    {% block scripts %}{% endblock %}
</body>
</html>'''
        
        with open(os.path.join(template_dir, "base.html"), "w", encoding="utf-8") as f:
            f.write(base_template)
        
        # 认证管理页面
        auth_template = '''{% extends "base.html" %}

{% block title %}认证管理 - AI代理服务{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-key"></i> 认证管理</h2>
    </div>
</div>

<!-- API密钥管理 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-key"></i> API密钥管理</h5>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createKeyModal">
                    <i class="fas fa-plus"></i> 创建密钥
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>名称</th>
                                <th>角色</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for key in api_keys %}
                            <tr>
                                <td>{{ key.name }}</td>
                                <td>{{ key.role.value }}</td>
                                <td>
                                    {% if key.is_active %}
                                        <span class="badge bg-success">激活</span>
                                    {% else %}
                                        <span class="badge bg-danger">停用</span>
                                    {% endif %}
                                </td>
                                <td>{{ key.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                <td>
                                    {% if key.is_active %}
                                        <a href="/auth/deactivate/{{ key.key_id }}" class="btn btn-sm btn-warning">停用</a>
                                    {% else %}
                                        <a href="/auth/activate/{{ key.key_id }}" class="btn btn-sm btn-success">激活</a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 模型映射管理 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-sitemap"></i> 模型映射管理</h5>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createMappingModal">
                    <i class="fas fa-plus"></i> 创建映射
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>公开名称</th>
                                <th>提供商</th>
                                <th>真实模型</th>
                                <th>描述</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for mapping in model_mappings %}
                            <tr>
                                <td>{{ mapping.public_name }}</td>
                                <td>{{ mapping.provider }}</td>
                                <td>{{ mapping.real_model }}</td>
                                <td>{{ mapping.description }}</td>
                                <td>
                                    {% if mapping.is_active %}
                                        <span class="badge bg-success">激活</span>
                                    {% else %}
                                        <span class="badge bg-danger">停用</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建密钥模态框 -->
<div class="modal fade" id="createKeyModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="/auth/create-key">
                <div class="modal-header">
                    <h5 class="modal-title">创建API密钥</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">密钥名称</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">角色</label>
                        <select class="form-select" name="role" required>
                            <option value="user">用户</option>
                            <option value="admin">管理员</option>
                            <option value="guest">访客</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">允许的模型</label>
                        <input type="text" class="form-control" name="allowed_models" value="*" 
                               placeholder="用逗号分隔，* 表示所有模型">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">允许的提供商</label>
                        <input type="text" class="form-control" name="allowed_providers" value="*"
                               placeholder="用逗号分隔，* 表示所有提供商">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">速率限制 (每分钟)</label>
                        <input type="number" class="form-control" name="rate_limit" value="100" min="1">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">创建</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 创建映射模态框 -->
<div class="modal fade" id="createMappingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="/auth/create-mapping">
                <div class="modal-header">
                    <h5 class="modal-title">创建模型映射</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">公开名称</label>
                        <input type="text" class="form-control" name="public_name" required
                               placeholder="如: gpt-4">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">提供商</label>
                        <select class="form-select" name="provider" required>
                            <option value="openai">OpenAI</option>
                            <option value="openrouter">OpenRouter</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">真实模型名</label>
                        <input type="text" class="form-control" name="real_model" required
                               placeholder="如: gpt-4 或 openai/gpt-4">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">描述</label>
                        <input type="text" class="form-control" name="description" required
                               placeholder="模型描述">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">价格倍数</label>
                        <input type="number" class="form-control" name="price_multiplier" 
                               value="1.0" step="0.1" min="0.1">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">创建</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}'''
        
        with open(os.path.join(template_dir, "auth.html"), "w", encoding="utf-8") as f:
            f.write(auth_template)
    
    def _register_routes(self):
        """注册路由"""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def home(request: Request):
            """首页"""
            return self.templates.TemplateResponse("base.html", {
                "request": request
            })
        
        @self.app.get("/auth", response_class=HTMLResponse)
        async def auth_page(request: Request):
            """认证管理页面"""
            api_keys = list(self.auth_manager.api_keys.values())
            model_mappings = list(self.auth_manager.model_mappings.values())
            
            return self.templates.TemplateResponse("auth.html", {
                "request": request,
                "api_keys": api_keys,
                "model_mappings": model_mappings
            })
        
        @self.app.post("/auth/create-key")
        async def create_key(
            request: Request,
            name: str = Form(...),
            role: str = Form(...),
            allowed_models: str = Form(...),
            allowed_providers: str = Form(...),
            rate_limit: int = Form(100)
        ):
            """创建API密钥"""
            try:
                models = [m.strip() for m in allowed_models.split(",") if m.strip()]
                providers = [p.strip() for p in allowed_providers.split(",") if p.strip()]
                
                new_key = self.auth_manager.create_api_key(
                    name=name,
                    role=UserRole(role),
                    allowed_models=models,
                    allowed_providers=providers,
                    rate_limit=rate_limit
                )
                
                # 重定向回认证页面，显示成功消息
                return RedirectResponse(url="/auth?success=key_created", status_code=303)
                
            except Exception as e:
                return RedirectResponse(url=f"/auth?error={str(e)}", status_code=303)
        
        @self.app.post("/auth/create-mapping")
        async def create_mapping(
            request: Request,
            public_name: str = Form(...),
            provider: str = Form(...),
            real_model: str = Form(...),
            description: str = Form(...),
            price_multiplier: float = Form(1.0)
        ):
            """创建模型映射"""
            try:
                self.auth_manager.add_model_mapping(
                    public_name=public_name,
                    provider=provider,
                    real_model=real_model,
                    description=description,
                    price_multiplier=price_multiplier
                )
                
                return RedirectResponse(url="/auth?success=mapping_created", status_code=303)
                
            except Exception as e:
                return RedirectResponse(url=f"/auth?error={str(e)}", status_code=303)


def create_simple_app(proxy_service=None):
    """创建简单Web应用"""
    web_app = SimpleWebApp(proxy_service)
    return web_app.app
