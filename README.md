# AI 代理服务项目

这是一个用于管理和代理多个AI API服务(如OpenAI、OpenRouter)的Python服务，提供负载均衡、故障转移、日志分析和监控功能。

## 项目结构

```
api_proxy/
├── __init__.py              # 包初始化文件
├── config.py                # 全局配置类
├── models.py                # 数据模型定义
├── job_analysis.py          # 作业失败分析模块
├── job_lint_analysis.py     # Lint日志分析模块
├── proxy_service.py         # 代理服务核心
├── providers/
│   ├── __init__.py          # 提供商包初始化
│   ├── base.py              # 基础提供商接口
│   ├── openai.py            # OpenAI提供商实现
│   └── openrouter.py        # OpenRouter提供商实现
└── utils.py                 # 工具函数

tests/
├── unit/                    # 单元测试
├── boundary/                # 边界测试  
├── error/                   # 错误处理测试
└── integration/             # 集成测试
```

## 主要功能

1. **多提供商管理**
   - 支持注册多个API提供商
   - 自动故障转移和负载均衡
   - 健康检查和监控

2. **热重载功能**
   - 配置文件自动监控
   - 无停机服务重载
   - 手动触发重载
   - 重载状态监控
   - 错误处理和回滚

3. **作业失败分析**
   - 自动解析CI/CD作业失败日志
   - 分类错误类型(测试失败、依赖错误等)
   - 提供针对性修复建议
   - 支持LINT工具错误分析

4. **API中转服务**
   - OpenAI兼容的API接口
   - 通用代理API端点
   - 多提供商模型支持
   - RESTful API设计

5. **安全特性**
   - 敏感信息过滤
   - 请求监控和统计
   - 防滥用机制

## 安装和使用

```bash
pip install -r requirements.txt
python -m api_proxy
# 方法1: 使用便捷脚本
python start_dev.py --host 0.0.0.0 --port 8000

# 方法2: 直接使用 uvicorn
uvicorn api_proxy.web_app:app --reload --host 0.0.0.0 --port 8000
```

## 示例代码

### 基本使用

```python
from api_proxy import ProxyService, Config, ProviderConfig

# 配置多个提供商
config = Config({
    "openai": [
        ProviderConfig("openai_account1", "sk-xxx1", {}),
    ],
    "openrouter": [
        ProviderConfig("openrouter_account1", "sk-or-xxx1", {
            "timeout": 30,
            "site_url": "https://myapp.com",
            "site_name": "My AI App"
        }),
    ]
})

service = ProxyService(config)

# 使用OpenAI
response = service.call("openai", "chat/completions",
                       model="gpt-3.5-turbo",
                       messages=[{"role": "user", "content": "Hello!"}])

# 使用OpenRouter访问多种模型
response = service.call("openrouter", "chat/completions",
                       model="openai/gpt-4o",
                       messages=[{"role": "user", "content": "Hello!"}])

# 使用OpenRouter访问Claude
response = service.call("openrouter", "chat/completions",
                       model="anthropic/claude-3-sonnet",
                       messages=[{"role": "user", "content": "Hello Claude!"}])
```

### OpenRouter特性

OpenRouter提供商支持以下特性：
- **多模型访问**: 通过单一API访问OpenAI、Anthropic、Google等多家提供商的模型
- **自动故障转移**: OpenRouter自动处理模型不可用的情况
- **成本优化**: 自动选择最具成本效益的提供商
- **应用归属**: 支持设置site_url和site_name用于OpenRouter排行榜
- **统一接口**: 与OpenAI API完全兼容的接口

### 配置说明

#### OpenRouter配置参数

```json
{
  "providers": {
    "openrouter": [
      {
        "name": "primary",
        "api_key": "sk-or-your-api-key-here",
        "config": {
          "timeout": 30,
          "site_url": "https://yourapp.com",
          "site_name": "Your App Name"
        }
      }
    ]
  }
}
```

**配置参数说明：**
- `timeout`: 请求超时时间（秒），默认30秒
- `site_url`: 可选，您的应用网站URL，用于OpenRouter排行榜
- `site_name`: 可选，您的应用名称，用于OpenRouter排行榜

#### 支持的模型

OpenRouter支持多种模型，使用时需要指定完整的模型名称：

```python
# OpenAI模型
response = service.call("openrouter", "chat/completions",
                       model="openai/gpt-4o",
                       messages=[...])

# Anthropic模型
response = service.call("openrouter", "chat/completions",
                       model="anthropic/claude-3-sonnet",
                       messages=[...])

# Google模型
response = service.call("openrouter", "chat/completions",
                       model="google/gemini-pro",
                       messages=[...])
```

## 测试套件

```bash
pytest tests/  # 运行所有测试
```

## 热重载功能

### 启用热重载

```bash
# 启动Web界面（默认启用热重载）
python -m api_proxy --web

# 禁用热重载
python -m api_proxy --web --no-hot-reload
```

### 功能特性

1. **自动监控**: 自动监控配置文件变化，无需重启服务
2. **无停机重载**: 配置更新时平滑切换，不影响正在进行的请求
3. **错误处理**: 配置错误时自动回滚到上一个有效配置
4. **状态监控**: 实时查看重载状态和历史记录

### API接口

```bash
# 手动触发重载
curl -X POST http://localhost:8000/api/reload

# 获取重载状态
curl http://localhost:8000/api/reload/status

# 获取服务状态
curl http://localhost:8000/api/service/status
```

### 编程接口

```python
from api_proxy.hot_reload import HotReloadManager, ServiceReloader
from api_proxy import ProxyService

# 创建服务重载器
service_reloader = ServiceReloader(ProxyService)

# 创建热重载管理器
hot_reload_manager = HotReloadManager("config.json")
hot_reload_manager.add_reload_callback(service_reloader.reload_service)

# 启动监控
hot_reload_manager.start_monitoring()

# 手动触发重载
success = hot_reload_manager.manual_reload()

# 获取当前服务
current_service = service_reloader.get_service()
```

### 信号支持

在Unix系统上，可以使用信号触发重载：

```bash
# 发送USR1信号触发重载
kill -USR1 <进程ID>
```

## API中转服务

### 对外提供API服务

启动服务后，其他人可以通过HTTP API调用您的AI代理服务：

```bash
# 启动服务（对外开放）
python -m api_proxy --web --host 0.0.0.0 --port 8000
```

### OpenAI兼容接口

```bash
# 聊天补全API
curl -X POST http://your-server:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "X-Provider: openai" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [{"role": "user", "content": "Hello!"}],
    "temperature": 0.7,
    "max_tokens": 150
  }'

# 列出可用模型
curl http://your-server:8000/v1/models
```

### 通用代理接口

```bash
# 代理请求
curl -X POST http://your-server:8000/api/proxy \
  -H "Content-Type: application/json" \
  -d '{
    "provider": "openrouter",
    "endpoint": "chat/completions",
    "data": {
      "model": "anthropic/claude-3-sonnet",
      "messages": [{"role": "user", "content": "Hello Claude!"}],
      "max_tokens": 100
    }
  }'
```

### 使用OpenAI SDK

```python
import openai

# 配置使用您的代理服务
openai.api_base = "http://your-server:8000/v1"
openai.api_key = "your-token"  # 可选

response = openai.ChatCompletion.create(
    model="gpt-3.5-turbo",
    messages=[{"role": "user", "content": "Hello!"}]
)
```

### 客户端测试

```bash
# 运行API客户端测试
python test_api_client.py
```

详细的API使用说明请参考 [API_USAGE.md](API_USAGE.md)。

## 安装

```bash
pip install -r requirements.txt
```

## 快速开始

```python
from api_proxy import ProxyService, Config, ProviderConfig

config = Config({
    "openai": [
        ProviderConfig("account1", "sk-xxx1", {}),
    ]
})

proxy = ProxyService(config)
response = proxy.call("openai", "chat/completions", model="gpt-3.5-turbo", messages=[...])
```

## CI/CD 问题诊断

项目包含 CI/CD 错误诊断工具:

```python
from api_proxy.ci_fixer import CIFixer

fixer = CIFixer()
for suggestion in fixer.diagnose_and_fix():
    print(suggestion)
```

## 开发

运行测试:
```bash
pytest tests/
```

代码格式化:
```bash
black .
flake8 .
```

## 贡献指南

欢迎提交 Pull Request。请确保:
- 通过所有测试
- 遵循代码风格指南
- 更新相关文档

## 许可
MIT License
