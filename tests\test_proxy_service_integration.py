"""代理服务集成测试。

测试ProxyService与其他组件的集成。
"""

import pytest
from unittest.mock import patch, MagicMock
from api_proxy.proxy_service import ProxyService
from api_proxy.config import Config
from api_proxy.models import ProviderConfig
from api_proxy.providers.openai import OpenAIProvider


class TestProxyServiceIntegration:
    """代理服务集成测试类"""

    @pytest.fixture
    def real_config(self):
        """真实配置fixture"""
        return Config(
            {
                "openai": [
                    ProviderConfig("account1", "sk-test1", {}),
                    ProviderConfig("account2", "sk-test2", {}),
                ]
            }
        )

    @patch.object(OpenAIProvider, "call")
    def test_full_workflow(self, mock_call, real_config):
        """测试完整工作流程"""
        mock_call.return_value = {"result": "success"}

        service = ProxyService(real_config)
        response = service.call("openai", "chat/completions", messages=[])

        assert response == {"result": "success"}
        mock_call.assert_called_once_with("chat/completions", messages=[])

    def test_round_robin_provider_selection(self, real_config):
        """测试轮询选择提供者"""
        service = ProxyService(real_config)

        # 获取提供者列表
        providers = service.providers["openai"]
        assert len(providers) == 2

        # 测试轮询机制
        provider1 = service.get_provider("openai")
        provider2 = service.get_provider("openai")

        # 由于轮询机制，应该选择不同的提供者
        assert provider1 is not None
        assert provider2 is not None
        # 第二次调用选择第二个提供者
        provider2 = service.get_provider("openai")

        assert provider1 != provider2

    def test_job_failure_analysis_integration(self):
        """测试完整的作业失败分析工作流"""
        config = Config({})
        service = ProxyService(config)

        # 模拟CI/CD作业日志
        log_text = """Running tests...
FAILED tests/test_service.py::TestProxyService::test_register_provider - AssertionError: Expected True, got False
ERROR: Could not import module 'missing_module'
[WARNING] Some warning message
Running black...
Oh no! 💥 💔 💥 1 file would be reformatted, 3 files would fail to reformat."""

        results = service.analyze_job_failure(log_text)
        assert len(results) == 3

        # 验证错误类型
        error_types = {r.error_type for r in results}
        assert JobErrorType.TEST_FAILURE in error_types
        assert JobErrorType.DEPENDENCY_ERROR in error_types
        assert JobErrorType.LINT_ERROR in error_types

        # 验证解决方案
        for result in results:
            assert result.solution
            assert isinstance(result.solution, str)
            assert result.timestamp
