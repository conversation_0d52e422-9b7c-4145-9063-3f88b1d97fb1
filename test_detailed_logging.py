#!/usr/bin/env python3
"""
测试详细日志记录功能

这个脚本会测试OpenRouter provider的详细日志记录，
帮助诊断API调用失败的问题。
"""

import os
import sys
import json
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from api_proxy.providers.openrouter import OpenRouterProvider
from api_proxy.config import Config
from api_proxy.models import ProviderConfig


def setup_detailed_logging():
    """设置详细的日志配置"""
    # 创建日志目录
    Path('logs').mkdir(exist_ok=True)
    
    # 配置日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 配置根日志记录器
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('logs/detailed_test.log', mode='w', encoding='utf-8')
        ]
    )
    
    # 设置特定模块的日志级别
    logging.getLogger('api_proxy.providers.openrouter').setLevel(logging.INFO)
    logging.getLogger('api_proxy.proxy_service').setLevel(logging.INFO)
    
    print("✅ 详细日志配置完成")
    print("📁 日志文件: logs/detailed_test.log")


def load_test_config():
    """加载测试配置"""
    config_file = "config.json"
    
    if not Path(config_file).exists():
        print(f"❌ 配置文件不存在: {config_file}")
        print("请先运行: python start_api_service.py --create-config")
        return None
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 构建配置对象
        providers = {}
        for provider_type, configs in data.get('providers', {}).items():
            provider_configs = []
            for config in configs:
                provider_configs.append(ProviderConfig(
                    name=config['name'],
                    api_key=config['api_key'],
                    config=config.get('config', {})
                ))
            providers[provider_type] = provider_configs
        
        config = Config(
            providers=providers,
            default_timeout=data.get('default_timeout', 30),
            max_retries=data.get('max_retries', 3),
            health_check_interval=data.get('health_check_interval', 300),
            enable_monitoring=data.get('enable_monitoring', True),
            log_level=data.get('log_level', 'INFO')
        )
        
        print(f"✅ 配置加载成功")
        print(f"   提供商类型: {list(config.providers.keys())}")
        
        return config
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return None


def test_openrouter_provider(config: Config):
    """测试OpenRouter provider"""
    logger = logging.getLogger(__name__)
    
    if 'openrouter' not in config.providers:
        print("❌ 配置中没有OpenRouter提供商")
        return False
    
    openrouter_configs = config.providers['openrouter']
    if not openrouter_configs:
        print("❌ OpenRouter配置为空")
        return False
    
    # 获取第一个OpenRouter配置
    provider_config = openrouter_configs[0]
    
    print(f"🧪 测试OpenRouter提供商: {provider_config.name}")
    print(f"   API密钥: {provider_config.api_key[:20]}...")
    
    # 检查API密钥是否为占位符
    if provider_config.api_key in ["sk-or-your-openrouter-key-here", "123"]:
        print("❌ API密钥为占位符，请在config.json中设置真实的OpenRouter API密钥")
        return False
    
    try:
        # 创建provider实例
        provider = OpenRouterProvider(
            api_key=provider_config.api_key,
            timeout=provider_config.config.get('timeout', 30),
            site_url=provider_config.config.get('site_url', ''),
            site_name=provider_config.config.get('site_name', '')
        )
        
        print(f"✅ Provider实例创建成功: {provider}")
        
        # 测试API调用
        print("\n🚀 开始测试API调用...")
        print("=" * 60)
        
        test_request = {
            "model": "deepseek/deepseek-chat-v3-0324:free",
            "messages": [
                {"role": "user", "content": "Hello! Please respond with just 'Hi there!'"}
            ],
            "max_tokens": 10,
            "temperature": 0.1
        }
        
        logger.info("🎯 开始详细日志测试")
        logger.info(f"   测试请求: {test_request}")
        
        response = provider.call("chat/completions", **test_request)
        
        print("=" * 60)
        print("✅ API调用成功!")
        print(f"📥 响应数据: {json.dumps(response, indent=2, ensure_ascii=False)}")
        
        return True
        
    except Exception as e:
        print("=" * 60)
        print(f"❌ API调用失败: {e}")
        print(f"   错误类型: {type(e).__name__}")
        
        # 记录详细错误信息
        import traceback
        logger.error("完整错误堆栈:")
        for line in traceback.format_exc().split('\n'):
            if line.strip():
                logger.error(f"  {line}")
        
        return False


def main():
    """主函数"""
    print("🔍 OpenRouter详细日志测试")
    print("=" * 50)
    
    # 设置详细日志
    setup_detailed_logging()
    
    # 加载配置
    config = load_test_config()
    if not config:
        sys.exit(1)
    
    # 测试OpenRouter
    success = test_openrouter_provider(config)
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 测试完成 - API调用成功")
    else:
        print("❌ 测试完成 - API调用失败")
        print("\n💡 请检查以下内容:")
        print("   1. logs/detailed_test.log 中的详细日志")
        print("   2. OpenRouter API密钥是否正确")
        print("   3. 网络连接是否正常")
        print("   4. 模型名称是否正确")
    
    print(f"\n📋 详细日志已保存到: logs/detailed_test.log")


if __name__ == "__main__":
    main()
