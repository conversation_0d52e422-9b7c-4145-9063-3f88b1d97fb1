"""Lint作业分析测试模块"""

import pytest
from api_proxy.job_lint_analysis import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LintErrorType


class TestLintAnalyzer:
    """Lint分析器测试类"""

    @pytest.fixture
    def analyzer(self):
        return LintAnalyzer()

    def test_black_formatting_error(self, analyzer):
        """测试Black格式化错误分析"""
        log = "would reformat src/api_proxy/main.py"
        results = analyzer.analyze_log(log)

        assert len(results) == 1
        assert results[0].error_type == LintErrorType.BLACK_FORMATTING
        assert "src/api_proxy/main.py" in results[0].file_path
        assert "black ." in results[0].solution

    def test_flake8_syntax_error(self, analyzer):
        """测试Flake8语法错误分析"""
        log = "src/api_proxy/utils.py:15:1: E302 expected 2 blank lines, found 1"
        results = analyzer.analyze_log(log)

        assert len(results) == 1
        assert results[0].error_type == LintErrorType.FLAKE8_SYNTAX
        assert results[0].line_no == 15
        assert "E302" in results[0].message

    def test_multiple_errors(self, analyzer):
        """测试混合错误分析"""
        log = """would reformat src/api_proxy/main.py
src/api_proxy/utils.py:15:1: E302 expected 2 blank lines, found 1
Oh no! 💥 💔 💥 3 files would be reformatted"""

        results = analyzer.analyze_log(log)
        assert len(results) == 3
        assert (
            sum(1 for r in results if r.error_type == LintErrorType.BLACK_FORMATTING)
            == 1
        )
        assert (
            sum(1 for r in results if r.error_type == LintErrorType.BLACK_FAILED) == 1
        )
        assert (
            sum(1 for r in results if r.error_type == LintErrorType.FLAKE8_SYNTAX) == 1
        )
