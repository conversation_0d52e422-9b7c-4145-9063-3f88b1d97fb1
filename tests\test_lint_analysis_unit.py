"""Lint分析单元测试模块

测试LintAnalyzer类的核心功能实现
"""

import pytest
from datetime import datetime
from api_proxy.job_lint_analysis import LintAnalyzer, LintErrorType, LintFailure


class TestLintAnalyzerUnit:
    """Lint分析器单元测试类"""

    @pytest.fixture
    def analyzer(self):
        return LintAnalyzer()

    def test_parse_black_formatting(self, analyzer):
        """测试解析Black格式化错误"""
        log = "would reformat src/main.py"
        result = analyzer.analyze_log(log)

        assert len(result) == 1
        assert isinstance(result[0], LintFailure)
        assert result[0].error_type == LintErrorType.BLACK_FORMATTING
        assert "src/main.py" in result[0].file_path
        assert "运行 black 命令" in result[0].solution

    def test_parse_flake8_syntax_error(self, analyzer):
        """测试解析Flake8语法错误"""
        log = "src/utils.py:10:1: E302 expected 2 blank lines"
        result = analyzer.analyze_log(log)

        assert len(result) == 1
        assert result[0].error_type == LintErrorType.FLAKE8_SYNTAX
        assert result[0].line_no == 10
        assert "E302" in result[0].message
