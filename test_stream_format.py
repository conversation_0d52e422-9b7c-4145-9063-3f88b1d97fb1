#!/usr/bin/env python3
"""
测试流式响应格式和分隔符
"""

import requests
import json

def test_stream_format():
    """测试流式响应格式"""
    print("🌊 测试流式响应格式和分隔符...")
    
    # 测试数据
    test_data = {
        "model": "deepseek/deepseek-chat-v3-0324:free",
        "messages": [{"role": "user", "content": "请说一句话"}],
        "max_tokens": 50,
        "stream": True  # 启用流式
    }
    
    headers = {
        "Content-Type": "application/json",
        "X-Provider": "openrouter"
    }
    
    print("📡 发送流式请求...")
    
    try:
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            headers=headers,
            json=test_data,
            stream=True,  # 重要：客户端也要启用流式
            timeout=30
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📋 响应头:")
        for key, value in response.headers.items():
            print(f"   {key}: {value}")
        
        if response.status_code == 200:
            print("\n🌊 流式数据:")
            print("-" * 60)
            
            chunk_count = 0
            total_content = ""
            
            # 逐块读取流式数据
            for chunk in response.iter_content(chunk_size=None, decode_unicode=True):
                if chunk:
                    chunk_count += 1
                    print(f"📦 块 {chunk_count}:")
                    print(f"   原始数据: {repr(chunk)}")
                    print(f"   长度: {len(chunk)} 字符")
                    
                    # 尝试解析SSE格式
                    lines = chunk.split('\n')
                    for line in lines:
                        if line.strip():
                            print(f"   行: {repr(line)}")
                            
                            # 如果是data:开头的行
                            if line.startswith('data: '):
                                data_content = line[6:]  # 去掉"data: "前缀
                                if data_content == '[DONE]':
                                    print(f"   ✅ 流结束标记")
                                else:
                                    try:
                                        json_data = json.loads(data_content)
                                        if 'choices' in json_data and len(json_data['choices']) > 0:
                                            delta = json_data['choices'][0].get('delta', {})
                                            content = delta.get('content', '')
                                            if content:
                                                total_content += content
                                                print(f"   💬 内容: {repr(content)}")
                                    except json.JSONDecodeError as e:
                                        print(f"   ❌ JSON解析失败: {e}")
                    
                    print("-" * 40)
                    
                    # 限制输出块数
                    if chunk_count >= 10:
                        print("   ... (限制输出，停止显示更多块)")
                        break
            
            print(f"\n📊 总结:")
            print(f"   总块数: {chunk_count}")
            print(f"   完整内容: {repr(total_content)}")
            
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_stream_with_iter_lines():
    """使用iter_lines测试流式响应"""
    print("\n🔄 使用iter_lines测试...")
    
    test_data = {
        "model": "deepseek/deepseek-chat-v3-0324:free",
        "messages": [{"role": "user", "content": "请说一句话"}],
        "max_tokens": 30,
        "stream": True
    }
    
    headers = {
        "Content-Type": "application/json",
        "X-Provider": "openrouter"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            headers=headers,
            json=test_data,
            stream=True,
            timeout=30
        )
        
        if response.status_code == 200:
            print("🌊 使用iter_lines读取:")
            print("-" * 60)
            
            line_count = 0
            total_content = ""
            
            for line in response.iter_lines(decode_unicode=True):
                if line:
                    line_count += 1
                    print(f"📝 行 {line_count}: {repr(line)}")
                    
                    # 解析SSE格式
                    if line.startswith('data: '):
                        data_content = line[6:]
                        if data_content == '[DONE]':
                            print(f"   ✅ 流结束")
                            break
                        else:
                            try:
                                json_data = json.loads(data_content)
                                if 'choices' in json_data and len(json_data['choices']) > 0:
                                    delta = json_data['choices'][0].get('delta', {})
                                    content = delta.get('content', '')
                                    if content:
                                        total_content += content
                                        print(f"   💬 内容: {repr(content)}")
                            except json.JSONDecodeError:
                                pass
                    
                    # 限制输出行数
                    if line_count >= 20:
                        print("   ... (限制输出)")
                        break
            
            print(f"\n📊 iter_lines总结:")
            print(f"   总行数: {line_count}")
            print(f"   完整内容: {repr(total_content)}")
            
    except Exception as e:
        print(f"❌ iter_lines测试异常: {e}")

def check_service():
    """检查服务状态"""
    try:
        response = requests.get("http://localhost:8000/api/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def main():
    """主函数"""
    print("🌊 流式响应格式测试")
    print("=" * 60)
    
    # 检查服务
    if not check_service():
        print("❌ 服务未运行")
        print("💡 请先启动服务: python start_api_service.py")
        return
    
    print("✅ 服务运行正常")
    print("\n📋 测试说明:")
    print("1. 测试流式响应的原始格式")
    print("2. 检查分隔符和编码")
    print("3. 验证SSE格式是否正确")
    print("4. 对比不同读取方式的结果")
    
    # 开始测试
    test_stream_format()
    test_stream_with_iter_lines()
    
    print("\n" + "=" * 60)
    print("📊 格式分析:")
    print("🔍 当前使用的分隔符:")
    print("   - 媒体类型: text/event-stream; charset=utf-8")
    print("   - 行分隔符: \\n (换行符)")
    print("   - SSE格式: data: {json}\\n")
    print("   - 结束标记: data: [DONE]\\n")
    
    print("\n💡 如果看到乱码，可能原因:")
    print("   1. 编码问题 - UTF-8解码失败")
    print("   2. 客户端解析问题 - 不支持SSE格式")
    print("   3. 分隔符不匹配 - 客户端期望不同格式")
    print("   4. 数据截断 - 网络传输问题")

if __name__ == "__main__":
    main()
