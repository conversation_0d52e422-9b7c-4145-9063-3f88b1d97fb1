"""作业失败分析服务

包含作业失败日志分析、错误归类、解决方案推荐等功能
"""

from datetime import datetime
from dataclasses import dataclass
from enum import Enum, auto
from typing import List, Optional
import re


class JobErrorType(Enum):
    """作业错误类型枚举"""

    TEST_FAILURE = auto()  # 测试失败
    LINT_ERROR = auto()  # 代码规范问题
    DEPENDENCY_ERROR = auto()  # 依赖问题
    CONFIG_ERROR = auto()  # 配置错误
    TIMEOUT = auto()  # 超时
    PERMISSION_ERROR = auto()  # 权限问题
    NETWORK_ERROR = auto()  # 网络问题
    MEMORY_ERROR = auto()  # 内存不足


@dataclass
class JobFailureAnalysis:
    """作业失败分析结果模型"""

    error_type: JobErrorType  # 错误类型
    error_message: str  # 错误信息
    file_path: Optional[str]  # 相关文件路径
    line_no: Optional[int]  # 相关行号
    solution: str  # 修复建议
    timestamp: datetime  # 时间戳


class JobAnalyzer:
    """作业日志分析器"""

    ERROR_PATTERNS = {
        JobErrorType.TEST_FAILURE: [
            r'FAILED (.+?) - (.*)',  # pytest失败格式
            r'AssertionError: (.*)',  # 断言错误
            r'ERROR: Job failed: exit status \d+',  # GitLab CI失败状态
        ],
        JobErrorType.DEPENDENCY_ERROR: [
            r'ModuleNotFoundError: No module named \'(.+?)\'',
            r'ImportError: (.*)',
            r'ERROR: Dependency installation failed',  # GitLab CI依赖安装失败
        ],
        JobErrorType.LINT_ERROR: [
            r'would reformat (.+?)(?:\r\n|\r|\n|$)',  # black格式化
            r'(.+?):\d+:\d+: E\d+ .*',  # flake8错误
            r'gitlab-ci\.yml:.*has errors',  # GitLab CI配置错误
        ],
        JobErrorType.CONFIG_ERROR: [
            r'Invalid CI config file',  # GitLab CI配置无效
            r'gitlab-ci\.yml:.*unknown key',  # 未知配置项
        ],
    }

    SOLUTIONS = {
        JobErrorType.TEST_FAILURE: "检查失败的测试用例并修复断言错误\n建议: 1) 检查测试用例 2) 查看详细日志 3) 本地运行测试",
        JobErrorType.DEPENDENCY_ERROR: "安装缺失的依赖包: pip install {missing_package}\n建议: 1) 检查requirements.txt 2) 验证Python版本",
        JobErrorType.LINT_ERROR: "运行代码格式化工具: black {file_path}\n建议: 1) 本地运行black/isort 2) 提交格式化后的代码",
        JobErrorType.CONFIG_ERROR: "检查.gitlab-ci.yml配置\n建议: 1) 使用CI Lint工具验证 2) 检查缩进和语法",
        JobErrorType.TIMEOUT: "增加作业超时时间\n建议: 1) 在.gitlab-ci.yml中添加timeout设置 2) 优化长时间运行的任务",
    }

    def analyze_log(
        self, log_text: str, redact_sensitive: bool = True
    ) -> List[JobFailureAnalysis]:
        """分析作业日志并返回失败分析结果

        参数:
            log_text: 要分析的日志文本
            redact_sensitive: 是否自动过滤敏感信息(默认为True)

        返回:
            List[JobFailureAnalysis]: 失败分析结果列表

        异常:
            ValueError: 如果输入日志文本不是字符串
        """
        # 输入验证
        if not isinstance(log_text, str):
            raise ValueError("日志文本必须是字符串类型")

        if not log_text.strip():
            return []

        # 敏感信息过滤
        if redact_sensitive:
            from .utils import redact_sensitive_info
            log_text = redact_sensitive_info(log_text)

        results = []
        processed_lines = set()  # 避免重复处理相同的错误行

        for line in log_text.splitlines():
            line = line.strip()
            if not line or line in processed_lines:
                continue

            for error_type, patterns in self.ERROR_PATTERNS.items():
                for pattern in patterns:
                    try:
                        match = re.search(pattern, line)
                        if match:
                            # 构造解决方案信息
                            solution = self._get_solution(error_type, match.groups())

                            # 提取文件路径和行号(如果适用)
                            file_path = None
                            line_no = None

                            if len(match.groups()) > 0:
                                file_path = match.group(1)
                                # 验证文件路径格式
                                if file_path and not self._is_valid_file_path(file_path):
                                    file_path = None

                            if len(match.groups()) > 1:
                                try:
                                    line_no = int(match.group(2))
                                    if line_no <= 0:
                                        line_no = None
                                except (ValueError, IndexError):
                                    line_no = None

                            results.append(
                                JobFailureAnalysis(
                                    error_type=error_type,
                                    error_message=line,
                                    file_path=file_path,
                                    line_no=line_no,
                                    solution=solution,
                                    timestamp=datetime.now(),
                                )
                            )
                            processed_lines.add(line)
                            break  # 每个错误只匹配第一个匹配的模式
                    except re.error as e:
                        # 正则表达式错误，记录日志但继续处理
                        import logging
                        logging.getLogger(__name__).warning(f"正则表达式错误: {e}")
                        continue

        return results

    def _get_solution(self, error_type: JobErrorType, match_groups: tuple) -> str:
        """获取针对性的解决方案

        Args:
            error_type: 错误类型
            match_groups: 正则匹配组

        Returns:
            str: 解决方案文本
        """
        solution_template = self.SOLUTIONS.get(error_type, "请检查日志获取详细信息")

        try:
            if "{missing_package}" in solution_template and len(match_groups) > 0:
                package_name = match_groups[0]
                # 验证包名格式
                if package_name and self._is_valid_package_name(package_name):
                    return solution_template.format(missing_package=package_name)
            elif "{file_path}" in solution_template and len(match_groups) > 0:
                file_path = match_groups[0]
                if file_path and self._is_valid_file_path(file_path):
                    return solution_template.format(file_path=file_path)
        except (IndexError, KeyError):
            pass

        return solution_template

    def _is_valid_file_path(self, file_path: str) -> bool:
        """验证文件路径格式

        Args:
            file_path: 文件路径

        Returns:
            bool: 是否为有效的文件路径
        """
        if not file_path or not isinstance(file_path, str):
            return False

        # 基本的文件路径验证
        import os
        try:
            # 检查路径长度
            if len(file_path) > 260:  # Windows路径长度限制
                return False

            # 检查是否包含危险字符
            dangerous_chars = ['<', '>', '|', '*', '?']
            if any(char in file_path for char in dangerous_chars):
                return False

            # 检查文件扩展名是否合理
            valid_extensions = ['.py', '.txt', '.log', '.yml', '.yaml', '.json', '.md', '.rst']
            if '.' in file_path:
                ext = os.path.splitext(file_path)[1].lower()
                return ext in valid_extensions or ext == ''

            return True
        except Exception:
            return False

    def _is_valid_package_name(self, package_name: str) -> bool:
        """验证Python包名格式

        Args:
            package_name: 包名

        Returns:
            bool: 是否为有效的包名
        """
        if not package_name or not isinstance(package_name, str):
            return False

        # Python包名验证
        import re
        # 包名只能包含字母、数字、下划线和连字符
        pattern = r'^[a-zA-Z][a-zA-Z0-9_-]*$'
        return bool(re.match(pattern, package_name)) and len(package_name) <= 100
