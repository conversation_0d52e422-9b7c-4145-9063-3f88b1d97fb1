#!/usr/bin/env python3
"""
模型使用统计功能测试脚本
演示每个实例的模型调用统计
"""

import json
import time
import random
from api_proxy.config import Config
from api_proxy.proxy_service import ProxyService
from api_proxy.models import ProviderConfig


def create_test_config():
    """创建测试配置，包含多个实例"""
    config_data = {
        "providers": {
            "openrouter": [
                {
                    "name": "primary",
                    "api_key": "sk-or-test-key-primary",
                    "config": {
                        "timeout": 30,
                        "site_url": "https://primary.com",
                        "site_name": "Primary Instance"
                    }
                },
                {
                    "name": "backup", 
                    "api_key": "sk-or-test-key-backup",
                    "config": {
                        "timeout": 30,
                        "site_url": "https://backup.com",
                        "site_name": "Backup Instance"
                    }
                }
            ]
        },
        "default_timeout": 30,
        "max_retries": 3,
        "health_check_interval": 300,
        "enable_monitoring": True,
        "log_level": "INFO"
    }
    
    # 创建配置对象
    providers = {}
    for provider_type, provider_configs in config_data["providers"].items():
        provider_list = []
        for pc in provider_configs:
            provider_list.append(ProviderConfig(
                name=pc["name"],
                api_key=pc["api_key"],
                config=pc.get("config", {})
            ))
        providers[provider_type] = provider_list
    
    return Config(
        providers=providers,
        default_timeout=config_data["default_timeout"],
        max_retries=config_data["max_retries"],
        health_check_interval=config_data["health_check_interval"],
        enable_monitoring=config_data["enable_monitoring"],
        log_level=config_data["log_level"]
    )


def simulate_model_calls(service: ProxyService, num_calls: int = 50):
    """模拟不同模型的API调用"""
    print(f"\n🔄 模拟 {num_calls} 次不同模型的API调用...")
    print("-" * 60)
    
    # 常用的模型列表
    models = [
        "openai/gpt-4",
        "openai/gpt-3.5-turbo",
        "anthropic/claude-3-haiku",
        "anthropic/claude-3-sonnet",
        "meta-llama/llama-2-70b-chat",
        "google/gemini-pro",
        "mistralai/mistral-7b-instruct",
        "cohere/command-r-plus"
    ]
    
    # 端点列表
    endpoints = [
        "chat/completions",
        "completions",
        "embeddings"
    ]
    
    for i in range(num_calls):
        try:
            # 随机选择模型和端点
            model = random.choice(models)
            endpoint = random.choice(endpoints)
            
            # 模拟不同的响应时间
            response_time = random.uniform(0.5, 3.0)
            
            # 模拟成功和失败（85%成功率）
            success = random.random() < 0.85
            error_type = None if success else random.choice(["TimeoutError", "RateLimitError", "AuthenticationError"])
            
            # 获取提供商实例
            provider = service.get_provider("openrouter")
            instance_name = service._get_provider_instance_name("openrouter", provider)
            
            # 记录请求统计
            service.monitor.record_request(
                provider=instance_name,
                duration=response_time,
                success=success,
                error_type=error_type,
                model=model,
                endpoint=endpoint
            )
            
            status = "✅" if success else "❌"
            print(f"第 {i+1:2d} 次: {instance_name} | {model} | {endpoint} | {status} ({response_time:.3f}s)")
            
            # 随机延迟
            time.sleep(random.uniform(0.05, 0.15))
            
        except Exception as e:
            print(f"第 {i+1:2d} 次: 异常 - {e}")


def display_model_stats(service: ProxyService):
    """显示详细的模型使用统计"""
    print("\n📊 模型使用统计报告")
    print("=" * 80)
    
    stats = service.monitor.get_stats()
    
    # 过滤出实例级别的统计
    instance_stats = {k: v for k, v in stats.items() if k != 'summary' and isinstance(v, dict)}
    
    if not instance_stats:
        print("❌ 暂无实例统计数据")
        return
    
    # 按提供商类型分组显示
    grouped_stats = {}
    for instance_name, instance_data in instance_stats.items():
        if ':' in instance_name:
            provider_type, instance_id = instance_name.split(':', 1)
        else:
            provider_type = instance_name
            instance_id = 'default'
        
        if provider_type not in grouped_stats:
            grouped_stats[provider_type] = []
        
        grouped_stats[provider_type].append({
            'instance_id': instance_id,
            'instance_name': instance_name,
            'data': instance_data
        })
    
    for provider_type, instances in grouped_stats.items():
        provider_name = provider_type.upper()
        print(f"\n🔧 {provider_name} 提供商 ({len(instances)} 个实例)")
        print("-" * 50)
        
        for instance in instances:
            data = instance['data']
            instance_id = instance['instance_id']
            
            total_requests = data.get('total_requests', 0)
            successful_requests = data.get('successful_requests', 0)
            failed_requests = data.get('failed_requests', 0)
            success_rate = data.get('success_rate', 0)
            model_usage = data.get('model_usage', {})
            endpoint_usage = data.get('endpoint_usage', {})
            
            print(f"\n📋 实例: {instance_id}")
            print(f"   总请求数: {total_requests}")
            print(f"   成功请求: {successful_requests} ({success_rate:.1f}%)")
            print(f"   失败请求: {failed_requests}")
            
            if model_usage:
                print(f"   📱 模型使用统计 ({len(model_usage)} 个模型):")
                # 按使用次数排序
                sorted_models = sorted(model_usage.items(), key=lambda x: x[1], reverse=True)
                for model_name, count in sorted_models:
                    percentage = (count / total_requests * 100) if total_requests > 0 else 0
                    print(f"     - {model_name}: {count} 次 ({percentage:.1f}%)")
            
            if endpoint_usage:
                print(f"   🔗 端点使用统计:")
                sorted_endpoints = sorted(endpoint_usage.items(), key=lambda x: x[1], reverse=True)
                for endpoint_name, count in sorted_endpoints:
                    percentage = (count / total_requests * 100) if total_requests > 0 else 0
                    print(f"     - {endpoint_name}: {count} 次 ({percentage:.1f}%)")
    
    # 显示全局模型统计
    print(f"\n🌍 全局模型使用统计")
    print("-" * 30)
    
    global_model_usage = {}
    global_endpoint_usage = {}
    
    for instance_data in instance_stats.values():
        model_usage = instance_data.get('model_usage', {})
        endpoint_usage = instance_data.get('endpoint_usage', {})
        
        for model, count in model_usage.items():
            global_model_usage[model] = global_model_usage.get(model, 0) + count
        
        for endpoint, count in endpoint_usage.items():
            global_endpoint_usage[endpoint] = global_endpoint_usage.get(endpoint, 0) + count
    
    if global_model_usage:
        print("📱 最受欢迎的模型:")
        sorted_global_models = sorted(global_model_usage.items(), key=lambda x: x[1], reverse=True)
        for i, (model_name, count) in enumerate(sorted_global_models[:5], 1):
            print(f"   {i}. {model_name}: {count} 次")
    
    if global_endpoint_usage:
        print("\n🔗 最常用的端点:")
        sorted_global_endpoints = sorted(global_endpoint_usage.items(), key=lambda x: x[1], reverse=True)
        for i, (endpoint_name, count) in enumerate(sorted_global_endpoints, 1):
            print(f"   {i}. {endpoint_name}: {count} 次")


def test_model_statistics():
    """测试模型统计功能"""
    print("🤖 模型使用统计功能测试")
    print("=" * 60)
    
    # 创建测试配置
    config = create_test_config()
    service = ProxyService(config)
    
    print(f"📋 配置了 {len(service.providers['openrouter'])} 个OpenRouter实例:")
    for i, provider_config in enumerate(config.providers['openrouter']):
        print(f"  {i+1}. {provider_config.name}")
    
    # 模拟API调用
    simulate_model_calls(service, 50)
    
    # 显示详细统计
    display_model_stats(service)
    
    # 保存统计数据
    print(f"\n💾 保存统计数据...")
    service.monitor.save_now()
    
    file_info = service.monitor.get_stats_file_info()
    if file_info.get('exists'):
        print(f"✅ 统计数据已保存到: {file_info['file_path']}")
        print(f"📁 文件大小: {file_info.get('file_size', 0)} 字节")


if __name__ == "__main__":
    test_model_statistics()
    
    print("\n🎉 测试完成!")
    print("\n💡 提示:")
    print("1. 每个实例的模型使用情况都会被单独跟踪")
    print("2. 支持显示最常用的模型和端点")
    print("3. 在Web界面中可以查看模型使用徽章")
    print("4. 统计数据会持久化保存")
