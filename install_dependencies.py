#!/usr/bin/env python3
"""
依赖安装脚本

自动安装AI代理服务所需的所有依赖包
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """运行命令并显示结果"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description}成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败: {e}")
        if e.stdout:
            print(f"输出: {e.stdout}")
        if e.stderr:
            print(f"错误: {e.stderr}")
        return False


def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"🐍 Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True


def install_requirements():
    """安装requirements.txt中的依赖"""
    requirements_file = Path("requirements.txt")
    
    if not requirements_file.exists():
        print("❌ requirements.txt文件不存在")
        return False
    
    # 升级pip
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "升级pip"):
        print("⚠️  pip升级失败，继续安装其他依赖")
    
    # 安装依赖
    return run_command(f"{sys.executable} -m pip install -r requirements.txt", "安装基础依赖")


def install_optional_dependencies():
    """安装可选依赖"""
    optional_deps = [
        ("watchdog==3.0.0", "文件监控（热重载功能）"),
        ("jinja2", "模板引擎（Web界面）"),
        ("python-multipart", "文件上传支持"),
    ]
    
    print("\n📦 安装可选依赖...")
    
    for package, description in optional_deps:
        success = run_command(f"{sys.executable} -m pip install {package}", f"安装 {package} ({description})")
        if not success:
            print(f"⚠️  {package} 安装失败，某些功能可能不可用")


def verify_installation():
    """验证安装"""
    print("\n🔍 验证安装...")
    
    # 检查核心模块
    try:
        import requests
        import fastapi
        import uvicorn
        print("✅ 核心依赖验证成功")
    except ImportError as e:
        print(f"❌ 核心依赖验证失败: {e}")
        return False
    
    # 检查可选模块
    optional_modules = [
        ("watchdog", "文件监控"),
        ("jinja2", "模板引擎"),
    ]
    
    for module, description in optional_modules:
        try:
            __import__(module)
            print(f"✅ {description} ({module}) 可用")
        except ImportError:
            print(f"⚠️  {description} ({module}) 不可用")
    
    # 测试导入项目模块
    try:
        from api_proxy import ProxyService, Config, ProviderConfig
        print("✅ 项目模块导入成功")
    except ImportError as e:
        print(f"❌ 项目模块导入失败: {e}")
        return False
    
    return True


def create_sample_config():
    """创建示例配置文件"""
    config_file = Path("config.json")
    
    if config_file.exists():
        print(f"⚠️  配置文件 {config_file} 已存在，跳过创建")
        return
    
    sample_config = {
        "providers": {
            "openai": [
                {
                    "name": "primary",
                    "api_key": "sk-your-openai-key-here",
                    "config": {}
                }
            ],
            "openrouter": [
                {
                    "name": "primary", 
                    "api_key": "sk-or-your-openrouter-key-here",
                    "config": {
                        "timeout": 30,
                        "site_url": "https://yourapp.com",
                        "site_name": "Your App Name"
                    }
                }
            ]
        },
        "default_timeout": 30,
        "max_retries": 3,
        "health_check_interval": 300,
        "enable_monitoring": True,
        "log_level": "INFO"
    }
    
    try:
        import json
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(sample_config, f, indent=2, ensure_ascii=False)
        print(f"✅ 创建示例配置文件: {config_file}")
        print("💡 请编辑配置文件，填入真实的API密钥")
    except Exception as e:
        print(f"❌ 创建配置文件失败: {e}")


def main():
    """主函数"""
    print("🚀 AI代理服务依赖安装脚本")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查当前目录
    if not Path("api_proxy").exists():
        print("❌ 请在项目根目录运行此脚本")
        sys.exit(1)
    
    print(f"\n📁 当前目录: {os.getcwd()}")
    
    # 安装依赖
    print("\n" + "=" * 50)
    if not install_requirements():
        print("❌ 基础依赖安装失败")
        sys.exit(1)
    
    # 安装可选依赖
    install_optional_dependencies()
    
    # 验证安装
    print("\n" + "=" * 50)
    if not verify_installation():
        print("❌ 安装验证失败")
        sys.exit(1)
    
    # 创建示例配置
    print("\n" + "=" * 50)
    create_sample_config()
    
    # 显示使用说明
    print("\n🎉 安装完成！")
    print("\n📖 使用说明:")
    print("  1. 编辑 config.json 文件，填入真实的API密钥")
    print("  2. 启动Web界面: python -m api_proxy --web")
    print("  3. 访问 http://localhost:8000 查看管理界面")
    print("  4. 运行演示: python example_hot_reload.py")
    
    print("\n🔗 相关链接:")
    print("  - OpenAI API密钥: https://platform.openai.com/api-keys")
    print("  - OpenRouter API密钥: https://openrouter.ai/keys")
    
    print("\n💡 提示:")
    print("  - 热重载功能需要 watchdog 模块")
    print("  - 如果安装失败，请检查网络连接和Python环境")


if __name__ == "__main__":
    main()
