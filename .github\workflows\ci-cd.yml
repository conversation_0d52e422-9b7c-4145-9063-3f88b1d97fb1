# AI代理服务 GitHub Actions CI/CD 工作流
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

env:
  PYTHON_VERSION: '3.9'
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # ==================== 代码质量检查 ====================
  code-quality:
    name: 代码质量检查
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置Python环境
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'

      - name: 安装依赖
        run: |
          python -m pip install --upgrade pip
          pip install black isort flake8 mypy bandit safety
          pip install -r requirements.txt
          pip install -r requirements-dev.txt

      - name: 代码格式检查
        run: |
          black --check --config pyproject.toml .
          isort --check-only --profile black .

      - name: 代码风格检查
        run: |
          flake8 --config .flake8 api_proxy tests

      - name: 类型检查
        run: |
          mypy api_proxy --ignore-missing-imports

      - name: 安全检查
        run: |
          bandit -r api_proxy -f json -o bandit-report.json
          safety check

      - name: 上传安全报告
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: security-reports
          path: bandit-report.json

  # ==================== 测试 ====================
  test:
    name: 测试
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ['3.8', '3.9', '3.10', '3.11']
    
    services:
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
          cache: 'pip'

      - name: 安装依赖
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install -r requirements-dev.txt
          pip install -e .

      - name: 运行单元测试
        run: |
          pytest tests/test_*_unit.py -v --tb=short \
            --junitxml=unit-test-results.xml \
            --cov=api_proxy --cov-report=xml --cov-report=html

      - name: 运行集成测试
        env:
          REDIS_URL: redis://localhost:6379/0
        run: |
          pytest tests/test_*_integration.py -v --tb=short \
            --junitxml=integration-test-results.xml

      - name: 运行边界测试
        run: |
          pytest tests/test_*_boundary.py -v --tb=short \
            --junitxml=boundary-test-results.xml

      - name: 运行错误处理测试
        run: |
          pytest tests/test_*_error.py -v --tb=short \
            --junitxml=error-test-results.xml

      - name: 上传测试结果
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: test-results-${{ matrix.python-version }}
          path: |
            *-test-results.xml
            htmlcov/
            coverage.xml

      - name: 上传覆盖率到Codecov
        uses: codecov/codecov-action@v3
        if: matrix.python-version == '3.9'
        with:
          file: ./coverage.xml
          flags: unittests
          name: codecov-umbrella

  # ==================== 构建 ====================
  build:
    name: 构建
    runs-on: ubuntu-latest
    needs: [code-quality, test]
    outputs:
      image-digest: ${{ steps.build.outputs.digest }}
      image-tag: ${{ steps.meta.outputs.tags }}
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 登录到容器注册表
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 提取元数据
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=semver,pattern={{major}}
            type=sha,prefix={{branch}}-

      - name: 构建并推送Docker镜像
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            VERSION=${{ github.ref_name }}
            BUILD_DATE=${{ github.event.head_commit.timestamp }}
            VCS_REF=${{ github.sha }}

      - name: 构建Python包
        run: |
          python -m pip install --upgrade pip build twine
          python -m build
          twine check dist/*

      - name: 上传构建产物
        uses: actions/upload-artifact@v3
        with:
          name: python-package
          path: dist/

  # ==================== 安全扫描 ====================
  security-scan:
    name: 安全扫描
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name != 'pull_request'
    
    steps:
      - name: 运行Trivy漏洞扫描
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ needs.build.outputs.image-tag }}
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: 上传Trivy扫描结果到GitHub Security
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  # ==================== 部署到测试环境 ====================
  deploy-staging:
    name: 部署到测试环境
    runs-on: ubuntu-latest
    needs: [build, security-scan]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment:
      name: staging
      url: ${{ steps.deploy.outputs.url }}
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 部署到测试环境
        id: deploy
        run: |
          echo "url=https://staging.ai-proxy.example.com" >> $GITHUB_OUTPUT
          # 这里添加实际的部署脚本
          echo "部署到测试环境完成"

      - name: 健康检查
        run: |
          sleep 30
          curl -f ${{ steps.deploy.outputs.url }}/health || exit 1

  # ==================== 部署到生产环境 ====================
  deploy-production:
    name: 部署到生产环境
    runs-on: ubuntu-latest
    needs: [build, security-scan]
    if: startsWith(github.ref, 'refs/tags/v')
    environment:
      name: production
      url: ${{ steps.deploy.outputs.url }}
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 部署到生产环境
        id: deploy
        run: |
          echo "url=https://ai-proxy.example.com" >> $GITHUB_OUTPUT
          # 这里添加实际的部署脚本
          echo "部署到生产环境完成"

      - name: 健康检查
        run: |
          sleep 30
          curl -f ${{ steps.deploy.outputs.url }}/health || exit 1

      - name: 创建GitHub Release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: Release ${{ github.ref }}
          draft: false
          prerelease: false
