"""作业分析集成测试

测试JobAnalysis与其他组件的集成
"""

import pytest
from api_proxy.job_analysis import JobAnalysis
from api_proxy.proxy_service import ProxyService
from api_proxy.config import Config


class TestJobAnalysisIntegration:
    """作业分析集成测试类"""

    def test_with_proxy_service(self):
        """测试与代理服务集成"""
        config = Config({})
        service = ProxyService(config)

        log = """Running tests...
FAILED test.py - AssertionError
ERROR: ModuleNotFoundError
[WARNING] black would reformat file.py"""

        results = service.analyze_job_failure(log)
        assert len(results) == 3

    def test_error_priority(self):
        """测试错误优先级处理"""
        analyzer = JobAnalysis()
        log = "FAILED test.py\nOutOfMemoryError"
        results = analyzer.parse_log(log)
        assert len(results) == 2
        assert results[0].error_type == JobErrorType.TEST_FAILURE
        assert results[1].error_type == JobErrorType.MEMORY_ERROR
