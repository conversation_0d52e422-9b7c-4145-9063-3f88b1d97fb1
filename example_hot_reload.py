#!/usr/bin/env python3
"""
热重载功能演示

本示例展示如何使用AI代理服务的热重载功能，包括：
1. 配置文件监控
2. 自动重载服务
3. 手动触发重载
4. 重载状态监控
"""

import json
import time
import threading
from pathlib import Path

from api_proxy import ProxyService, Config, ProviderConfig
from api_proxy.hot_reload import HotReloadManager, ServiceReloader


def create_sample_config(filename: str, provider_count: int = 1):
    """创建示例配置文件"""
    config_data = {
        "providers": {
            "openai": [
                {
                    "name": f"openai_instance_{i+1}",
                    "api_key": f"sk-demo-key-{i+1}",
                    "config": {}
                }
                for i in range(provider_count)
            ],
            "openrouter": [
                {
                    "name": f"openrouter_instance_{i+1}",
                    "api_key": f"sk-or-demo-key-{i+1}",
                    "config": {
                        "timeout": 30 + i * 10,
                        "site_url": f"https://demo-app-{i+1}.com",
                        "site_name": f"Demo App {i+1}"
                    }
                }
                for i in range(provider_count)
            ]
        },
        "default_timeout": 30,
        "max_retries": 3,
        "health_check_interval": 300,
        "enable_monitoring": True,
        "log_level": "INFO"
    }
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(config_data, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 创建配置文件: {filename}")
    return config_data


def print_service_status(service: ProxyService):
    """打印服务状态"""
    if not service:
        print("❌ 服务未启动")
        return
    
    status = service.get_service_status()
    print(f"\n📊 服务状态:")
    print(f"  - 提供商类型: {status['config']['provider_types']}")
    print(f"  - 总提供商数: {status['config']['total_providers']}")
    print(f"  - 默认超时: {status['config']['default_timeout']}秒")
    print(f"  - 监控状态: {'启用' if status['config']['monitoring_enabled'] else '禁用'}")
    print(f"  - 日志级别: {status['config']['log_level']}")
    
    for provider_type, provider_info in status['providers'].items():
        print(f"  - {provider_type}: {provider_info['count']} 个实例")


def demo_basic_hot_reload():
    """演示基本热重载功能"""
    print("🔥 基本热重载演示")
    print("=" * 50)
    
    config_file = "demo_config.json"
    
    # 创建初始配置
    create_sample_config(config_file, provider_count=1)
    
    # 创建服务重载器
    service_reloader = ServiceReloader(ProxyService)
    
    # 创建热重载管理器
    hot_reload_manager = HotReloadManager(config_file)
    hot_reload_manager.add_reload_callback(service_reloader.reload_service)
    
    try:
        # 启动监控
        hot_reload_manager.start_monitoring()
        print("🎯 开始监控配置文件变化...")
        
        # 手动触发初始加载
        print("\n📥 手动触发初始加载...")
        success = hot_reload_manager.manual_reload()
        if success:
            print("✅ 初始加载成功")
            service = service_reloader.get_service()
            print_service_status(service)
        else:
            print("❌ 初始加载失败")
            return
        
        # 等待用户输入
        print(f"\n🔧 现在可以修改配置文件 {config_file} 来测试自动重载")
        print("💡 建议修改:")
        print("  1. 增加提供商实例数量")
        print("  2. 修改超时时间")
        print("  3. 更改日志级别")
        print("\n按 Enter 继续演示自动配置更新...")
        input()
        
        # 自动更新配置
        print("\n🔄 自动更新配置...")
        create_sample_config(config_file, provider_count=2)
        
        # 等待重载完成
        time.sleep(3)
        
        # 显示更新后的状态
        updated_service = service_reloader.get_service()
        print("\n📈 更新后的服务状态:")
        print_service_status(updated_service)
        
        # 显示重载历史
        history = service_reloader.get_reload_history()
        print(f"\n📜 重载历史 (共 {len(history)} 次):")
        for i, record in enumerate(history, 1):
            status = "✅ 成功" if record["success"] else "❌ 失败"
            timestamp = record["timestamp"].strftime("%H:%M:%S")
            print(f"  {i}. {timestamp} - {status}")
            if not record["success"]:
                print(f"     错误: {record.get('error', 'Unknown')}")
        
    finally:
        # 清理
        hot_reload_manager.stop_monitoring()
        if Path(config_file).exists():
            Path(config_file).unlink()
        print("\n🧹 清理完成")


def demo_manual_reload():
    """演示手动重载功能"""
    print("\n🖱️  手动重载演示")
    print("=" * 50)
    
    config_file = "manual_reload_config.json"
    
    # 创建配置
    create_sample_config(config_file, provider_count=1)
    
    # 创建服务重载器
    service_reloader = ServiceReloader(ProxyService)
    
    # 创建热重载管理器（不启动自动监控）
    hot_reload_manager = HotReloadManager(config_file)
    hot_reload_manager.add_reload_callback(service_reloader.reload_service)
    
    try:
        print("🎯 手动重载模式（不启动自动监控）")
        
        # 手动重载几次
        for i in range(3):
            print(f"\n🔄 第 {i+1} 次手动重载...")
            
            # 更新配置
            create_sample_config(config_file, provider_count=i+1)
            
            # 手动触发重载
            success = hot_reload_manager.manual_reload()
            if success:
                print("✅ 重载成功")
                service = service_reloader.get_service()
                print_service_status(service)
            else:
                print("❌ 重载失败")
            
            time.sleep(1)
        
        # 显示最终状态
        print("\n📊 最终状态:")
        status = hot_reload_manager.get_status()
        print(f"  - 监控状态: {'启用' if status['monitoring'] else '禁用'}")
        print(f"  - 配置文件: {status['config_path']}")
        print(f"  - 文件存在: {'是' if status['config_exists'] else '否'}")
        print(f"  - 回调数量: {status['callbacks_count']}")
        
    finally:
        # 清理
        if Path(config_file).exists():
            Path(config_file).unlink()
        print("\n🧹 清理完成")


def demo_error_handling():
    """演示错误处理"""
    print("\n⚠️  错误处理演示")
    print("=" * 50)
    
    config_file = "error_config.json"
    
    # 创建服务重载器
    service_reloader = ServiceReloader(ProxyService)
    
    # 创建热重载管理器
    hot_reload_manager = HotReloadManager(config_file)
    hot_reload_manager.add_reload_callback(service_reloader.reload_service)
    
    try:
        # 测试文件不存在的情况
        print("🔍 测试配置文件不存在...")
        success = hot_reload_manager.manual_reload()
        print(f"结果: {'成功' if success else '失败'} (预期: 失败)")
        
        # 创建无效的JSON文件
        print("\n🔍 测试无效JSON配置...")
        with open(config_file, 'w') as f:
            f.write("invalid json content")
        
        success = hot_reload_manager.manual_reload()
        print(f"结果: {'成功' if success else '失败'} (预期: 失败)")
        
        # 创建无效的配置结构
        print("\n🔍 测试无效配置结构...")
        with open(config_file, 'w') as f:
            json.dump({"invalid": "config"}, f)
        
        success = hot_reload_manager.manual_reload()
        print(f"结果: {'成功' if success else '失败'} (预期: 失败)")
        
        # 显示重载历史（应该包含失败记录）
        history = service_reloader.get_reload_history()
        print(f"\n📜 重载历史 (共 {len(history)} 次):")
        for i, record in enumerate(history, 1):
            status = "✅ 成功" if record["success"] else "❌ 失败"
            timestamp = record["timestamp"].strftime("%H:%M:%S")
            print(f"  {i}. {timestamp} - {status}")
            if not record["success"]:
                print(f"     错误: {record.get('error', 'Unknown')}")
        
    finally:
        # 清理
        if Path(config_file).exists():
            Path(config_file).unlink()
        print("\n🧹 清理完成")


def main():
    """主函数"""
    print("🔥 AI代理服务热重载功能演示")
    print("=" * 60)
    
    print("\n本演示将展示以下功能:")
    print("1. 📁 配置文件自动监控")
    print("2. 🔄 服务自动重载")
    print("3. 🖱️  手动触发重载")
    print("4. ⚠️  错误处理机制")
    print("5. 📊 重载状态监控")
    
    print("\n⚠️  注意: 这是演示程序，使用的是模拟API密钥")
    print("🔗 实际使用时请配置真实的API密钥")
    
    try:
        # 基本热重载演示
        demo_basic_hot_reload()
        
        # 手动重载演示
        demo_manual_reload()
        
        # 错误处理演示
        demo_error_handling()
        
        print("\n🎉 演示完成！")
        print("\n💡 使用提示:")
        print("  - 启动Web界面: python -m api_proxy --web")
        print("  - 禁用热重载: python -m api_proxy --web --no-hot-reload")
        print("  - 手动重载API: POST /api/reload")
        print("  - 重载状态API: GET /api/reload/status")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")


if __name__ == "__main__":
    main()
