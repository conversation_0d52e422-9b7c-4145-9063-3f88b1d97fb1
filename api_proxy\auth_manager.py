# API密钥和权限管理模块
import json
import hashlib
import secrets
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum


class UserRole(Enum):
    """用户角色"""
    ADMIN = "admin"
    USER = "user"
    GUEST = "guest"


@dataclass
class APIKey:
    """API密钥信息"""
    key_id: str
    key_hash: str  # 存储哈希值，不存储明文
    name: str
    role: UserRole
    allowed_models: List[str]
    allowed_providers: List[str]
    rate_limit: int  # 每分钟请求数
    created_at: datetime
    expires_at: Optional[datetime]
    is_active: bool
    usage_count: int = 0
    last_used: Optional[datetime] = None


@dataclass
class ModelMapping:
    """模型映射配置"""
    public_name: str  # 对外显示的模型名
    provider: str  # 实际提供商
    real_model: str  # 实际模型名
    description: str
    is_active: bool
    price_multiplier: float = 1.0  # 价格倍数


class AuthManager:
    """认证和权限管理器"""
    
    def __init__(self, auth_file: str = "auth_config.json"):
        self.auth_file = Path(auth_file)
        self.api_keys: Dict[str, APIKey] = {}
        self.model_mappings: Dict[str, ModelMapping] = {}
        self.usage_stats: Dict[str, Dict] = {}
        self._load_config()
    
    def _load_config(self):
        """加载认证配置"""
        if not self.auth_file.exists():
            self._create_default_config()
            return
        
        try:
            with open(self.auth_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 加载API密钥
            for key_data in data.get('api_keys', []):
                key_data['created_at'] = datetime.fromisoformat(key_data['created_at'])
                if key_data.get('expires_at'):
                    key_data['expires_at'] = datetime.fromisoformat(key_data['expires_at'])
                if key_data.get('last_used'):
                    key_data['last_used'] = datetime.fromisoformat(key_data['last_used'])
                key_data['role'] = UserRole(key_data['role'])
                
                api_key = APIKey(**key_data)
                self.api_keys[api_key.key_id] = api_key
            
            # 加载模型映射
            for mapping_data in data.get('model_mappings', []):
                mapping = ModelMapping(**mapping_data)
                self.model_mappings[mapping.public_name] = mapping
            
            # 加载使用统计
            self.usage_stats = data.get('usage_stats', {})
            
        except Exception as e:
            print(f"加载认证配置失败: {e}")
            self._create_default_config()
    
    def _save_config(self):
        """保存认证配置"""
        data = {
            'api_keys': [],
            'model_mappings': [],
            'usage_stats': self.usage_stats
        }
        
        # 序列化API密钥
        for api_key in self.api_keys.values():
            key_data = asdict(api_key)
            key_data['created_at'] = api_key.created_at.isoformat()
            if api_key.expires_at:
                key_data['expires_at'] = api_key.expires_at.isoformat()
            if api_key.last_used:
                key_data['last_used'] = api_key.last_used.isoformat()
            key_data['role'] = api_key.role.value
            data['api_keys'].append(key_data)
        
        # 序列化模型映射
        for mapping in self.model_mappings.values():
            data['model_mappings'].append(asdict(mapping))
        
        with open(self.auth_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    
    def _create_default_config(self):
        """创建默认配置"""
        # 创建默认管理员密钥
        admin_key = self.create_api_key(
            name="默认管理员",
            role=UserRole.ADMIN,
            allowed_models=["*"],
            allowed_providers=["*"],
            rate_limit=1000
        )
        
        # 创建默认模型映射
        default_mappings = [
            ModelMapping("gpt-3.5-turbo", "openai", "gpt-3.5-turbo", "GPT-3.5 Turbo", True),
            ModelMapping("gpt-4", "openai", "gpt-4", "GPT-4", True),
            ModelMapping("claude-3-sonnet", "openrouter", "anthropic/claude-3-sonnet", "Claude 3 Sonnet", True),
            ModelMapping("gemini-pro", "openrouter", "google/gemini-pro", "Gemini Pro", True),
        ]
        
        for mapping in default_mappings:
            self.model_mappings[mapping.public_name] = mapping
        
        self._save_config()
        print(f"✅ 创建默认认证配置")
        print(f"🔑 管理员API密钥: {admin_key}")
        print(f"📁 配置文件: {self.auth_file}")
    
    def create_api_key(self, name: str, role: UserRole, allowed_models: List[str], 
                      allowed_providers: List[str], rate_limit: int = 100,
                      expires_days: Optional[int] = None) -> str:
        """创建API密钥"""
        # 生成密钥
        key = f"sk-proxy-{secrets.token_urlsafe(32)}"
        key_id = hashlib.sha256(key.encode()).hexdigest()[:16]
        key_hash = hashlib.sha256(key.encode()).hexdigest()
        
        # 设置过期时间
        expires_at = None
        if expires_days:
            expires_at = datetime.now() + timedelta(days=expires_days)
        
        # 创建API密钥对象
        api_key = APIKey(
            key_id=key_id,
            key_hash=key_hash,
            name=name,
            role=role,
            allowed_models=allowed_models,
            allowed_providers=allowed_providers,
            rate_limit=rate_limit,
            created_at=datetime.now(),
            expires_at=expires_at,
            is_active=True
        )
        
        self.api_keys[key_id] = api_key
        self._save_config()
        
        return key
    
    def verify_api_key(self, key: str) -> Optional[APIKey]:
        """验证API密钥"""
        if not key or not key.startswith("sk-proxy-"):
            return None
        
        key_hash = hashlib.sha256(key.encode()).hexdigest()
        
        for api_key in self.api_keys.values():
            if api_key.key_hash == key_hash:
                # 检查是否激活
                if not api_key.is_active:
                    return None
                
                # 检查是否过期
                if api_key.expires_at and datetime.now() > api_key.expires_at:
                    return None
                
                # 更新使用统计
                api_key.usage_count += 1
                api_key.last_used = datetime.now()
                self._save_config()
                
                return api_key
        
        return None
    
    def check_rate_limit(self, api_key: APIKey) -> bool:
        """检查速率限制"""
        now = datetime.now()
        minute_key = f"{api_key.key_id}:{now.strftime('%Y-%m-%d-%H-%M')}"
        
        if minute_key not in self.usage_stats:
            self.usage_stats[minute_key] = 0
        
        if self.usage_stats[minute_key] >= api_key.rate_limit:
            return False
        
        self.usage_stats[minute_key] += 1
        return True
    
    def can_access_model(self, api_key: APIKey, model: str) -> bool:
        """检查是否可以访问指定模型"""
        if "*" in api_key.allowed_models:
            return True
        return model in api_key.allowed_models
    
    def can_access_provider(self, api_key: APIKey, provider: str) -> bool:
        """检查是否可以访问指定提供商"""
        if "*" in api_key.allowed_providers:
            return True
        return provider in api_key.allowed_providers
    
    def get_model_mapping(self, public_model: str) -> Optional[ModelMapping]:
        """获取模型映射"""
        return self.model_mappings.get(public_model)
    
    def add_model_mapping(self, public_name: str, provider: str, real_model: str, 
                         description: str, price_multiplier: float = 1.0):
        """添加模型映射"""
        mapping = ModelMapping(
            public_name=public_name,
            provider=provider,
            real_model=real_model,
            description=description,
            is_active=True,
            price_multiplier=price_multiplier
        )
        self.model_mappings[public_name] = mapping
        self._save_config()

    def delete_model_mapping(self, public_name: str) -> bool:
        """删除模型映射"""
        if public_name in self.model_mappings:
            del self.model_mappings[public_name]
            self._save_config()
            return True
        return False

    def update_model_mapping(self, public_name: str, provider: str = None, real_model: str = None,
                           description: str = None, price_multiplier: float = None, is_active: bool = None) -> bool:
        """更新模型映射"""
        if public_name not in self.model_mappings:
            return False

        mapping = self.model_mappings[public_name]
        if provider is not None:
            mapping.provider = provider
        if real_model is not None:
            mapping.real_model = real_model
        if description is not None:
            mapping.description = description
        if price_multiplier is not None:
            mapping.price_multiplier = price_multiplier
        if is_active is not None:
            mapping.is_active = is_active

        self._save_config()
        return True
    
    def list_public_models(self) -> List[Dict[str, Any]]:
        """列出对外提供的模型"""
        models = []
        for mapping in self.model_mappings.values():
            if mapping.is_active:
                models.append({
                    "id": mapping.public_name,
                    "object": "model",
                    "created": int(time.time()),
                    "owned_by": "proxy",
                    "description": mapping.description,
                    "provider": mapping.provider
                })
        return models
    
    def get_api_keys_info(self) -> List[Dict[str, Any]]:
        """获取API密钥信息（不包含密钥本身）"""
        keys_info = []
        for api_key in self.api_keys.values():
            keys_info.append({
                "key_id": api_key.key_id,
                "name": api_key.name,
                "role": api_key.role.value,
                "allowed_models": api_key.allowed_models,
                "allowed_providers": api_key.allowed_providers,
                "rate_limit": api_key.rate_limit,
                "created_at": api_key.created_at.isoformat(),
                "expires_at": api_key.expires_at.isoformat() if api_key.expires_at else None,
                "is_active": api_key.is_active,
                "usage_count": api_key.usage_count,
                "last_used": api_key.last_used.isoformat() if api_key.last_used else None
            })
        return keys_info
    
    def deactivate_api_key(self, key_id: str) -> bool:
        """停用API密钥"""
        if key_id in self.api_keys:
            self.api_keys[key_id].is_active = False
            self._save_config()
            return True
        return False
    
    def activate_api_key(self, key_id: str) -> bool:
        """激活API密钥"""
        if key_id in self.api_keys:
            self.api_keys[key_id].is_active = True
            self._save_config()
            return True
        return False

    def delete_api_key(self, key_id: str) -> bool:
        """删除API密钥"""
        if key_id in self.api_keys:
            del self.api_keys[key_id]
            self._save_config()
            return True
        return False

    def update_api_key(self, key_id: str, name: str = None, allowed_models: List[str] = None,
                      allowed_providers: List[str] = None, rate_limit: int = None) -> bool:
        """更新API密钥信息"""
        if key_id not in self.api_keys:
            return False

        api_key = self.api_keys[key_id]
        if name is not None:
            api_key.name = name
        if allowed_models is not None:
            api_key.allowed_models = allowed_models
        if allowed_providers is not None:
            api_key.allowed_providers = allowed_providers
        if rate_limit is not None:
            api_key.rate_limit = rate_limit

        self._save_config()
        return True
