{"providers": {"openai": [{"name": "primary", "api_key": "sk-your-openai-key-here", "config": {}}, {"name": "backup", "api_key": "sk-your-backup-openai-key-here", "config": {"timeout": 60}}], "openrouter": [{"name": "primary", "api_key": "sk-or-your-openrouter-key-here", "config": {"timeout": 30, "site_url": "https://yourapp.com", "site_name": "Your Application Name"}}, {"name": "backup", "api_key": "sk-or-your-backup-openrouter-key-here", "config": {"timeout": 60, "site_url": "https://yourapp.com", "site_name": "Your Application Name"}}]}, "default_timeout": 30, "max_retries": 3, "health_check_interval": 300, "enable_monitoring": true, "log_level": "INFO"}