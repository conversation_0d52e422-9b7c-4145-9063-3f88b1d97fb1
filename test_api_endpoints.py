#!/usr/bin/env python3
"""
测试API端点
验证统计相关的API端点是否正常工作
"""

import requests
import time
import json
from threading import Thread
from api_proxy.web_app import WebApp


def start_server():
    """启动测试服务器"""
    try:
        app = WebApp()
        print("🚀 启动测试服务器...")
        app.run(host="127.0.0.1", port=8001)
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")


def test_api_endpoints():
    """测试API端点"""
    base_url = "http://127.0.0.1:8001"
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(3)
    
    # 测试健康检查
    print("\n🔍 测试健康检查端点...")
    try:
        response = requests.get(f"{base_url}/api/health", timeout=5)
        print(f"健康检查状态: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"健康检查响应: {data}")
        else:
            print(f"健康检查失败: {response.text}")
    except Exception as e:
        print(f"健康检查请求失败: {e}")
    
    # 测试统计端点
    print("\n📊 测试统计端点...")
    try:
        response = requests.get(f"{base_url}/api/stats", timeout=5)
        print(f"统计端点状态: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"统计端点响应状态: {data.get('status')}")
            if data.get('status') == 'success':
                stats_data = data.get('data', {})
                print(f"统计数据键: {list(stats_data.keys())}")
            else:
                print(f"统计端点错误: {data.get('error', '未知错误')}")
        else:
            print(f"统计端点失败: {response.text}")
    except Exception as e:
        print(f"统计端点请求失败: {e}")
    
    # 测试保存统计端点
    print("\n💾 测试保存统计端点...")
    try:
        response = requests.post(f"{base_url}/api/stats/save", timeout=5)
        print(f"保存统计状态: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"保存统计响应: {data}")
        else:
            print(f"保存统计失败: {response.text}")
    except Exception as e:
        print(f"保存统计请求失败: {e}")
    
    # 测试主页
    print("\n🏠 测试主页...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"主页状态: {response.status_code}")
        if response.status_code == 200:
            print("主页加载成功")
        else:
            print(f"主页加载失败: {response.text[:200]}")
    except Exception as e:
        print(f"主页请求失败: {e}")


def test_direct_import():
    """测试直接导入和使用"""
    print("\n🔧 测试直接导入...")
    
    try:
        from api_proxy.monitoring import RequestMonitor
        print("✅ RequestMonitor导入成功")
        
        # 创建监控器
        monitor = RequestMonitor("test_direct.json", auto_save_interval=0)
        print("✅ RequestMonitor创建成功")
        
        # 记录一个请求
        monitor.record_request("test:provider", 1.0, True, model="test-model")
        print("✅ 记录请求成功")
        
        # 获取统计
        stats = monitor.get_stats()
        print(f"✅ 获取统计成功: {len(stats)} 个条目")
        
        # 保存统计
        monitor.save_now()
        print("✅ 保存统计成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 直接导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🧪 API端点测试")
    print("=" * 50)
    
    # 测试直接导入
    direct_ok = test_direct_import()
    
    if not direct_ok:
        print("\n❌ 基础功能测试失败，跳过服务器测试")
        return
    
    # 启动服务器（在后台线程中）
    server_thread = Thread(target=start_server, daemon=True)
    server_thread.start()
    
    # 测试API端点
    test_api_endpoints()
    
    print("\n🎉 测试完成!")


if __name__ == "__main__":
    main()
