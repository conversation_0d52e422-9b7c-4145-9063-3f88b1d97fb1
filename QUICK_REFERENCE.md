# 快速参考指南

## 🎯 改造核心要点

### 核心原则
**保持 BaseProvider 设计不变，通过端点识别和功能声明支持多种 API 类型**

### 三个关键改动
1. **BaseProvider 扩展** - 添加 `supported_features` 属性
2. **提供商实现** - 在 `call()` 方法中路由不同功能
3. **新增提供商** - 为文生图和文生视频添加专用提供商

---

## 📋 改造清单

### 第一步：扩展 BaseProvider (api_proxy/providers/base.py)
```python
@property
def supported_features(self) -> List[str]:
    """返回支持的功能列表"""
    return ['chat']  # 默认值

def supports_feature(self, feature: str) -> bool:
    """检查是否支持某个功能"""
    return feature in self.supported_features
```

### 第二步：更新 OpenAI 提供商 (api_proxy/providers/openai.py)
```python
@property
def supported_features(self) -> List[str]:
    return ['chat', 'image']

def call(self, endpoint: str, **kwargs):
    if 'image' in endpoint:
        return self._call_image_api(endpoint, **kwargs)
    else:
        return self._call_chat_api(endpoint, **kwargs)

def _call_image_api(self, endpoint: str, **kwargs):
    # 调用 DALL-E API
    pass
```

### 第三步：新增提供商文件
- `api_proxy/providers/image_providers.py` - Stability AI, Midjourney
- `api_proxy/providers/video_providers.py` - Runway ML, HeyGen

### 第四步：更新 ProxyService (api_proxy/proxy_service.py)
```python
def _create_provider(self, provider_type: str, config):
    # 添加新提供商到 providers_map
    providers_map = {
        "openai": ...,
        "stability_ai": ...,
        "runway_ml": ...,
    }

def get_available_features(self, provider_type: str) -> List[str]:
    # 新增方法
    pass
```

### 第五步：添加 Web API 端点 (api_proxy/web_app.py)
```python
@app.post("/v1/images/generations")
async def generate_image(request: ImageGenerationRequest):
    pass

@app.post("/v1/videos/generations")
async def generate_video(request: VideoGenerationRequest):
    pass
```

### 第六步：更新配置示例 (config.example.json)
```json
{
  "providers": {
    "openai": [...],
    "stability_ai": [...],
    "runway_ml": [...]
  }
}
```

---

## 🔧 实现细节速查

### 端点识别逻辑
```python
def identify_feature(endpoint: str) -> str:
    if 'image' in endpoint:
        return 'image'
    elif 'video' in endpoint:
        return 'video'
    else:
        return 'chat'
```

### 参数映射示例

#### 文生图参数
```python
{
    "prompt": str,           # 必需
    "size": str,             # 可选: "1024x1024"
    "quality": str,          # 可选: "standard" / "hd"
    "n": int,                # 可选: 1-10
    "style": str,            # 可选: "natural" / "vivid"
}
```

#### 文生视频参数
```python
{
    "prompt": str,           # 必需
    "duration": int,         # 可选: 秒数
    "fps": int,              # 可选: 帧率
    "resolution": str,       # 可选: "720p" / "1080p"
    "model": str,            # 可选: 模型名称
}
```

### 提供商配置示例

#### Stability AI
```json
{
    "name": "stability_primary",
    "api_key": "sk-stability-xxx",
    "config": {
        "engine_id": "stable-diffusion-xl-1024-v1-0",
        "timeout": 60
    }
}
```

#### Runway ML
```json
{
    "name": "runway_primary",
    "api_key": "runway-api-key",
    "config": {
        "model": "gen3",
        "timeout": 120
    }
}
```

---

## 📊 功能支持矩阵

```
提供商          | 文本 | 图像 | 视频 | 说明
----------------|------|------|------|------------------
OpenAI          |  ✅  |  ✅  |  ❌  | GPT + DALL-E
OpenRouter      |  ✅  |  ✅  |  ❌  | 多模型聚合
Stability AI    |  ❌  |  ✅  |  ❌  | 专业文生图
Midjourney      |  ❌  |  ✅  |  ❌  | 高质量图像
Runway ML       |  ❌  |  ❌  |  ✅  | 文生视频
HeyGen          |  ❌  |  ❌  |  ✅  | 数字人视频
```

---

## 🚀 使用示例

### Python 客户端

#### 文生图
```python
from api_proxy import ProxyService, Config, ProviderConfig

config = Config({
    "openai": [ProviderConfig("primary", "sk-xxx", {})],
    "stability_ai": [ProviderConfig("primary", "sk-stability-xxx", {})]
})

service = ProxyService(config)

# 使用 OpenAI DALL-E
response = service.call("openai", "images/generations",
                       prompt="A beautiful sunset",
                       size="1024x1024")

# 使用 Stability AI
response = service.call("stability_ai", "images/generations",
                       prompt="A beautiful sunset",
                       cfg_scale=7)
```

#### 文生视频
```python
# 使用 Runway ML
response = service.call("runway_ml", "videos/generations",
                       prompt="A person walking in the park",
                       duration=10,
                       fps=24)
```

### cURL 示例

#### 文生图
```bash
curl -X POST http://localhost:8000/v1/images/generations \
  -H "Authorization: Bearer sk-proxy-xxx" \
  -H "X-Provider: openai" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "A beautiful sunset",
    "size": "1024x1024",
    "n": 1
  }'
```

#### 文生视频
```bash
curl -X POST http://localhost:8000/v1/videos/generations \
  -H "Authorization: Bearer sk-proxy-xxx" \
  -H "X-Provider: runway_ml" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "A person walking in the park",
    "duration": 10,
    "fps": 24
  }'
```

---

## 🔍 调试技巧

### 查询提供商功能
```bash
curl http://localhost:8000/v1/providers/openai/features
# 返回: {"provider": "openai", "features": ["chat", "image"]}
```

### 查看所有提供商
```bash
curl http://localhost:8000/v1/providers
# 返回: {"providers": {"openai": [...], "stability_ai": [...]}}
```

### 启用详细日志
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

---

## ⚠️ 常见问题

### Q: 现有代码需要修改吗？
**A**: 不需要。改造完全向后兼容。

### Q: 如何添加新的提供商？
**A**: 
1. 创建继承 BaseProvider 的类
2. 实现 `call()` 方法
3. 设置 `supported_features` 属性
4. 在 ProxyService 中注册

### Q: 如何处理不同提供商的参数差异？
**A**: 在各提供商的 `call()` 方法中进行参数映射和转换。

### Q: 负载均衡对新功能有效吗？
**A**: 是的。现有的轮询机制对所有功能类型都有效。

### Q: 监控统计会记录新功能吗？
**A**: 是的。现有的监控系统自动支持新功能。

---

## 📚 相关文档

- `ARCHITECTURE_UPGRADE_PLAN.md` - 详细的升级规划
- `IMPLEMENTATION_GUIDE.md` - 详细的实现指南
- `ARCHITECTURE_COMPARISON.md` - 改造前后对比
- `API_USAGE.md` - API 使用指南（需要更新）

---

## 🎓 学习路径

1. **理解现有架构** - 阅读 README.md 和 ARCHITECTURE_UPGRADE_PLAN.md
2. **学习改造方案** - 阅读 ARCHITECTURE_COMPARISON.md
3. **实现改造** - 按照 IMPLEMENTATION_GUIDE.md 逐步实现
4. **测试验证** - 编写单元测试和集成测试
5. **文档更新** - 更新 API_USAGE.md 和配置示例

---

## 📞 支持

如有问题，请参考：
- 项目 README.md
- 详细规划文档
- 实现指南
- 代码注释

