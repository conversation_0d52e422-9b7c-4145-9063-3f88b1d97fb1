# API提供商基础接口
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional
from datetime import datetime


class BaseProvider(ABC):
    """API提供商基础接口

    子类必须实现:
    - call(): 执行API调用
    - name: 提供商名称属性
    - last_used: 最后使用时间戳
    """

    @property
    @abstractmethod
    def name(self) -> str:
        """返回提供商名称"""
        pass

    @property
    @abstractmethod
    def last_used(self) -> Optional[datetime]:
        """返回最后使用时间戳"""
        pass

    @abstractmethod
    def call(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """统一调用接口方法

        Args:
            endpoint (str): API端点路径 (如 'chat/completions')
            **kwargs: 请求参数:
                - model (str): 模型ID (必需)
                - messages (List[Dict]): 对话消息列表
                - temperature (float): 温度参数(0-2)
                - max_tokens (int): 最大token数
                - stream (bool): 是否流式输出

        Returns:
            Dict[str, Any]: 标准化的响应数据结构:
                - id (str): 请求ID
                - model (str): 使用的模型
                - choices (List[Dict]): 响应选项
                - usage (Dict): token统计
                - created (int): 时间戳

        Raises:
            ProviderError: API调用失败(4xx/5xx)
            TimeoutError: 请求超时
            ValueError: 参数无效
            RateLimitError: 速率限制(429)
            AuthenticationError: 认证失败(401)

        Example:
            >>> response = provider.call(
                    "chat/completions",
                    model="gpt-4",
                    messages=[{"role":"user","content":"hello"}]
                )
        """
        pass
