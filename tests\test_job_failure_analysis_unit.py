"""作业失败分析单元测试

测试JobAnalyzer类的核心功能实现
"""

import pytest
from datetime import datetime
from api_proxy.job_failure_analysis import JobAnalyzer, JobFailureAnalysis, JobErrorType


class TestJobAnalyzerUnit:
    """作业分析器单元测试类"""

    @pytest.fixture
    def analyzer(self):
        """返回初始化好的JobAnalyzer实例"""
        return JobAnalyzer()

    def test_parse_test_failure(self, analyzer):
        """测试识别测试失败错误"""
        log = "FAILED test_service.py::test_api - AssertionError: expected 200, got 404"
        results = analyzer.analyze_log(log)

        assert len(results) == 1
        assert results[0].error_type == JobErrorType.TEST_FAILURE
        assert "test_service.py" in results[0].error_message
        assert "检查失败的测试用例" in results[0].solution
        assert isinstance(results[0].timestamp, datetime)

    def test_parse_dependency_error(self, analyzer):
        """测试识别依赖错误"""
        log = "ModuleNotFoundError: No module named 'nonexistent'"
        results = analyzer.analyze_log(log)

        assert len(results) == 1
        assert results[0].error_type == JobErrorType.DEPENDENCY_ERROR
        assert "pip install nonexistent" in results[0].solution

    def test_solution_customization(self, analyzer):
        """测试解决方案根据错误详情定制"""
        log1 = "ModuleNotFoundError: No module named 'pkg1'"
        log2 = "ModuleNotFoundError: No module named 'pkg2'"

        result1 = analyzer.analyze_log(log1)[0]
        result2 = analyzer.analyze_log(log2)[0]

        assert "pkg1" in result1.solution
        assert "pkg2" in result2.solution
        assert result1.solution != result2.solution
