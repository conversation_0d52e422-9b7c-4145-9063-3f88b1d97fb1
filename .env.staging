# AI代理服务测试环境配置
# 复制此文件并填入真实值

# ==================== 应用配置 ====================
APP_ENV=staging
APP_PORT=8080
LOG_LEVEL=DEBUG
DEBUG=true

# ==================== API配置 ====================
# OpenAI API密钥 (测试环境可使用测试密钥)
OPENAI_API_KEY_PRIMARY=sk-test-your-staging-openai-api-key-here
OPENAI_API_KEY_BACKUP=sk-test-your-backup-openai-api-key-here

# API配置
DEFAULT_TIMEOUT=30
MAX_RETRIES=3
HEALTH_CHECK_INTERVAL=300

# ==================== 存储配置 ====================
# 数据文件存储路径
DATA_DIR=./data
CONFIG_FILE=./config.json
STATS_FILE=./stats.json
AUTH_FILE=./auth_config.json

# Redis缓存配置 (可选)
REDIS_PASSWORD=staging_redis_password_456
REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0

# ==================== Docker配置 ====================
# 镜像注册表
REGISTRY_URL=ghcr.io/your-org
VERSION=latest

# 容器配置
COMPOSE_PROJECT_NAME=ai-proxy-staging

# ==================== 网络配置 ====================
# Nginx端口
NGINX_HTTP_PORT=80
NGINX_HTTPS_PORT=443

# 监控端口
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000

# ==================== 监控配置 ====================
ENABLE_MONITORING=true
GRAFANA_PASSWORD=staging_grafana_admin_123

# ==================== 安全配置 ====================
ENABLE_API_KEY_ROTATION=false
API_KEY_ROTATION_INTERVAL=86400

# JWT密钥 (生产环境请使用强密钥)
JWT_SECRET_KEY=staging-jwt-secret-key-change-in-production

# ==================== 日志配置 ====================
LOG_FILE_PATH=logs/app.log
LOG_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5

# ==================== 通知配置 ====================
# 企业微信/钉钉/Slack Webhook URL
WEBHOOK_URL=https://hooks.slack.com/services/YOUR/STAGING/WEBHOOK

# 邮件配置 (可选)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=smtp_password
SMTP_FROM=AI代理服务 <<EMAIL>>

# ==================== 部署配置 ====================
# 部署主机
STAGING_HOST=staging.ai-proxy.example.com

# SSH配置
SSH_USER=deploy
SSH_PORT=22

# 备份配置
BACKUP_RETENTION_DAYS=7
BACKUP_S3_BUCKET=ai-proxy-staging-backups

# ==================== 开发配置 ====================
# 热重载
ENABLE_HOT_RELOAD=true

# 调试配置
ENABLE_DEBUG=true
ENABLE_PROFILING=true

# 测试配置
TEST_DATA_DIR=./test_data
TEST_REDIS_URL=redis://redis-test:6379/0

# ==================== 第三方服务配置 ====================
# Sentry错误追踪 (可选)
SENTRY_DSN=https://<EMAIL>/project-id

# 分析服务 (可选)
ANALYTICS_API_KEY=your-analytics-api-key

# ==================== 功能开关 ====================
# 功能标志
FEATURE_NEW_UI=true
FEATURE_ADVANCED_ANALYTICS=true
FEATURE_RATE_LIMITING=false

# 实验性功能
EXPERIMENTAL_FEATURES=true
