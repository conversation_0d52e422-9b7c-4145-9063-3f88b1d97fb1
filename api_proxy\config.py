# 配置文件
import json
from dataclasses import dataclass, field
from typing import Dict, List, Optional
from .models import ProviderConfig


@dataclass
class Config:
    """全局配置类

    Attributes:
        providers: 按类型分组的提供商配置字典
        default_timeout: 默认请求超时时间(秒)
        max_retries: 默认最大重试次数
        health_check_interval: 健康检查间隔(秒)
        enable_monitoring: 是否启用监控
        log_level: 日志级别
    """

    providers: Dict[str, List[ProviderConfig]]
    default_timeout: int = 30
    max_retries: int = 3
    health_check_interval: int = 300
    enable_monitoring: bool = True
    log_level: str = "INFO"

    def __post_init__(self):
        """后初始化验证"""
        if not isinstance(self.providers, dict):
            raise ValueError("providers必须是字典类型")

        # 检查是否至少有一个提供商有配置
        has_any_provider = False
        for configs in self.providers.values():
            if configs:  # 如果这个提供商有配置
                has_any_provider = True
                break

        if not has_any_provider:
            raise ValueError("至少需要配置一个提供商实例")

        # 验证每个提供商配置
        for provider_type, configs in self.providers.items():
            if not isinstance(provider_type, str) or not provider_type.strip():
                raise ValueError("提供商类型必须是非空字符串")

            if not isinstance(configs, list):
                raise ValueError(f"提供商 '{provider_type}' 的配置必须是列表类型")

            # 允许空的配置列表 - 用户可能不想配置某个提供商
            for config in configs:
                if not isinstance(config, ProviderConfig):
                    raise ValueError(f"提供商 '{provider_type}' 的配置必须是ProviderConfig实例")

        # 验证其他参数
        if not isinstance(self.default_timeout, int) or self.default_timeout <= 0:
            raise ValueError("default_timeout必须是正整数")

        if not isinstance(self.max_retries, int) or self.max_retries < 0:
            raise ValueError("max_retries必须是非负整数")

        if not isinstance(self.health_check_interval, int) or self.health_check_interval <= 0:
            raise ValueError("health_check_interval必须是正整数")

        if self.log_level not in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
            raise ValueError("log_level必须是有效的日志级别")

    def get_provider_count(self, provider_type: Optional[str] = None) -> int:
        """获取提供商数量

        Args:
            provider_type: 提供商类型，None表示所有类型

        Returns:
            int: 提供商数量
        """
        if provider_type:
            return len(self.providers.get(provider_type, []))
        return sum(len(configs) for configs in self.providers.values())

    def get_provider_types(self) -> List[str]:
        """获取所有提供商类型列表"""
        return list(self.providers.keys())

    def has_provider_type(self, provider_type: str) -> bool:
        """检查是否存在指定类型的提供商"""
        return provider_type in self.providers and len(self.providers[provider_type]) > 0

    @classmethod
    def from_file(cls, config_file: str) -> 'Config':
        """从JSON配置文件加载配置

        Args:
            config_file: 配置文件路径

        Returns:
            Config: 配置实例
        """
        with open(config_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 转换providers数据
        providers = {}
        for provider_type, configs in data.get('providers', {}).items():
            provider_configs = []
            for config_data in configs:
                provider_config = ProviderConfig(
                    name=config_data['name'],
                    api_key=config_data['api_key'],
                    config=config_data.get('config', {})
                )
                provider_configs.append(provider_config)
            providers[provider_type] = provider_configs

        # 创建Config实例
        return cls(
            providers=providers,
            default_timeout=data.get('default_timeout', 30),
            max_retries=data.get('max_retries', 3),
            health_check_interval=data.get('health_check_interval', 300),
            enable_monitoring=data.get('enable_monitoring', True),
            log_level=data.get('log_level', 'INFO')
        )
