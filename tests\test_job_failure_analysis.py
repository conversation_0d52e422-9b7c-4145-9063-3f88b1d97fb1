"""作业失败分析测试

测试JobFailureAnalyzer类的核心功能
"""

import pytest
from datetime import datetime
from api_proxy.job_failure_analysis import JobAnalyzer, JobFailureAnalysis, JobErrorType


class TestJobAnalyzer:
    """作业分析器测试类"""

    @pytest.fixture
    def analyzer(self):
        return JobAnalyzer()

    def test_parse_test_failure(self, analyzer):
        """测试识别测试失败错误"""
        log = "FAILED test_service.py::test_api - AssertionError: expected 200, got 404"
        results = analyzer.analyze_log(log)

        assert len(results) == 1
        assert results[0].error_type == JobErrorType.TEST_FAILURE
        assert "test_service.py" in results[0].error_message
        assert "检查失败的测试用例" in results[0].solution

    def test_parse_dependency_error(self, analyzer):
        """测试识别依赖错误"""
        log = "ModuleNotFoundError: No module named 'nonexistent'"
        results = analyzer.analyze_log(log)

        assert len(results) == 1
        assert results[0].error_type == JobErrorType.DEPENDENCY_ERROR
        assert "pip install nonexistent" in results[0].solution

    def test_parse_lint_error(self, analyzer):
        """测试识别代码规范错误"""
        log = (
            "would reformat src/main.py\nsrc/utils.py:15:1: E302 expected 2 blank lines"
        )
        results = analyzer.analyze_log(log)

        assert len(results) == 2
        assert results[0].error_type == JobErrorType.LINT_ERROR
        assert "black src/main.py" in results[0].solution

    def test_empty_log(self, analyzer):
        """测试空日志处理"""
        assert len(analyzer.analyze_log("")) == 0

    def test_no_errors(self, analyzer):
        """测试无错误日志"""
        log = "[INFO] All tests passed"
        assert len(analyzer.analyze_log(log)) == 0
