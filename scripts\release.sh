#!/bin/bash
# AI代理服务版本发布脚本
# 自动化版本标记、变更日志生成和发布流程

set -euo pipefail

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CHANGELOG_FILE="$PROJECT_ROOT/CHANGELOG.md"
VERSION_FILE="$PROJECT_ROOT/api_proxy/__init__.py"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
AI代理服务版本发布脚本

用法: $0 [选项] <版本类型>

版本类型:
    major       主版本号 (1.0.0 -> 2.0.0)
    minor       次版本号 (1.0.0 -> 1.1.0)
    patch       修订版本号 (1.0.0 -> 1.0.1)
    <version>   指定具体版本号 (如: 1.2.3)

选项:
    -m, --message MESSAGE   发布说明
    -d, --dry-run          模拟运行，不执行实际操作
    -f, --force            强制发布，跳过检查
    -h, --help             显示此帮助信息
    --no-push              不推送到远程仓库
    --no-changelog         不更新变更日志

示例:
    $0 patch -m "修复API响应问题"
    $0 minor -m "添加新的负载均衡功能"
    $0 1.2.3 -m "重要安全更新"
EOF
}

# 解析命令行参数
parse_args() {
    VERSION_TYPE=""
    RELEASE_MESSAGE=""
    DRY_RUN=false
    FORCE=false
    NO_PUSH=false
    NO_CHANGELOG=false

    while [[ $# -gt 0 ]]; do
        case $1 in
            -m|--message)
                RELEASE_MESSAGE="$2"
                shift 2
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -f|--force)
                FORCE=true
                shift
                ;;
            --no-push)
                NO_PUSH=true
                shift
                ;;
            --no-changelog)
                NO_CHANGELOG=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            major|minor|patch)
                VERSION_TYPE="$1"
                shift
                ;;
            [0-9]*.[0-9]*.[0-9]*)
                VERSION_TYPE="$1"
                shift
                ;;
            *)
                error "未知参数: $1"
                ;;
        esac
    done

    if [[ -z "$VERSION_TYPE" ]]; then
        error "请指定版本类型或版本号"
    fi

    if [[ -z "$RELEASE_MESSAGE" ]]; then
        error "请提供发布说明 (-m 参数)"
    fi
}

# 检查先决条件
check_prerequisites() {
    log "检查发布先决条件..."

    # 检查Git仓库状态
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        error "当前目录不是Git仓库"
    fi

    # 检查是否在主分支
    local current_branch=$(git branch --show-current)
    if [[ "$current_branch" != "main" ]] && [[ "$current_branch" != "master" ]] && [[ "$FORCE" != true ]]; then
        error "请在主分支上执行发布操作，当前分支: $current_branch"
    fi

    # 检查工作目录是否干净
    if [[ -n $(git status --porcelain) ]] && [[ "$FORCE" != true ]]; then
        error "工作目录不干净，请先提交或暂存更改"
    fi

    # 检查是否与远程同步
    if [[ "$NO_PUSH" != true ]]; then
        git fetch origin
        local local_commit=$(git rev-parse HEAD)
        local remote_commit=$(git rev-parse origin/$(git branch --show-current))
        if [[ "$local_commit" != "$remote_commit" ]] && [[ "$FORCE" != true ]]; then
            error "本地分支与远程分支不同步，请先拉取最新更改"
        fi
    fi

    # 检查必需的工具
    local required_tools=("git" "python" "pip")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            error "缺少必需的工具: $tool"
        fi
    done

    success "先决条件检查通过"
}

# 获取当前版本
get_current_version() {
    if [[ -f "$VERSION_FILE" ]]; then
        grep -E "^__version__" "$VERSION_FILE" | sed -E 's/__version__ = "(.*)"/\1/'
    else
        echo "0.0.0"
    fi
}

# 计算新版本
calculate_new_version() {
    local current_version="$1"
    local version_type="$2"

    if [[ "$version_type" =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        echo "$version_type"
        return
    fi

    local major minor patch
    IFS='.' read -r major minor patch <<< "$current_version"

    case "$version_type" in
        major)
            echo "$((major + 1)).0.0"
            ;;
        minor)
            echo "$major.$((minor + 1)).0"
            ;;
        patch)
            echo "$major.$minor.$((patch + 1))"
            ;;
        *)
            error "无效的版本类型: $version_type"
            ;;
    esac
}

# 更新版本文件
update_version_file() {
    local new_version="$1"
    
    log "更新版本文件到 $new_version..."
    
    if [[ "$DRY_RUN" == true ]]; then
        log "[DRY RUN] 将更新 $VERSION_FILE 中的版本号"
        return
    fi

    # 创建版本文件如果不存在
    if [[ ! -f "$VERSION_FILE" ]]; then
        mkdir -p "$(dirname "$VERSION_FILE")"
        cat > "$VERSION_FILE" << EOF
"""AI代理服务版本信息"""

__version__ = "$new_version"
__author__ = "AI Proxy Team"
__email__ = "<EMAIL>"
__description__ = "智能API代理和负载均衡服务"
EOF
    else
        # 更新现有文件
        sed -i.bak "s/__version__ = \".*\"/__version__ = \"$new_version\"/" "$VERSION_FILE"
        rm -f "$VERSION_FILE.bak"
    fi

    success "版本文件更新完成"
}

# 生成变更日志
generate_changelog() {
    local new_version="$1"
    local release_message="$2"
    
    if [[ "$NO_CHANGELOG" == true ]]; then
        log "跳过变更日志更新"
        return
    fi

    log "生成变更日志..."
    
    if [[ "$DRY_RUN" == true ]]; then
        log "[DRY RUN] 将更新变更日志"
        return
    fi

    local release_date=$(date +%Y-%m-%d)
    local temp_file=$(mktemp)

    # 创建新的变更日志条目
    cat > "$temp_file" << EOF
# 变更日志

## [${new_version}] - ${release_date}

### 更新内容
- ${release_message}

### 技术改进
$(git log --oneline --since="$(git describe --tags --abbrev=0 2>/dev/null || echo '1 month ago')" --pretty=format:"- %s" | head -10)

EOF

    # 如果变更日志文件存在，合并内容
    if [[ -f "$CHANGELOG_FILE" ]]; then
        # 跳过第一行标题，添加现有内容
        tail -n +2 "$CHANGELOG_FILE" >> "$temp_file"
    fi

    mv "$temp_file" "$CHANGELOG_FILE"
    success "变更日志更新完成"
}

# 运行测试
run_tests() {
    log "运行测试套件..."
    
    if [[ "$DRY_RUN" == true ]]; then
        log "[DRY RUN] 将运行测试"
        return
    fi

    cd "$PROJECT_ROOT"
    
    # 安装依赖
    pip install -r requirements-dev.txt > /dev/null 2>&1
    
    # 运行测试
    if ! python -m pytest tests/ -v --tb=short; then
        error "测试失败，发布中止"
    fi
    
    # 运行代码质量检查
    if ! python -m flake8 api_proxy tests; then
        error "代码质量检查失败，发布中止"
    fi
    
    success "所有测试通过"
}

# 创建Git标签
create_git_tag() {
    local new_version="$1"
    local release_message="$2"
    
    log "创建Git标签 v$new_version..."
    
    if [[ "$DRY_RUN" == true ]]; then
        log "[DRY RUN] 将创建标签: v$new_version"
        return
    fi

    # 提交版本更改
    git add "$VERSION_FILE"
    if [[ "$NO_CHANGELOG" != true ]]; then
        git add "$CHANGELOG_FILE"
    fi
    git commit -m "chore: bump version to $new_version

$release_message"

    # 创建标签
    git tag -a "v$new_version" -m "Release v$new_version

$release_message"

    success "Git标签创建完成"
}

# 推送到远程仓库
push_to_remote() {
    local new_version="$1"
    
    if [[ "$NO_PUSH" == true ]]; then
        log "跳过推送到远程仓库"
        return
    fi

    log "推送到远程仓库..."
    
    if [[ "$DRY_RUN" == true ]]; then
        log "[DRY RUN] 将推送提交和标签到远程仓库"
        return
    fi

    # 推送提交
    git push origin $(git branch --show-current)
    
    # 推送标签
    git push origin "v$new_version"
    
    success "推送完成"
}

# 构建和发布包
build_and_publish() {
    local new_version="$1"
    
    log "构建Python包..."
    
    if [[ "$DRY_RUN" == true ]]; then
        log "[DRY RUN] 将构建和发布包"
        return
    fi

    cd "$PROJECT_ROOT"
    
    # 清理旧的构建文件
    rm -rf build/ dist/ *.egg-info/
    
    # 构建包
    python -m build
    
    # 检查包
    python -m twine check dist/*
    
    # 发布到PyPI (需要配置认证)
    if [[ -n "${PYPI_TOKEN:-}" ]]; then
        python -m twine upload dist/* --username __token__ --password "$PYPI_TOKEN"
        success "包已发布到PyPI"
    else
        warning "未配置PyPI令牌，跳过发布"
    fi
}

# 发送通知
send_notification() {
    local new_version="$1"
    local release_message="$2"
    
    if [[ -n "${WEBHOOK_URL:-}" ]]; then
        local payload=$(cat << EOF
{
    "text": "🚀 AI代理服务新版本发布",
    "attachments": [
        {
            "color": "good",
            "fields": [
                {
                    "title": "版本",
                    "value": "v$new_version",
                    "short": true
                },
                {
                    "title": "发布说明",
                    "value": "$release_message",
                    "short": false
                }
            ]
        }
    ]
}
EOF
        )
        
        curl -X POST "$WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "$payload" || true
    fi
}

# 主函数
main() {
    log "开始AI代理服务版本发布流程"
    
    parse_args "$@"
    
    local current_version=$(get_current_version)
    local new_version=$(calculate_new_version "$current_version" "$VERSION_TYPE")
    
    log "发布信息:"
    log "  当前版本: $current_version"
    log "  新版本: $new_version"
    log "  发布说明: $RELEASE_MESSAGE"
    log "  模拟运行: $DRY_RUN"
    
    # 确认发布
    if [[ "$FORCE" != true ]] && [[ "$DRY_RUN" != true ]]; then
        read -p "确认发布版本 v$new_version? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log "发布已取消"
            exit 0
        fi
    fi
    
    check_prerequisites
    run_tests
    update_version_file "$new_version"
    generate_changelog "$new_version" "$RELEASE_MESSAGE"
    create_git_tag "$new_version" "$RELEASE_MESSAGE"
    push_to_remote "$new_version"
    build_and_publish "$new_version"
    send_notification "$new_version" "$RELEASE_MESSAGE"
    
    success "版本 v$new_version 发布完成!"
    log "下一步:"
    log "  1. 检查CI/CD流水线状态"
    log "  2. 验证部署是否成功"
    log "  3. 更新文档和发布说明"
}

# 执行主函数
main "$@"
