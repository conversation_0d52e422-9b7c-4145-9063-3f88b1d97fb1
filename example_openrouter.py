#!/usr/bin/env python3
"""
OpenRouter使用示例

本示例展示如何配置和使用OpenRouter提供商来访问多种AI模型。
"""

from api_proxy import ProxyService, Config, ProviderConfig

def main():
    """主函数"""
    
    # 配置OpenRouter提供商
    config = Config({
        "openrouter": [
            ProviderConfig("primary", "sk-or-your-api-key-here", {
                "timeout": 30,
                "site_url": "https://yourapp.com",
                "site_name": "Your AI Application"
            }),
            ProviderConfig("backup", "sk-or-your-backup-key-here", {
                "timeout": 60,
                "site_url": "https://yourapp.com", 
                "site_name": "Your AI Application"
            })
        ]
    })
    
    # 创建代理服务
    service = ProxyService(config)
    
    print("🚀 OpenRouter AI代理服务已启动")
    print("=" * 50)
    
    # 示例消息
    messages = [
        {"role": "user", "content": "请简单介绍一下人工智能的发展历史"}
    ]
    
    # 测试不同的模型
    models_to_test = [
        ("openai/gpt-4o", "OpenAI GPT-4o"),
        ("anthropic/claude-3-sonnet", "Anthropic Claude 3 Sonnet"),
        ("google/gemini-pro", "Google Gemini Pro"),
        ("meta-llama/llama-2-70b-chat", "Meta Llama 2 70B Chat")
    ]
    
    for model_id, model_name in models_to_test:
        print(f"\n🤖 测试模型: {model_name}")
        print("-" * 30)
        
        try:
            # 调用OpenRouter API
            response = service.call(
                "openrouter", 
                "chat/completions",
                model=model_id,
                messages=messages,
                max_tokens=150,
                temperature=0.7
            )
            
            # 提取响应内容
            if response and "choices" in response and len(response["choices"]) > 0:
                content = response["choices"][0]["message"]["content"]
                print(f"✅ 响应: {content[:100]}...")
                
                # 显示使用统计
                if "usage" in response:
                    usage = response["usage"]
                    print(f"📊 Token使用: {usage.get('total_tokens', 'N/A')}")
            else:
                print("❌ 未收到有效响应")
                
        except Exception as e:
            print(f"❌ 调用失败: {str(e)}")
    
    print("\n" + "=" * 50)
    print("✨ 测试完成！")

def demo_streaming():
    """演示流式响应（如果支持）"""
    config = Config({
        "openrouter": [
            ProviderConfig("streaming", "sk-or-your-api-key-here", {
                "timeout": 60,
                "site_url": "https://yourapp.com",
                "site_name": "Streaming Demo"
            })
        ]
    })
    
    service = ProxyService(config)
    
    print("\n🌊 流式响应演示")
    print("-" * 30)
    
    try:
        response = service.call(
            "openrouter",
            "chat/completions", 
            model="openai/gpt-3.5-turbo",
            messages=[{"role": "user", "content": "写一首关于AI的短诗"}],
            stream=False,  # OpenRouter支持流式，但这里为了简单起见使用非流式
            max_tokens=100
        )
        
        if response and "choices" in response:
            content = response["choices"][0]["message"]["content"]
            print(f"🎭 AI诗歌:\n{content}")
        
    except Exception as e:
        print(f"❌ 流式调用失败: {str(e)}")

def show_config_example():
    """显示配置示例"""
    print("\n📋 配置示例")
    print("-" * 30)
    
    config_example = """
    {
      "providers": {
        "openrouter": [
          {
            "name": "primary",
            "api_key": "sk-or-your-api-key-here",
            "config": {
              "timeout": 30,
              "site_url": "https://yourapp.com",
              "site_name": "Your App Name"
            }
          }
        ]
      },
      "default_timeout": 30,
      "max_retries": 3,
      "health_check_interval": 300,
      "enable_monitoring": true,
      "log_level": "INFO"
    }
    """
    
    print("💡 将以下配置保存为 config.json:")
    print(config_example)

if __name__ == "__main__":
    print("🎯 OpenRouter AI代理服务示例")
    print("=" * 50)
    
    # 显示配置示例
    show_config_example()
    
    # 注意：实际运行需要有效的API密钥
    print("\n⚠️  注意: 请将 'sk-or-your-api-key-here' 替换为您的真实OpenRouter API密钥")
    print("🔗 获取API密钥: https://openrouter.ai/keys")
    
    # 如果有有效的API密钥，可以取消注释以下行来运行演示
    # main()
    # demo_streaming()
    
    print("\n🎉 示例代码准备就绪！")
