"""作业失败分析集成测试

测试JobAnalyzer与其他组件的集成
"""

import pytest
from unittest.mock import patch
from api_proxy.job_failure_analysis import <PERSON><PERSON><PERSON>yzer
from api_proxy.proxy_service import ProxyService
from api_proxy.config import Config


class TestJobAnalyzerIntegration:
    """作业分析器集成测试类"""

    def test_with_proxy_service(self):
        """测试与代理服务集成"""
        config = Config({})
        service = ProxyService(config)

        log = """Running tests...
FAILED test.py - AssertionError
ERROR: ModuleNotFoundError: missing_pkg
would reformat file.py"""

        results = service.analyze_job_failure(log)
        assert len(results) >= 3
        assert any(r.error_type.name == "TEST_FAILURE" for r in results)
        assert any(r.error_type.name == "DEPENDENCY_ERROR" for r in results)
        assert any(r.error_type.name == "LINT_ERROR" for r in results)

    @patch('api_proxy.log_source.get_job_log')
    def test_with_external_log_source(self, mock_get_log):
        """测试与外部日志源集成"""
        mock_get_log.return_value = "FAILED test.py"

        analyzer = JobAnalyzer()
        results = analyzer.analyze_log(mock_get_log())

        assert len(results) == 1
        mock_get_log.assert_called_once()

    def test_error_priority_in_proxy_service(self):
        """测试在代理服务中的错误优先级"""
        config = Config({})
        service = ProxyService(config)

        log = "FAILED test.py\nOutOfMemoryError"
        results = service.analyze_job_failure(log)

        # 内存错误应该比测试失败优先级更高
        assert results[0].error_type.name == "MEMORY_ERROR"
