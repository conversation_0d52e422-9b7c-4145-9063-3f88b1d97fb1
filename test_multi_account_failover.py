#!/usr/bin/env python3
"""
测试多账户负载均衡和故障转移功能
"""

import requests
import json
import time

def test_multi_account_failover():
    """测试多账户故障转移"""
    print("🧪 测试多账户负载均衡和故障转移...")
    
    # 测试数据
    test_data = {
        "model": "deepseek/deepseek-chat-v3-0324:free",
        "messages": [{"role": "user", "content": "测试多账户负载均衡"}],
        "max_tokens": 20,
        "stream": False
    }
    
    headers = {
        "Content-Type": "application/json",
        "X-Provider": "openrouter"
    }
    
    print("📡 发送多个请求测试负载均衡...")
    
    success_count = 0
    total_requests = 5
    
    for i in range(total_requests):
        print(f"\n--- 请求 {i+1}/{total_requests} ---")
        
        try:
            start_time = time.time()
            response = requests.post(
                "http://localhost:8000/v1/chat/completions",
                headers=headers,
                json=test_data,
                timeout=30
            )
            end_time = time.time()
            
            duration = end_time - start_time
            print(f"⏱️  请求耗时: {duration:.2f}秒")
            print(f"📊 状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 请求{i+1}成功")
                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0].get('message', {}).get('content', '')
                    print(f"📝 响应内容: {content[:50]}...")
                success_count += 1
            else:
                print(f"❌ 请求{i+1}失败: {response.status_code}")
                print(f"   错误信息: {response.text[:200]}...")
                
        except Exception as e:
            print(f"❌ 请求{i+1}异常: {e}")
        
        # 请求间隔
        if i < total_requests - 1:
            time.sleep(2)
    
    print(f"\n📊 测试结果: {success_count}/{total_requests} 个请求成功")
    return success_count

def test_rate_limit_scenario():
    """模拟速率限制场景"""
    print("\n🧪 模拟速率限制场景...")
    
    # 快速发送多个请求来触发速率限制
    test_data = {
        "model": "deepseek/deepseek-chat-v3-0324:free",
        "messages": [{"role": "user", "content": "快速请求测试"}],
        "max_tokens": 10,
        "stream": False
    }
    
    headers = {
        "Content-Type": "application/json",
        "X-Provider": "openrouter"
    }
    
    print("📡 快速发送多个请求...")
    
    for i in range(3):
        print(f"\n--- 快速请求 {i+1}/3 ---")
        
        try:
            response = requests.post(
                "http://localhost:8000/v1/chat/completions",
                headers=headers,
                json=test_data,
                timeout=20
            )
            
            print(f"📊 状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"✅ 快速请求{i+1}成功")
            else:
                print(f"❌ 快速请求{i+1}失败: {response.status_code}")
                print(f"   错误信息: {response.text[:200]}...")
                
        except Exception as e:
            print(f"❌ 快速请求{i+1}异常: {e}")
        
        # 很短的间隔
        time.sleep(0.5)

def test_invalid_model_scenario():
    """测试无效模型场景"""
    print("\n🧪 测试无效模型场景...")
    
    test_data = {
        "model": "invalid/model-name-that-does-not-exist",
        "messages": [{"role": "user", "content": "测试无效模型"}],
        "max_tokens": 10,
        "stream": False
    }
    
    headers = {
        "Content-Type": "application/json",
        "X-Provider": "openrouter"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            headers=headers,
            json=test_data,
            timeout=20
        )
        
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            print(f"✅ 无效模型请求意外成功")
        else:
            print(f"❌ 无效模型请求失败（预期）: {response.status_code}")
            print(f"   错误信息: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ 无效模型请求异常: {e}")

def check_service_health():
    """检查服务健康状态"""
    try:
        response = requests.get("http://localhost:8000/api/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def main():
    """主测试函数"""
    print("🔍 多账户负载均衡和故障转移测试")
    print("=" * 50)
    
    # 检查服务状态
    if not check_service_health():
        print("❌ 服务未运行或不健康")
        print("💡 请先启动服务: python start_api_service.py")
        print("💡 并确保配置了多个OpenRouter账户")
        return
    
    print("✅ 服务运行正常")
    print("\n📋 测试说明:")
    print("1. 多账户负载均衡 - 验证请求在多个账户间分配")
    print("2. 速率限制故障转移 - 验证429错误时自动切换账户")
    print("3. 无效模型处理 - 验证错误处理机制")
    print("\n💡 请观察服务端日志中的详细信息:")
    print("   - 🔄 提供商切换日志")
    print("   - 🚫 错误检测和重试决策")
    print("   - ✅ 成功的故障转移")
    
    # 等待用户确认
    input("\n按回车键开始测试...")
    
    # 测试多账户负载均衡
    success_count = test_multi_account_failover()
    
    # 测试速率限制场景
    test_rate_limit_scenario()
    
    # 测试无效模型场景
    test_invalid_model_scenario()
    
    print("\n" + "=" * 50)
    print("📊 测试总结:")
    print(f"   基本负载均衡: {success_count}/5 成功")
    print("\n🎯 多账户负载均衡的优势:")
    print("✅ 自动故障转移 - 429错误时切换账户")
    print("✅ 负载分散 - 请求分布到多个账户")
    print("✅ 提高可用性 - 单个账户失效不影响服务")
    print("✅ 智能重试 - 根据错误类型决定是否重试")
    print("\n💡 配置建议:")
    print("1. 在config.json中配置多个OpenRouter账户")
    print("2. 使用不同的API密钥避免共享限制")
    print("3. 设置合理的超时时间")
    print("4. 监控各账户的使用情况")
    
    if success_count >= 3:
        print("\n🎉 多账户负载均衡工作正常！")
    else:
        print("\n⚠️  可能需要检查账户配置或网络连接")

if __name__ == "__main__":
    main()
