# 热重载功能测试
import pytest
import json
import tempfile
import os
import time
import threading
from unittest.mock import Mock, patch
from pathlib import Path

from api_proxy.hot_reload import <PERSON><PERSON>eloadManager, ServiceReloader, ConfigFileHandler
from api_proxy.config import Config
from api_proxy.models import ProviderConfig
from api_proxy.proxy_service import ProxyService


class TestConfigFileHandler:
    """配置文件处理器测试"""

    def test_config_file_handler_creation(self):
        """测试配置文件处理器创建"""
        callback = Mock()
        handler = ConfigFileHandler("test_config.json", callback)
        
        assert handler.config_path.name == "test_config.json"
        assert handler.reload_callback == callback
        assert handler.reload_debounce == 2

    def test_reload_config_success(self):
        """测试配置重载成功"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config_data = {
                "providers": {
                    "openai": [
                        {
                            "name": "test",
                            "api_key": "sk-test123",
                            "config": {}
                        }
                    ]
                },
                "default_timeout": 30,
                "max_retries": 3,
                "health_check_interval": 300,
                "enable_monitoring": True,
                "log_level": "INFO"
            }
            json.dump(config_data, f)
            config_file = f.name

        try:
            callback = Mock()
            handler = ConfigFileHandler(config_file, callback)
            
            # 触发重载
            handler._reload_config()
            
            # 验证回调被调用
            callback.assert_called_once()
            
            # 验证传递的配置对象
            called_config = callback.call_args[0][0]
            assert isinstance(called_config, Config)
            assert "openai" in called_config.providers
            
        finally:
            os.unlink(config_file)

    def test_reload_config_invalid_json(self):
        """测试无效JSON配置重载"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            f.write("invalid json content")
            config_file = f.name

        try:
            callback = Mock()
            handler = ConfigFileHandler(config_file, callback)
            
            # 触发重载应该不会崩溃
            handler._reload_config()
            
            # 回调不应该被调用
            callback.assert_not_called()
            
        finally:
            os.unlink(config_file)


class TestHotReloadManager:
    """热重载管理器测试"""

    def test_hot_reload_manager_creation(self):
        """测试热重载管理器创建"""
        manager = HotReloadManager("test_config.json")
        
        assert manager.config_path == "test_config.json"
        assert not manager.is_monitoring
        assert manager.observer is None
        assert len(manager.reload_callbacks) == 0

    def test_add_remove_callback(self):
        """测试添加和移除回调"""
        manager = HotReloadManager()
        callback1 = Mock()
        callback2 = Mock()
        
        # 添加回调
        manager.add_reload_callback(callback1)
        manager.add_reload_callback(callback2)
        assert len(manager.reload_callbacks) == 2
        
        # 移除回调
        manager.remove_reload_callback(callback1)
        assert len(manager.reload_callbacks) == 1
        assert callback2 in manager.reload_callbacks

    def test_manual_reload_success(self):
        """测试手动重载成功"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config_data = {
                "providers": {
                    "openai": [{"name": "test", "api_key": "sk-test", "config": {}}]
                },
                "default_timeout": 30,
                "max_retries": 3,
                "health_check_interval": 300,
                "enable_monitoring": True,
                "log_level": "INFO"
            }
            json.dump(config_data, f)
            config_file = f.name

        try:
            manager = HotReloadManager(config_file)
            callback = Mock()
            manager.add_reload_callback(callback)
            
            # 手动重载
            result = manager.manual_reload()
            
            assert result is True
            callback.assert_called_once()
            
        finally:
            os.unlink(config_file)

    def test_manual_reload_file_not_exists(self):
        """测试手动重载文件不存在"""
        manager = HotReloadManager("nonexistent_config.json")
        
        result = manager.manual_reload()
        assert result is False

    def test_get_status(self):
        """测试获取状态"""
        manager = HotReloadManager("test_config.json")
        callback = Mock()
        manager.add_reload_callback(callback)
        
        status = manager.get_status()
        
        assert status["monitoring"] is False
        assert status["config_path"] == "test_config.json"
        assert status["config_exists"] is False
        assert status["callbacks_count"] == 1

    def test_context_manager(self):
        """测试上下文管理器"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = os.path.join(temp_dir, "config.json")
            
            # 创建配置文件
            with open(config_file, 'w') as f:
                json.dump({"providers": {}}, f)
            
            manager = HotReloadManager(config_file)
            
            # 使用上下文管理器
            with manager:
                assert manager.is_monitoring
            
            assert not manager.is_monitoring


class TestServiceReloader:
    """服务重载器测试"""

    def test_service_reloader_creation(self):
        """测试服务重载器创建"""
        factory = Mock()
        reloader = ServiceReloader(factory)
        
        assert reloader.service_factory == factory
        assert reloader.current_service is None
        assert len(reloader.reload_history) == 0

    def test_reload_service_success(self):
        """测试服务重载成功"""
        # 创建模拟服务工厂
        mock_service = Mock()
        factory = Mock(return_value=mock_service)
        
        reloader = ServiceReloader(factory)
        
        # 创建配置
        config = Config({
            "openai": [ProviderConfig("test", "sk-test", {})]
        })
        
        # 重载服务
        reloader.reload_service(config)
        
        # 验证
        factory.assert_called_once_with(config)
        assert reloader.current_service == mock_service
        assert len(reloader.reload_history) == 1
        assert reloader.reload_history[0]["success"] is True

    def test_reload_service_failure(self):
        """测试服务重载失败"""
        # 创建会抛出异常的工厂
        factory = Mock(side_effect=Exception("Service creation failed"))
        
        reloader = ServiceReloader(factory)
        
        config = Config({
            "openai": [ProviderConfig("test", "sk-test", {})]
        })
        
        # 重载应该抛出异常
        with pytest.raises(Exception, match="Service creation failed"):
            reloader.reload_service(config)
        
        # 验证历史记录
        assert len(reloader.reload_history) == 1
        assert reloader.reload_history[0]["success"] is False
        assert "Service creation failed" in reloader.reload_history[0]["error"]

    def test_get_service(self):
        """测试获取服务"""
        mock_service = Mock()
        factory = Mock(return_value=mock_service)
        
        reloader = ServiceReloader(factory)
        
        # 初始状态
        assert reloader.get_service() is None
        
        # 重载后
        config = Config({"openai": [ProviderConfig("test", "sk-test", {})]})
        reloader.reload_service(config)
        
        assert reloader.get_service() == mock_service

    def test_get_reload_history(self):
        """测试获取重载历史"""
        factory = Mock(return_value=Mock())
        reloader = ServiceReloader(factory)
        
        config = Config({"openai": [ProviderConfig("test", "sk-test", {})]})
        
        # 执行多次重载
        for i in range(12):  # 超过10次
            reloader.reload_service(config)
        
        history = reloader.get_reload_history()
        
        # 应该只返回最近10次
        assert len(history) == 10
        assert all(record["success"] for record in history)


class TestIntegration:
    """集成测试"""

    def test_hot_reload_integration(self):
        """测试热重载集成"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            initial_config = {
                "providers": {
                    "openai": [{"name": "initial", "api_key": "sk-initial", "config": {}}]
                },
                "default_timeout": 30,
                "max_retries": 3,
                "health_check_interval": 300,
                "enable_monitoring": True,
                "log_level": "INFO"
            }
            json.dump(initial_config, f)
            config_file = f.name

        try:
            # 创建服务重载器
            service_reloader = ServiceReloader(ProxyService)
            
            # 创建热重载管理器
            hot_reload_manager = HotReloadManager(config_file)
            hot_reload_manager.add_reload_callback(service_reloader.reload_service)
            
            # 手动触发重载
            success = hot_reload_manager.manual_reload()
            assert success
            
            # 验证服务被创建
            service = service_reloader.get_service()
            assert service is not None
            assert isinstance(service, ProxyService)
            assert "openai" in service.providers
            
            # 验证重载历史
            history = service_reloader.get_reload_history()
            assert len(history) == 1
            assert history[0]["success"]
            
        finally:
            os.unlink(config_file)
