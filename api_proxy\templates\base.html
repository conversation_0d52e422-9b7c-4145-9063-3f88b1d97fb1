<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>{% block title %}AI代理服务管理界面{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            border-radius: 8px;
            margin: 2px 0;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-2px);
        }

        /* 页面切换动画 */
        .page-transition {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.3s ease-in-out;
        }

        .page-transition.loaded {
            opacity: 1;
            transform: translateY(0);
        }

        /* 导航链接悬停效果 */
        .sidebar .nav-link {
            position: relative;
            overflow: hidden;
        }

        .sidebar .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .sidebar .nav-link:hover::before {
            left: 100%;
        }
        .status-badge {
            font-size: 0.8em;
            padding: 4px 8px;
            border-radius: 20px;
        }
        .status-running {
            background-color: #d4edda;
            color: #155724;
        }
        .status-stopped {
            background-color: #f8d7da;
            color: #721c24;
        }

        /* 指标卡片样式 */
        .metric-card {
            border-left: 4px solid #007bff;
        }
        .metric-card-blue {
            border-left: 4px solid #007bff;
        }
        .metric-card-green {
            border-left: 4px solid #28a745;
        }
        .metric-card-orange {
            border-left: 4px solid #fd7e14;
        }
        .metric-card-purple {
            border-left: 4px solid #6f42c1;
            color: #721c24;
        }
        .metric-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        .metric-card-blue {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        .metric-card-green {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
        }
        .metric-card-orange {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-robot"></i>
                            AI代理服务
                        </h4>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" data-page="dashboard">
                                <i class="fas fa-tachometer-alt"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-page="config">
                                <i class="fas fa-cog"></i>
                                配置管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-page="api-info">
                                <i class="fas fa-info-circle"></i>
                                API信息
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-page="auth">
                                <i class="fas fa-key"></i>
                                认证管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-action="showLogs">
                                <i class="fas fa-file-alt"></i>
                                日志查看
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-action="testAPI">
                                <i class="fas fa-flask"></i>
                                API测试
                            </a>
                        </li>
                    </ul>
                    
                    <hr class="text-white-50">
                    
                    <div class="text-center">
                        <small class="text-white-50">
                            版本 1.0.0<br>
                            <i class="fas fa-heart text-danger"></i> 
                            Made with Love
                        </small>
                    </div>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2" id="pageTitle">仪表板</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" data-action="refreshCurrentPage">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 动态内容容器 -->
                <div id="pageContent">
                    {% block content %}
                    <!-- 默认显示仪表板内容 -->
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2 text-muted">正在加载仪表板...</p>
                    </div>
                    {% endblock %}
                </div>

                <!-- 加载指示器 -->
                <div id="loadingIndicator" class="text-center py-5" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2 text-muted">正在加载...</p>
                </div>
            </main>
        </div>
    </div>

    <!-- 模态框 -->
    <div class="modal fade" id="logModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">系统日志</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <pre id="logContent" style="max-height: 400px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 8px;"></pre>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="testModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">API测试</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="testForm">
                        <div class="mb-3">
                            <label class="form-label">提供商类型</label>
                            <select class="form-select" name="provider_type">
                                <option value="openai">OpenAI</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">API端点</label>
                            <input type="text" class="form-control" name="endpoint" value="chat/completions" placeholder="例如: chat/completions">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" data-action="runTest">测试</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- 主应用JavaScript -->
    <script src="/static/app.js"></script>

    {% block scripts %}{% endblock %}
</body>
</html>
