#!/usr/bin/env python3
"""
测试流式响应功能
"""

import requests
import json
import time

def test_non_stream():
    """测试非流式响应"""
    print("🧪 测试非流式响应...")
    
    test_data = {
        "model": "deepseek/deepseek-chat-v3-0324:free",
        "messages": [
            {"role": "user", "content": "Hello! Please respond with just 'Hi!'"}
        ],
        "max_tokens": 10,
        "temperature": 0.1,
        "stream": False  # 明确设置为非流式
    }
    
    headers = {
        "Content-Type": "application/json",
        "X-Provider": "openrouter"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            headers=headers,
            json=test_data,
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        print(f"Content-Type: {response.headers.get('Content-Type')}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            print("✅ 非流式请求成功")
            return True
        else:
            print(f"错误: {response.text}")
            print("❌ 非流式请求失败")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_stream():
    """测试流式响应"""
    print("\n🧪 测试流式响应...")
    
    test_data = {
        "model": "deepseek/deepseek-chat-v3-0324:free",
        "messages": [
            {"role": "user", "content": "请说一句简短的问候语"}
        ],
        "max_tokens": 50,
        "temperature": 0.7,
        "stream": True  # 启用流式响应
    }
    
    headers = {
        "Content-Type": "application/json",
        "X-Provider": "openrouter"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            headers=headers,
            json=test_data,
            timeout=30,
            stream=True  # 启用流式接收
        )
        
        print(f"状态码: {response.status_code}")
        print(f"Content-Type: {response.headers.get('Content-Type')}")
        
        if response.status_code == 200:
            print("📡 开始接收流式数据...")
            chunk_count = 0
            
            for line in response.iter_lines(decode_unicode=True):
                if line:
                    chunk_count += 1
                    print(f"Chunk {chunk_count}: {line}")
                    
                    # 限制显示的chunk数量
                    if chunk_count >= 10:
                        print("... (更多数据)")
                        break
            
            print("✅ 流式请求成功")
            return True
        else:
            print(f"错误: {response.text}")
            print("❌ 流式请求失败")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_stream_with_curl():
    """显示curl测试命令"""
    print("\n💡 你也可以用curl测试流式响应:")
    print("=" * 60)
    
    curl_cmd = '''curl -X POST http://localhost:8000/v1/chat/completions \\
  -H "Content-Type: application/json" \\
  -H "X-Provider: openrouter" \\
  -d '{
    "model": "deepseek/deepseek-chat-v3-0324:free",
    "messages": [{"role": "user", "content": "Hello!"}],
    "max_tokens": 20,
    "stream": true
  }' \\
  --no-buffer'''
    
    print(curl_cmd)
    print("=" * 60)

if __name__ == "__main__":
    print("🔍 流式响应测试")
    print("=" * 50)
    print("请确保服务正在运行: python start_api_service.py")
    print("=" * 50)
    
    # 等待用户确认
    input("按回车键开始测试...")
    
    # 测试非流式响应
    non_stream_success = test_non_stream()
    
    # 等待一下
    time.sleep(2)
    
    # 测试流式响应
    stream_success = test_stream()
    
    # 显示curl命令
    test_stream_with_curl()
    
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"  非流式响应: {'✅ 成功' if non_stream_success else '❌ 失败'}")
    print(f"  流式响应: {'✅ 成功' if stream_success else '❌ 失败'}")
    
    if non_stream_success and stream_success:
        print("\n🎉 所有测试通过！流式响应功能正常工作")
    else:
        print("\n⚠️  部分测试失败，请检查日志获取详细信息")
